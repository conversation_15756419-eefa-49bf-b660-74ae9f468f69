module AD5383_CTRL(
    input wire clk_40mhz,         // 40MHz系统时钟
    input wire reset_n,           // 低电平有效复位信号
    
    // AD5383接口信号
    output wire FIFO_EN,
    output wire CLR_N,
    output wire AD5383_RESET_N,
    output wire PD,
    output wire PAR_N,
    output wire A_N_B,
    output wire REG0,
    output wire REG1,
    output wire CS_N,
    output wire WR_N,
    output wire [4:0] ADDRESS,
    output wire [11:0] DB
);

    // 定义阶梯状电平值
    // 将12位DAC范围(0-4095)均匀分为6个等级
    wire [11:0] DAC_STEP1 = 12'h195; // 10%  (405)
    wire [11:0] DAC_STEP2 = 12'h333; // 20%  (819)
    wire [11:0] DAC_STEP3 = 12'h666; // 40%  (1638)
    wire [11:0] DAC_STEP4 = 12'h999; // 60% (2457)
    wire [11:0] DAC_STEP5 = 12'hCCC; // 80% (3276)
    wire [11:0] DAC_STEP6 = 12'hFFF; // 100%  (4095)

    // 实例化AD5383驱动模块
    AD5383_Driver AD5383_inst(
        .CLK(clk_40mhz),          // 40MHz系统时钟
        .RESET_N(reset_n),         // 复位信号，低电平有效
        .ENA_AD5383(1'b1),         // 始终使能AD5383
        
        // 各通道设置阶梯状固定值
        // 通道0-5设置为第一级电平
        .DA0(DAC_STEP1),
        .DA1(DAC_STEP1),
        .DA2(DAC_STEP1),
        .DA3(DAC_STEP1),
        .DA4(DAC_STEP1),
        
        // 通道5-9设置为第二级电平
        .DA5(DAC_STEP2),
        .DA6(DAC_STEP2),
        .DA7(DAC_STEP2),
        .DA8(DAC_STEP2),
        .DA9(DAC_STEP2),
        
        // 通道10-14设置为第三级电平
        .DA10(DAC_STEP3),
        .DA11(DAC_STEP3),
        .DA12(DAC_STEP3),
        .DA13(DAC_STEP3),
        .DA14(DAC_STEP3),
        
        // 通道15-19设置为第四级电平
        .DA15(DAC_STEP4),
        .DA16(DAC_STEP4),
        .DA17(DAC_STEP4),
        .DA18(DAC_STEP4),
        .DA19(DAC_STEP4),
        
        // 通道20-24设置为第五级电平
        .DA20(DAC_STEP5),
        .DA21(DAC_STEP5),
        .DA22(DAC_STEP5),
        .DA23(DAC_STEP5),
        .DA24(DAC_STEP5),
        
        // 通道25-31设置为第六级电平
        .DA25(DAC_STEP6),
        .DA26(DAC_STEP6),
        .DA27(DAC_STEP6),
        .DA28(DAC_STEP6),
        .DA29(DAC_STEP6),
        .DA30(DAC_STEP6),
        .DA31(DAC_STEP6),
        
        // AD5383控制信号
        .FIFO_EN(FIFO_EN),
        .CLR_N(CLR_N),
        .AD5383_RESET_N(AD5383_RESET_N),
        .PD(PD),
        .PAR_N(PAR_N),
        .A_N_B(A_N_B),
        .REG0(REG0),
        .REG1(REG1),
        .CS_N(CS_N),
        .WR_N(WR_N),
        .ADDRESS(ADDRESS),
        .DB(DB)
    );

endmodule