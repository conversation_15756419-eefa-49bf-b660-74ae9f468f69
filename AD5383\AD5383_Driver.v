module AD5383_Driver(
    input wire CLK,             // 系统时钟 40MHz
    input wire RESET_N,         // 复位信号（低电平有效）
    input wire ENA_AD5383,      // AD5383使能信号
    
    // DAC数据输入接口
    input wire [11:0] DA0,
    input wire [11:0] DA1,
    input wire [11:0] DA2,
    input wire [11:0] DA3,
    input wire [11:0] DA4,
    input wire [11:0] DA5,
    input wire [11:0] DA6,
    input wire [11:0] DA7,
    input wire [11:0] DA8,
    input wire [11:0] DA9,
    input wire [11:0] DA10,
    input wire [11:0] DA11,
    input wire [11:0] DA12,
    input wire [11:0] DA13,
    input wire [11:0] DA14,
    input wire [11:0] DA15,
    input wire [11:0] DA16,
    input wire [11:0] DA17,
    input wire [11:0] DA18,
    input wire [11:0] DA19,
    input wire [11:0] DA20,
    input wire [11:0] DA21,
    input wire [11:0] DA22,
    input wire [11:0] DA23,
    input wire [11:0] DA24,
    input wire [11:0] DA25,
    input wire [11:0] DA26,
    input wire [11:0] DA27,
    input wire [11:0] DA28,
    input wire [11:0] DA29,
    input wire [11:0] DA30,
    input wire [11:0] DA31,
    
    output reg FIFO_EN,          // FIFO使能信号
    output reg CLR_N,            // 清除信号（低电平有效）
    output reg AD5383_RESET_N,   // DAC复位信号（低电平有效）
    output reg PD,               // 功耗控制信号
    output reg PAR_N,            // 串并转换信号
    output reg A_N_B,            // 寄存器选择信号
    output reg REG0,             // 寄存器1选择信号
    output reg REG1,             // 寄存器0选择信号

    // DAC控制信号
    output reg CS_N,           // 片选信号（低电平有效）
    output reg WR_N,           // 写入信号（低电平有效）
    
    // DAC地址和数据
    output reg [4:0] ADDRESS,   // 地址输出
    output reg [11:0] DB        // 数据输出
);

    // 内部信号定义
    reg [3:0] div_cnt;         // 分频计数器
    reg clk_div16;             // 分频后的时钟脉冲（每16个周期一个脉冲）
    reg [6:0] cnt;             // 主状态计数器（0-127）
    reg stop_cnt;              // 停止计数器
    
    // 时钟分频器实现（每16个时钟周期产生一个脉冲,2.5MHz）
    always @(posedge CLK or negedge RESET_N) begin
        if (!RESET_N) begin
            div_cnt <= 4'b0000;
            clk_div16 <= 1'b0;
        end else begin
            if (div_cnt == 4'b1111) begin
                clk_div16 <= 1'b1;
                div_cnt <= 4'b0000;
            end else begin
                clk_div16 <= 1'b0;
                div_cnt <= div_cnt + 1'b1;
            end
        end
    end
    
    // 主状态计数器控制
    always @(posedge CLK or negedge RESET_N) begin
        if (!RESET_N) begin
            cnt <= 7'd0;
            stop_cnt <= 1'b0;
        end 
		  else begin 
		     if(!ENA_AD5383)begin
			    cnt <= 7'd0;
             stop_cnt <= 1'b0;
			  end
		    else if (clk_div16) begin
            if (cnt >= 7'd127) begin
                cnt <= 7'd0;
                stop_cnt <= 1'b1;
            end else begin
                cnt <= cnt + 1'b1;
            end
        end
		  end
    end
    
    // 初始化配置
    always @(posedge CLK or negedge RESET_N) begin
        if (!RESET_N) begin
            FIFO_EN <= 1'b1;        // 启用FIFO
            CLR_N <= 1'b1;          // 清除信号（非激活状态）
            AD5383_RESET_N <= 1'b1; // 复位信号（非激活状态）
            PD <= 1'b0;             // 掉电控制（正常工作状态）
            PAR_N <= 1'b0;          // 串并行选择 (并行模式)
            A_N_B <= 1'b0;          // 数据寄存器选择（默认状态）
        end
        else if (!ENA_AD5383)begin
            FIFO_EN <= 1'b1;        // 启用FIFO
            CLR_N <= 1'b1;          // 清除信号（非激活状态）
            AD5383_RESET_N <= 1'b1; // 复位信号（非激活状态）
            PD <= 1'b0;             // 掉电控制（正常工作状态）
            PAR_N <= 1'b0;          // 串并行选择 (并行模式)
            A_N_B <= 1'b0;          // 数据寄存器选择（默认状态）
    end
    end
    //主状态机
    always @(posedge CLK or negedge RESET_N) begin
        if (!RESET_N ) 
        begin CS_N <= 1'b1;WR_N <= 1'b1;ADDRESS <= 5'b01100;DB <= 12'b010001000000;REG1<=1'b0;REG0<=1'b0;end //初始化
        else if (!ENA_AD5383 || stop_cnt)begin
            begin CS_N <= 1'b1;WR_N <= 1'b1;ADDRESS <= 5'b01100;DB <= 12'b010001000000;REG1<=1'b0;REG0<=1'b0;end //初始化
        end
        else begin
        case(cnt)
            0: begin CS_N <= 1'b1;WR_N <= 1'b1;ADDRESS <= 5'b01100;DB <= 12'b010001000000;REG1<=1'b0;REG0<=1'b0;end
            1: begin CS_N <= 1'b0;WR_N <= 1'b0;end
            2: begin CS_N <= 1'b1;WR_N <= 1'b1;end
            3: begin CS_N <= 1'b1;WR_N <= 1'b1;end
            4: begin CS_N <= 1'b1;WR_N <= 1'b1;end
            5: begin CS_N <= 1'b1;WR_N <= 1'b1;end
            6: begin CS_N <= 1'b1;WR_N <= 1'b1;end  
            7: begin CS_N <= 1'b1;WR_N <= 1'b1;end
            8: begin CS_N <= 1'b1;WR_N <= 1'b1;end
            9: begin CS_N <= 1'b1;WR_N <= 1'b1;end

            10:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b00000;DB<=DA0;REG1<=1'b1;REG0<=1'b1;end //ch0
            11:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            12:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            13:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b00001;DB<=DA1;REG1<=1'b1;REG0<=1'b1;end //ch1
            14:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            15:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            16:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b00010;DB<=DA2;REG1<=1'b1;REG0<=1'b1;end //ch2
            17:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            18:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            19:begin CS_N <= 1'b0;WR_N <= 1'b1;end

            20:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b00011;DB<=DA3;REG1<=1'b1;REG0<=1'b1;end //ch3
            21:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            22:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            23:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b00100;DB<=DA4;REG1<=1'b1;REG0<=1'b1;end //ch4
            24:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            25:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            26:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b00101;DB<=DA5;REG1<=1'b1;REG0<=1'b1;end //ch5
            27:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            28:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            29:begin CS_N <= 1'b0;WR_N <= 1'b1;end

            30:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b00110;DB<=DA6;REG1<=1'b1;REG0<=1'b1;end //ch6
            31:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            32:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            33:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b00111;DB<=DA7;REG1<=1'b1;REG0<=1'b1;end //ch7
            34:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            35:begin CS_N <= 1'b0;WR_N <= 1'b1;end      
            36:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b01000;DB<=DA8;REG1<=1'b1;REG0<=1'b1;end //ch8
            37:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            38:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            39:begin CS_N <= 1'b0;WR_N <= 1'b1;end

            40:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b01001;DB<=DA9;REG1<=1'b1;REG0<=1'b1;end //ch9
            41:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            42:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            43:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b01010;DB<=DA10;REG1<=1'b1;REG0<=1'b1;end //ch10
            44:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            45:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            46:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b01011;DB<=DA11;REG1<=1'b1;REG0<=1'b1;end //ch11
            47:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            48:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            49:begin CS_N <= 1'b0;WR_N <= 1'b1;end

            50:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b01100;DB<=DA12;REG1<=1'b1;REG0<=1'b1;end //ch12
            51:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            52:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            53:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b01101;DB<=DA13;REG1<=1'b1;REG0<=1'b1;end //ch13
            54:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            55:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            56:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b01110;DB<=DA14;REG1<=1'b1;REG0<=1'b1;end //ch14
            57:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            58:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            59:begin CS_N <= 1'b0;WR_N <= 1'b1;end

            60:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b01111;DB<=DA15;REG1<=1'b1;REG0<=1'b1;end //ch15
            61:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            62:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            63:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b10000;DB<=DA16;REG1<=1'b1;REG0<=1'b1;end //ch16
            64:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            65:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            66:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b10001;DB<=DA17;REG1<=1'b1;REG0<=1'b1;end //ch17
            67:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            68:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            69:begin CS_N <= 1'b0;WR_N <= 1'b1;end

            70:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b10010;DB<=DA18;REG1<=1'b1;REG0<=1'b1;end //ch18
            71:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            72:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            73:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b10011;DB<=DA19;REG1<=1'b1;REG0<=1'b1;end //ch19
            74:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            75:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            76:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b10100;DB<=DA20;REG1<=1'b1;REG0<=1'b1;end //ch20
            77:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            78:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            79:begin CS_N <= 1'b0;WR_N <= 1'b1;end

            80:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b10101;DB<=DA21;REG1<=1'b1;REG0<=1'b1;end //ch21
            81:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            82:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            83:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b10110;DB<=DA22;REG1<=1'b1;REG0<=1'b1;end //ch22
            84:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            85:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            86:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b10111;DB<=DA23;REG1<=1'b1;REG0<=1'b1;end //ch23
            87:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            88:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            89:begin CS_N <= 1'b0;WR_N <= 1'b1;end

            90:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b11000;DB<=DA24;REG1<=1'b1;REG0<=1'b1;end //ch24
            91:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            92:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            93:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b11001;DB<=DA25;REG1<=1'b1;REG0<=1'b1;end //ch25
            94:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            95:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            96:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b11010;DB<=DA26;REG1<=1'b1;REG0<=1'b1;end //ch26
            97:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            98:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            99:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            
            100:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b11011;DB<=DA27;REG1<=1'b1;REG0<=1'b1;end //ch27
            101:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            102:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            103:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b11100;DB<=DA28;REG1<=1'b1;REG0<=1'b1;end //ch28
            104:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            105:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            106:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b11101;DB<=DA29;REG1<=1'b1;REG0<=1'b1;end //ch29
            107:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            108:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            109:begin CS_N <= 1'b0;WR_N <= 1'b1;end

            110:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b11110;DB<=DA30;REG1<=1'b1;REG0<=1'b1;end //ch30
            111:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            112:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            113:begin CS_N <= 1'b0;WR_N <= 1'b1;ADDRESS <= 5'b11111;DB<=DA31;REG1<=1'b1;REG0<=1'b1;end //ch31
            114:begin CS_N <= 1'b0;WR_N <= 1'b0;end
            115:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            116:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            117:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            118:begin CS_N <= 1'b0;WR_N <= 1'b1;end 
            119:begin CS_N <= 1'b0;WR_N <= 1'b1;end
            
            default:begin CS_N <= 1'b1;WR_N <= 1'b1;ADDRESS <= 5'b01100;DB <= 12'b010001000000;REG1<=1'b0;REG0<=1'b0;end
        endcase
        end
    end
endmodule
