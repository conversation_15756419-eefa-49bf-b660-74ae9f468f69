`timescale 1ns/100ps

module tb_AD5383_Driver();
    // 时钟和复位信号
    reg CLK;
    reg RESET_N;
    reg ENA_AD5383;
    
    // DAC数据输入信号
    reg [11:0] DA0, DA1, DA2, DA3, DA4, DA5, DA6, DA7;
    reg [11:0] DA8, DA9, DA10, DA11, DA12, DA13, DA14, DA15;
    reg [11:0] DA16, DA17, DA18, DA19, DA20, DA21, DA22, DA23;
    reg [11:0] DA24, DA25, DA26, DA27, DA28, DA29, DA30, DA31;
    
    // 控制和状态信号
    wire FIFO_EN;
    wire CLR_N;
    wire AD5383_RESET_N;
    wire PD;
    wire PAR_N;
    wire A_N_B;
    wire REG0;
    wire REG1;
    
    // DAC接口信号
    wire CS_N;
    wire WR_N;
    wire [4:0] ADDRESS;
    wire [11:0] DB;
    
    // 实例化被测模块
    AD5383_Driver dut(
        .CLK(CLK),
        .RESET_N(RESET_N),
        .ENA_AD5383(ENA_AD5383),
        
        // DAC数据输入
        .DA0(DA0), .DA1(DA1), .DA2(DA2), .DA3(DA3),
        .DA4(DA4), .DA5(DA5), .DA6(DA6), .DA7(DA7),
        .DA8(DA8), .DA9(DA9), .DA10(DA10), .DA11(DA11),
        .DA12(DA12), .DA13(DA13), .DA14(DA14), .DA15(DA15),
        .DA16(DA16), .DA17(DA17), .DA18(DA18), .DA19(DA19),
        .DA20(DA20), .DA21(DA21), .DA22(DA22), .DA23(DA23),
        .DA24(DA24), .DA25(DA25), .DA26(DA26), .DA27(DA27),
        .DA28(DA28), .DA29(DA29), .DA30(DA30), .DA31(DA31),
        
        // 控制信号
        .FIFO_EN(FIFO_EN),
        .CLR_N(CLR_N),
        .AD5383_RESET_N(AD5383_RESET_N),
        .PD(PD),
        .PAR_N(PAR_N),
        .A_N_B(A_N_B),
        .REG0(REG0),
        .REG1(REG1),
        
        // DAC接口
        .CS_N(CS_N),
        .WR_N(WR_N),
        .ADDRESS(ADDRESS),
        .DB(DB)
    );
    
    // 时钟生成，40MHz
    initial begin
        CLK = 0;
        forever #12.5 CLK = ~CLK; // 25ns周期，40MHz
    end
    
    // 监视重要信号
    // initial begin
    //     $monitor("Time=%t, cnt=%d, CS_N=%b, WR_N=%b, ADDR=%h, DATA=%h", 
    //              $time, dut.cnt, CS_N, WR_N, ADDRESS, DB);
    // end
    
    // 测试过程
    initial begin
        // 初始化输入
        RESET_N = 0;
        ENA_AD5383 = 0;
        
        // 设置DAC通道值为递增模式
        DA0 = 12'h000; DA1 = 12'h111; DA2 = 12'h222; DA3 = 12'h333;
        DA4 = 12'h444; DA5 = 12'h555; DA6 = 12'h666; DA7 = 12'h777;
        DA8 = 12'h888; DA9 = 12'h999; DA10 = 12'hAAA; DA11 = 12'hBBB;
        DA12 = 12'hCCC; DA13 = 12'hDDD; DA14 = 12'hEEE; DA15 = 12'hFFF;
        DA16 = 12'h123; DA17 = 12'h234; DA18 = 12'h345; DA19 = 12'h456;
        DA20 = 12'h567; DA21 = 12'h678; DA22 = 12'h789; DA23 = 12'h89A;
        DA24 = 12'h9AB; DA25 = 12'hABC; DA26 = 12'hBCD; DA27 = 12'hCDE;
        DA28 = 12'hDEF; DA29 = 12'hEF0; DA30 = 12'hF01; DA31 = 12'h012;
        
        // 复位拉高，开始测试
        #100;
        RESET_N = 1;
        #100;
        
        // 启用AD5383
        ENA_AD5383 = 1;
        
        // 等待一个完整周期结束
        #60000; // 足够一个完整的DAC更新周期 (约51.2us)
             
        // 禁用模块，观察行为
        ENA_AD5383 = 0;
        #1000;
        
        // 再次启用
        ENA_AD5383 = 1;
        #60000;
        
        // 结束仿真
        $finish;
    end
    
endmodule