# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个卫星科学仪器系统的HDL代码库，使用Verilog HDL实现。该系统集成了多个科学仪器子系统，用于空间科学探测任务。

## 系统架构

### 顶层设计
- **top.v**: 系统顶层模块，集成所有科学仪器子系统
- 系统时钟：40MHz
- 通信协议：RS422异步串行通信，115200bps，奇校验
- 系统时间管理：提供秒和毫秒计时，PPS输出

### 核心子系统
1. **加速度计** (accelerometer/): 静电加速度计，支持PID控制、ADC/DAC偏置校准
2. **振动传感器** (vibration/): 振动监测子系统，支持详查/巡查/停止模式
3. **环境监测** (envmonitor/, envmonitor2/): 空间环境监测，包括粒子探测、温度、电压监测
4. **相机** (camera/): 卫星监视相机，支持三相机控制、压缩核管理、LVDS输出
5. **平台** (platform/): 平台科学数据管理
6. **GNSS** (GNSSA/, GNSSB/): 导航系统科学数据
7. **AD5383** (AD5383/): 32通道数模转换器控制
8. **UART驱动** (Uart_Driver/): 通用UART通信模块

### 通用架构模式
每个科学仪器子系统通常包含：
- **命令通道**: 接收和解析遥控指令
- **科学数据通道**: 发送科学探测数据
- **遥测通道**: 发送工程参数和状态信息
- **共享状态**: 管理工作模式和参数配置

## 开发指南

### 模块命名约定
- 顶层模块：`*_top.v`
- 功能通道：`*_channel.v`
- 共享状态：`*_shared_state.v`
- 测试平台：`tb_*.v`

### 信号命名规范
- 时钟：`clk`
- 复位：`rst_n` (低电平有效)
- UART接口：`rx`, `tx`
- 系统时间：`sys_time_s`, `sys_time_ms`
- 波特率配置：`baud_val`

### 通信协议
- 默认波特率：115200bps
- 波特率配置值：`8'd21` (40MHz/(115200*16))
- 数据格式：8位数据，奇校验

### 测试和验证
- 测试平台文件以`tb_`为前缀
- 主要测试文件：`tb_sci_data_only.v`
- 每个子系统都有独立的测试平台

## 关键参数

### 系统时钟
- 主时钟：40MHz
- 通过DCM模块进行时钟管理和复位同步

### 时间管理
- 系统时间：32位秒计数 + 16位毫秒计数
- PPS输出：每秒脉冲信号
- UTC时间支持：环境监测子系统支持UTC时间注入

### 控制接口
- AD5383 DAC：32通道，12位分辨率，阶梯状输出
- 工作模式控制：通过命令通道配置
- 阈值参数：支持多种探测阈值设置

## 开发注意事项

1. **时序约束**：所有模块基于40MHz时钟设计
2. **复位管理**：使用DCM的LOCKED信号作为系统复位
3. **通信可靠性**：包含错误计数和通信状态监控
4. **参数配置**：支持运行时参数更新和状态查询
5. **模块化设计**：各子系统相对独立，便于维护和测试