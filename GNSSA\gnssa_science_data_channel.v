module gnssa_science_data_channel (
    input wire clk,                // 系统时钟
    input wire rst_n,              // 复位信号，低电平有效
    input wire [7:0] baud_val,     // 波特率配置
    
    // 系统时间
    input wire [31:0] sys_time_s,  // 系统时间(秒)
    input wire [15:0] sys_time_ms, // 系统时间(毫秒)
    
    // 状态参数
    input wire [7:0] work_mode,    // 工作模式状态
     
    // UART输出
    output wire tx                 // RS422发送线
);

    // ================== 内部参数定义 ==================
    // 科学数据包定义，固定为96字节
    localparam SCIENCE_DATA_SIZE = 96;
    
    // 科学数据包字段位置定义
    localparam FRAME_HEAD_POS      = 0;  // 帧头位置(2字节)
    localparam DATA_LEN_POS        = 2;  // 数据长度位置(2字节)
    localparam SERVICE_TYPE_POS    = 4;  // 服务类型位置(1字节)
    localparam SERVICE_STATUS_POS  = 5;  // 服务状态位置(1字节)
    localparam FRAME_CNT_POS       = 6;  // 帧计数位置(3字节)
    localparam USEC_CNT_POS        = 9;  // 微秒计数位置(3字节)
    localparam SEC_CNT_POS         = 12; // 秒计数位置(4字节)
    localparam WORK_MODE_POS       = 16; // 工作模式位置(1字节)
    localparam TEMP_DATA_POS       = 17; // 温度数据起始位置(4*3=12字节)
    localparam POSITION_DATA_POS   = 29; // 位移数据起始位置(6*3=18字节)
    localparam ACCEL_DATA_POS      = 47; // 加速度数据起始位置(6*3=18字节)
    localparam CONTROL_DATA_POS    = 65; // 控制数据起始位置(6*3=18字节)
    localparam TIME_DIFF_POS       = 83; // 时钟差值位置(3字节)
    localparam RESERVED_POS        = 86; // 保留字节位置(6字节)
    localparam CHECKSUM_POS        = 92; // 校验码位置(2字节)
    localparam FRAME_TAIL_POS      = 94; // 帧尾位置(2字节)
    
    // 固定参数值
    localparam [15:0] FRAME_HEAD   = 16'hEB90; // 帧头
    localparam [15:0] DATA_LEN     = 16'h0058; // 数据长度(88)
    localparam [7:0] SERVICE_TYPE  = 8'h33;    // 服务类型
    localparam [7:0] SERVICE_STATUS = 8'h44;   // 服务状态
    localparam [15:0] FRAME_TAIL   = 16'h09D7; // 帧尾
    
    // UART通信参数
    reg cs_n;                      // 片选信号
    reg we_n;                      // 写使能信号
    reg [7:0] data_in;             // 待写数据
    wire txrdy;                    // 发送就绪标志
    reg  txrdy_prev;
    wire txrdy_rf;
    assign txrdy_rf = txrdy & ~txrdy_prev;
    
    // ================== 内部寄存器定义 ==================
    reg [23:0] frame_counter;      // 帧计数器
    reg [31:0] timer_counter;      // 定时器计数器(用于0.1s周期)
    reg [15:0] sine_counter;       // 正弦波计数器，用于生成周期性数据
    
    // 科学数据包缓冲区
    reg [7:0] science_data [0:SCIENCE_DATA_SIZE-1];
    reg [7:0] send_index;          // 发送索引
    
    // 数据生成相关临时变量
    reg [23:0] position_data [0:5]; // 6个通道的位移数据
    reg [23:0] accel_data [0:5];    // 6个通道的加速度数据
    reg [23:0] control_data [0:5];  // 6个通道的控制数据
    reg [23:0] temp_data [0:3];     // 4个温度数据
    
    // 内部生成的PID参数，不依赖外部输入
    reg [31:0] pid_params [0:5];   // 6个PID参数（6个通道，每个通道3个参数）
    
    // 发送状态机
    reg [2:0] state;
    localparam IDLE      = 3'b000;
    localparam BUILD_PKG = 3'b001;
    localparam SEND_PKG  = 3'b010;
    localparam WAIT_NEXT = 3'b011;
    
    // ================== UART实例化 ==================
    // 使用COREUART模块发送科学数据
    COREUART uart_inst (
        .clk(clk),
        .rst_n(rst_n),
        
        .cs_n(cs_n),
        .baud_val(baud_val),
        .bit8(1'b1),        // 8位数据位
        .parity_en(1'b1),   // 使能奇偶校验
        .odd_n_even(1'b1),  // 奇校验
        
        .we_n(we_n),        // 写使能信号
        .data_in(data_in),  // 待写数据
        .txrdy(txrdy),      // 发送就绪标志
        .tx(tx),            // 发送数据线
        
        .re_n(1'b1),        // 禁用接收功能
        .rx(1'b1),          // 接收线不使用
        .rxrdy(),
        .data_out(),
        .parity_err(),
        .overflow()
    );
    
    // ================== 0.1秒定时器 ==================
    // 系统时钟40MHz，0.1s需要计数5,000,000次
    localparam TIMER_MAX = 32'd4000000; 
    
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            timer_counter <= 32'd0;
            sine_counter <= 16'd0;
        end
        else begin
            if (timer_counter >= TIMER_MAX-1) begin
                timer_counter <= 32'd0;
                // 每个科学数据包周期增加正弦波计数器
                sine_counter <= sine_counter + 16'd157; 
            end
            else begin
                timer_counter <= timer_counter + 1'b1;
            end
        end
    end
    
    // ================== 初始化PID参数 ==================
    // 在复位时初始化内部PID参数
    integer init_i;
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            // 初始化6个PID参数为不同的默认值
            for (init_i = 0; init_i < 6; init_i = init_i + 1) begin
                pid_params[init_i] <= 32'h01000000 + (init_i << 16);
            end
        end
    end
    
    // 寄存器更新逻辑，保存txrdy的上一个状态
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n)
            txrdy_prev <= 1'b0;
        else
            txrdy_prev <= txrdy;
    end
    // ================== 科学数据包构建和发送状态机 ==================
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            state <= IDLE;
            frame_counter <= 24'd0;
            send_index <= 8'd0;
            cs_n <= 1'b0;
            we_n <= 1'b1;
            data_in <= 8'h00;
        end
        else begin
            case (state)
                IDLE: begin
                    // 等待定时器触发，每0.1s发送一次科学数据
                    if (timer_counter == 32'd0) begin
                        state <= BUILD_PKG;
                    end
                    else begin
                        state <= IDLE;
                    end
                end
                
                BUILD_PKG: begin
                    // 构建科学数据包
                    build_science_data_package();
                    
                    // 更新帧计数
                    frame_counter <= frame_counter + 1'b1;
                    
                    // 准备发送
                    send_index <= 8'd0;
                    state <= SEND_PKG;
                end
                
                SEND_PKG: begin
                    // 发送科学数据包
                    if (send_index < SCIENCE_DATA_SIZE) begin
                        if (send_index == 8'd0) begin
                         if (txrdy) begin
                            // UART就绪，发送下一个字节
                            we_n <= 1'b0;
                            data_in <= science_data[send_index];
                            send_index <= send_index + 1'b1;
                         end
                         else begin
                            we_n <= 1'b1;
                         end
                        end
                        else begin
                            if (txrdy_rf) begin
                                we_n <= 1'b0;
                                data_in <= science_data[send_index];
                                send_index <= send_index + 1'b1;
                            end
                            else begin
                                we_n <= 1'b1;
                            end
                        end
                    end
                    else begin
                        // 所有数据已发送，等待下一周期
                        we_n <= 1'b1;
                        state <= WAIT_NEXT;
                    end
                end
                
                WAIT_NEXT: begin
                    // 等待当前周期结束
                    if (timer_counter >= TIMER_MAX - 32'd100) begin
                        state <= IDLE;
                    end
                    else begin
                        state <= WAIT_NEXT;
                    end
                end
                
                default: begin
                    state <= IDLE;
                end
            endcase
        end
    end
    
    // ================== 科学数据包构建任务 ==================
    // 构建完整的96字节科学数据包
    task build_science_data_package;
        integer i;
        reg [15:0] checksum;
    begin
        // 生成模拟的传感器数据
        generate_sensor_data();
        
        // 1. 帧头 (2字节)
        science_data[FRAME_HEAD_POS]     = FRAME_HEAD[15:8];
        science_data[FRAME_HEAD_POS+1]   = FRAME_HEAD[7:0];
        
        // 2. 数据长度 (2字节)
        science_data[DATA_LEN_POS]       = DATA_LEN[15:8];
        science_data[DATA_LEN_POS+1]     = DATA_LEN[7:0];
        
        // 3. 服务类型和状态 (2字节)
        science_data[SERVICE_TYPE_POS]   = SERVICE_TYPE;
        science_data[SERVICE_STATUS_POS] = SERVICE_STATUS;
        
        // 4. 帧计数 (3字节)
        science_data[FRAME_CNT_POS]      = frame_counter[23:16];
        science_data[FRAME_CNT_POS+1]    = frame_counter[15:8];
        science_data[FRAME_CNT_POS+2]    = frame_counter[7:0];
        
        // 5. 采样时刻微秒计 (3字节)
        science_data[USEC_CNT_POS]       = sys_time_ms[15:8];
        science_data[USEC_CNT_POS+1]     = sys_time_ms[7:0];
        science_data[USEC_CNT_POS+2]     = 8'h00; // 微秒的低8位，简化为0
        
        // 6. 采样时刻秒计 (4字节)
        science_data[SEC_CNT_POS]        = sys_time_s[31:24];
        science_data[SEC_CNT_POS+1]      = sys_time_s[23:16];
        science_data[SEC_CNT_POS+2]      = sys_time_s[15:8];
        science_data[SEC_CNT_POS+3]      = sys_time_s[7:0];
        
        // 7. 工作模式状态 (1字节)
        science_data[WORK_MODE_POS]      = work_mode;
        
        // 8. 温度数据 (4*3=12字节)
        for (i = 0; i < 4; i = i + 1) begin
            science_data[TEMP_DATA_POS+i*3]      = temp_data[i][23:16];
            science_data[TEMP_DATA_POS+i*3+1]    = temp_data[i][15:8];
            science_data[TEMP_DATA_POS+i*3+2]    = temp_data[i][7:0];
        end
        
        // 9. 位移数据 (6*3=18字节)
        for (i = 0; i < 6; i = i + 1) begin
            science_data[POSITION_DATA_POS+i*3]    = position_data[i][23:16];
            science_data[POSITION_DATA_POS+i*3+1]  = position_data[i][15:8];
            science_data[POSITION_DATA_POS+i*3+2]  = position_data[i][7:0];
        end
        
        // 10. 加速度数据 (6*3=18字节)
        for (i = 0; i < 6; i = i + 1) begin
            science_data[ACCEL_DATA_POS+i*3]    = accel_data[i][23:16];
            science_data[ACCEL_DATA_POS+i*3+1]  = accel_data[i][15:8];
            science_data[ACCEL_DATA_POS+i*3+2]  = accel_data[i][7:0];
        end
        
        // 11. 控制数据 (6*3=18字节)
        for (i = 0; i < 6; i = i + 1) begin
            science_data[CONTROL_DATA_POS+i*3]    = control_data[i][23:16];
            science_data[CONTROL_DATA_POS+i*3+1]  = control_data[i][15:8];
            science_data[CONTROL_DATA_POS+i*3+2]  = control_data[i][7:0];
        end
        
        // 12. 秒脉冲与系统时钟差值 (3字节)
        science_data[TIME_DIFF_POS]      = 8'h00;
        science_data[TIME_DIFF_POS+1]    = 8'h00;
        science_data[TIME_DIFF_POS+2]    = 8'h00;
        
        // 13. 保留字节 (6字节)，按0x55AA顺序填充
        science_data[RESERVED_POS]       = 8'h55;
        science_data[RESERVED_POS+1]     = 8'hAA;
        science_data[RESERVED_POS+2]     = 8'h55;
        science_data[RESERVED_POS+3]     = 8'hAA;
        science_data[RESERVED_POS+4]     = 8'h55;
        science_data[RESERVED_POS+5]     = 8'hAA;
        
        // 14. 计算校验和 (数据长度和有效数据字段的校验和)
        checksum = 16'h0000;
        // 数据长度字段
        checksum = checksum + science_data[DATA_LEN_POS];
        checksum = checksum + science_data[DATA_LEN_POS+1];
        // 有效数据字段 (从服务类型到保留字节)
        for (i = SERVICE_TYPE_POS; i < CHECKSUM_POS; i = i + 1) begin
            checksum = checksum + science_data[i];
        end
        
        // 15. 校验码 (2字节)
        science_data[CHECKSUM_POS]     = checksum[15:8];
        science_data[CHECKSUM_POS+1]   = checksum[7:0];
        
        // 16. 帧尾 (2字节)
        science_data[FRAME_TAIL_POS]   = FRAME_TAIL[15:8];
        science_data[FRAME_TAIL_POS+1] = FRAME_TAIL[7:0];
    end
    endtask
    
    // ================== 传感器数据生成任务 ==================
    // 生成模拟的传感器数据 (温度、位移、加速度和控制数据)
    task generate_sensor_data;
        integer i;
        reg [23:0] sine_value;
        reg [23:0] base_value;
    begin
        // 生成一个简单的正弦波值，用于数据变化
        sine_value = {8'h02, sine_counter}; 
        
        // 根据工作模式生成不同特性的数据
        case (work_mode)
            8'hAA: begin // 捕获模式
                // 生成温度数据 (基础值+正弦变化)
                for (i = 0; i < 4; i = i + 1) begin
                    base_value = 24'h200000 + (i << 16);
                    temp_data[i] = base_value + sine_value[15:0];
                end
                
                // 生成位移数据 (基础值+正弦变化)
                for (i = 0; i < 6; i = i + 1) begin
                    base_value = 24'h300000 + (i << 16);
                    position_data[i] = base_value + {8'h00, sine_counter[15:8], sine_counter[7:0]};
                end
                
                // 生成加速度数据 (基础值+正弦变化的较小幅度)
                for (i = 0; i < 6; i = i + 1) begin
                    base_value = 24'h400000 + (i << 16);
                    accel_data[i] = base_value + {16'h0000, sine_counter[15:8]};
                end
                
                // 生成控制数据 (使用内部生成的PID参数)
                for (i = 0; i < 6; i = i + 1) begin
                    control_data[i] = pid_params[i][23:0];
                end
            end
            
            8'h55: begin // 大量程模式
                // 生成温度数据 (基础值+不同相位的正弦变化)
                for (i = 0; i < 4; i = i + 1) begin
                    base_value = 24'h210000 + (i << 16);
                    temp_data[i] = base_value + {8'h00, sine_counter[7:0], sine_counter[15:8]};
                end
                
                // 生成较大范围的位移数据 (基础值+较大幅度正弦变化)
                for (i = 0; i < 6; i = i + 1) begin
                    base_value = 24'h500000 + (i << 16);
                    position_data[i] = base_value + {sine_counter[7:0], sine_counter[15:8], 8'h00};
                end
                
                // 生成较大范围的加速度数据 (基础值+大幅度变化)
                for (i = 0; i < 6; i = i + 1) begin
                    base_value = 24'h600000 + (i << 16);
                    accel_data[i] = base_value + {8'h00, sine_counter[15:0]};
                end
                
                // 生成控制数据 (使用内部生成的PID参数)
                for (i = 0; i < 6; i = i + 1) begin
                    control_data[i] = pid_params[i][23:0];
                end
            end
            
            8'h33: begin // 小量程模式
                // 生成温度数据 (与帧计数相关的小振幅变化)
                for (i = 0; i < 4; i = i + 1) begin
                    base_value = 24'h220000 + (i << 16);
                    temp_data[i] = base_value + {16'h0000, frame_counter[7:0]};
                end
                
                // 生成较小范围的位移数据 (基础值+小幅度变化)
                for (i = 0; i < 6; i = i + 1) begin
                    base_value = 24'h100000 + (i << 16);
                    position_data[i] = base_value + {16'h0000, sine_counter[7:0]};
                end
                
                // 生成较小范围的加速度数据 (基础值+最小幅度变化)
                for (i = 0; i < 6; i = i + 1) begin
                    base_value = 24'h100000 + (i << 16);
                    accel_data[i] = base_value + {20'h00000, sine_counter[11:8]};
                end
                
                // 生成控制数据 (使用内部生成的PID参数)
                for (i = 0; i < 6; i = i + 1) begin
                    control_data[i] = pid_params[i][23:0];
                end
            end
            
            8'h11: begin // 程序上注模式
                // 特殊模式，使用固定数据
                for (i = 0; i < 4; i = i + 1) begin
                    temp_data[i] = 24'h000111 * (i+1);
                end
                
                for (i = 0; i < 6; i = i + 1) begin
                    position_data[i] = 24'h000111 * (i+1);
                    accel_data[i] = 24'h000111 * (i+1);
                    // 控制数据也采用固定模式
                    control_data[i] = 24'h000111 * (i+1);
                end
            end
            
            default: begin // 未知模式，使用默认数据
                for (i = 0; i < 4; i = i + 1) begin
                    temp_data[i] = 24'h000000;
                end
                
                for (i = 0; i < 6; i = i + 1) begin
                    position_data[i] = 24'h000000;
                    accel_data[i] = 24'h000000;
                    control_data[i] = 24'h000000;
                end
            end
        endcase
    end
    endtask

endmodule 