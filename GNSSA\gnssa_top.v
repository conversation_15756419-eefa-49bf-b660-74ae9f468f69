module gnssa_top (
    input wire clk,                // 系统时钟
    input wire rst_n,              // 复位信号，低电平有效
    input wire [7:0] baud_val,     // 波特率配?
    
    // 系统时间接口
    input wire [31:0] sys_time_s,  // 系统时间(?)
    input wire [15:0] sys_time_ms, // 系统时间(毫秒)
    
    // 三路RS422接口
    output wire sci_tx            // 科学数据发?线
);

// 科学数据发??道
gnssa_science_data_channel science_data_inst (
    .clk(clk),
    .rst_n(rst_n),
    .baud_val(baud_val),
    
    // 系统时间
    .sys_time_s(sys_time_s),
    .sys_time_ms(sys_time_ms),
    
    // 状?参?
    .work_mode(8'h55),
    // UART输出
    .tx(sci_tx)
);

endmodule 