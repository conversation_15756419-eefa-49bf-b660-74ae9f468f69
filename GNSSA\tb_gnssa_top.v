`timescale 1ns/1ps

module tb_gnssa_top();

reg clk;
reg rst_n;
wire sci_tx;        // Scientific data output channel
reg [7:0] rx_byte;
reg [7:0] sci_data[0:95];    // Array for science data (96 bytes)
integer sci_count = 0;
integer i;

// Baud rate settings
parameter BAUD_RATE = 115200;
parameter BIT_TIME = 1_000_000_000/BAUD_RATE; // Bit time in nanoseconds

// Packet sizes
parameter SCI_PACKET_SIZE = 96;  // Science data packet size

// 只实例化需要测试的模块，保留sci_tx相关接口
gnssa_top gnssa_top_inst(
    .clk(clk),
    .rst_n(rst_n),
    .baud_val(8'd21), // 115200bps @ 50MHz
    .sys_time_s(32'd0),
    .sys_time_ms(16'd0),
    .sci_tx(sci_tx)
);

initial clk = 0;
always #12.5 clk = ~clk; // 50MHz clock

initial begin
    rst_n = 0;
    #100 rst_n = 1;
    
end

endmodule 