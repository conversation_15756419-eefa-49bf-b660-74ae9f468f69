module COREUART(
    // 时钟和复位信号
    input wire clk,                    // 系统时钟
    input wire rst,                    // 复位信号，高电平有效
    
    // 波特率配置
    input wire [15:0] baud_div,        // 波特率分频值（系统时钟频率 / 波特率）
    
    // UART配置参数
    input wire data_bits_8,            // 数据位数：1=8位数据，0=7位数据
    input wire parity_enable,          // 奇偶校验使能：1=使能校验，0=禁用校验
    input wire parity_odd,             // 校验方式：1=奇校验，0=偶校验
    input wire stop_bits_2,            // 停止位数：1=2个停止位，0=1个停止位
    
    // 发送接口 - 简化的valid/ready握手协议
    input wire [7:0] tx_data,          // 待发送数据
    input wire tx_data_valid,          // 发送数据有效信号
    output wire tx_data_ready,         // 发送数据就绪信号
    
    // 接收接口 - 简化的valid/ready握手协议
    output wire [7:0] rx_data,         // 接收到的数据
    output wire rx_data_valid,         // 接收数据有效信号
    input wire rx_data_ready,          // 接收数据就绪信号
    
    // 错误信号
    output wire rx_parity_error,       // 接收奇偶校验错误
    output wire rx_frame_error,        // 接收帧错误
    
    // 串行接口
    output wire uart_tx,               // 串行发送输出
    input wire uart_rx                 // 串行接收输入
);

    // 内部时钟信号
    wire baud_clk;                     // 波特率时钟
    wire sample_clk;                   // 采样时钟（16倍波特率）
    

    // 时钟生成模块实例化
    uart_clk_gen clk_gen_inst (
        .clk(clk),
        .rst(rst),
        .baud_div(baud_div),
        .baud_clk(baud_clk),
        .sample_clk(sample_clk)
    );
    
    // 发送模块实例化
    uart_tx tx_inst (
        .clk(clk),
        .rst(rst),
        .tx_clk(baud_clk),
        
        // 数据接口
        .data_in(tx_data),
        .data_valid(tx_data_valid),
        .data_ready(tx_data_ready),
        
        // 配置参数
        .data_bits_8(data_bits_8),
        .parity_enable(parity_enable),
        .parity_odd(parity_odd),
        .stop_bits_2(stop_bits_2),
        
        // 串行输出
        .tx_out(uart_tx)
    );
    
    // 接收模块实例化
    uart_rx rx_inst (
        .clk(clk),
        .rst(rst),
        .rx_clk(sample_clk),
        
        // 数据接口
        .data_out(rx_data),
        .data_valid(rx_data_valid),
        .data_ready(rx_data_ready),
        
        // 配置参数
        .data_bits_8(data_bits_8),
        .parity_enable(parity_enable),
        .parity_odd(parity_odd),
        
        // 错误信号
        .parity_error(rx_parity_error),
        .frame_error(rx_frame_error),
        
        // 串行输入
        .rx_in(uart_rx)
    );
 
endmodule

