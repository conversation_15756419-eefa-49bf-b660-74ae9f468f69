module COREUART(
    // 时钟和复位信号
    input wire clk,                    // 系统时钟
    input wire rst,                    // 复位信号，高电平有效
    
    // 波特率配置
    input wire [15:0] baud_div,        // 波特率分频值（系统时钟频率 / 波特率）
    
    // UART配置参数
    input wire data_bits_8,            // 数据位数：1=8位数据，0=7位数据
    input wire parity_enable,          // 奇偶校验使能：1=使能校验，0=禁用校验
    input wire parity_odd,             // 校验方式：1=奇校验，0=偶校验
    input wire stop_bits_2,            // 停止位数：1=2个停止位，0=1个停止位
    
    // 发送接口 - 简化的valid/ready握手协议
    input wire [7:0] tx_data,          // 待发送数据
    input wire tx_data_valid,          // 发送数据有效信号
    output wire tx_data_ready,         // 发送数据就绪信号
    
    // 接收接口 - 简化的valid/ready握手协议
    output wire [7:0] rx_data,         // 接收到的数据
    output wire rx_data_valid,         // 接收数据有效信号
    input wire rx_data_ready,          // 接收数据就绪信号
    
    // 错误信号
    output wire tx_error,              // 发送错误（保留接口，当前未使用）
    output wire rx_parity_error,       // 接收奇偶校验错误
    output wire rx_frame_error,        // 接收帧错误
    
    // 串行接口
    output wire uart_tx,               // 串行发送输出
    input wire uart_rx                 // 串行接收输入
);

    // 内部时钟信号
    wire baud_clk;                     // 波特率时钟
    wire sample_clk;                   // 采样时钟（16倍波特率）
    
    // 发送错误信号（当前未使用，保持接口完整性）
    assign tx_error = 1'b0;
    
    // 时钟生成模块实例化
    uart_clk_gen clk_gen_inst (
        .clk(clk),
        .rst(rst),
        .baud_div(baud_div),
        .baud_clk(baud_clk),
        .sample_clk(sample_clk)
    );
    
    // 发送模块实例化
    uart_tx tx_inst (
        .clk(clk),
        .rst(rst),
        .tx_clk(baud_clk),
        
        // 数据接口
        .data_in(tx_data),
        .data_valid(tx_data_valid),
        .data_ready(tx_data_ready),
        
        // 配置参数
        .data_bits_8(data_bits_8),
        .parity_enable(parity_enable),
        .parity_odd(parity_odd),
        .stop_bits_2(stop_bits_2),
        
        // 串行输出
        .tx_out(uart_tx)
    );
    
    // 接收模块实例化
    uart_rx rx_inst (
        .clk(clk),
        .rst(rst),
        .rx_clk(sample_clk),
        
        // 数据接口
        .data_out(rx_data),
        .data_valid(rx_data_valid),
        .data_ready(rx_data_ready),
        
        // 配置参数
        .data_bits_8(data_bits_8),
        .parity_enable(parity_enable),
        .parity_odd(parity_odd),
        
        // 错误信号
        .parity_error(rx_parity_error),
        .frame_error(rx_frame_error),
        
        // 串行输入
        .rx_in(uart_rx)
    );

endmodule

/*
================================================================================
COREUART 模块使用说明
================================================================================

这是一个简化的UART核心模块，集成了时钟生成、发送和接收功能。

主要特点：
    1. 统一的valid/ready握手协议
    2. 清晰易懂的信号命名
    3. 所有信号都是高电平有效
    4. 支持完整的UART功能配置

配置参数：
    - baud_div: 波特率分频值 = 系统时钟频率 / 目标波特率
    - data_bits_8: 1=8位数据，0=7位数据
    - parity_enable: 1=启用校验，0=禁用校验
    - parity_odd: 1=奇校验，0=偶校验
    - stop_bits_2: 1=2个停止位，0=1个停止位

发送接口：
    - tx_data[7:0]: 待发送的数据
    - tx_data_valid: 高电平表示有数据要发送
    - tx_data_ready: 高电平表示可以接收新的发送数据

接收接口：
    - rx_data[7:0]: 接收到的数据
    - rx_data_valid: 高电平表示有新数据接收完成
    - rx_data_ready: 高电平表示外部可以接收数据

错误信号：
    - rx_parity_error: 接收奇偶校验错误
    - rx_frame_error: 接收帧错误（停止位错误）

使用示例：

    // 基本实例化
    COREUART uart_inst (
        .clk(clk_40mhz),
        .rst(reset),
        
        // 40MHz时钟，115200波特率
        .baud_div(16'd347),              // 40,000,000 / 115200 = 347
        
        // 配置：8位数据，奇校验，2个停止位
        .data_bits_8(1'b1),
        .parity_enable(1'b1),
        .parity_odd(1'b1),
        .stop_bits_2(1'b1),
        
        // 发送接口
        .tx_data(send_data),
        .tx_data_valid(send_valid),
        .tx_data_ready(send_ready),
        
        // 接收接口
        .rx_data(recv_data),
        .rx_data_valid(recv_valid),
        .rx_data_ready(recv_ready),
        
        // 错误信号
        .rx_parity_error(parity_err),
        .rx_frame_error(frame_err),
        
        // 串行接口
        .uart_tx(tx_line),
        .uart_rx(rx_line)
    );

与FIFO对接示例：

    // 发送FIFO对接
    COREUART uart_inst (
        // ... 其他连接
        .tx_data(tx_fifo_data_out),
        .tx_data_valid(!tx_fifo_empty),  // FIFO非空时数据有效
        .tx_data_ready(tx_fifo_rd_en),   // 直接连接FIFO读使能
        
        // 接收FIFO对接
        .rx_data(rx_fifo_data_in),
        .rx_data_valid(rx_fifo_wr_en),   // 直接连接FIFO写使能
        .rx_data_ready(!rx_fifo_full),   // FIFO未满时可接收
        // ...
    );

================================================================================
*/
