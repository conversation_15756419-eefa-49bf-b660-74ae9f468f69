`timescale 1ns / 1ps

module tb_COREUART;

    // 测试参数 - 使用较小的分频值加快仿真
    parameter CLK_PERIOD = 25;          // 40MHz时钟周期 (25ns)
    parameter BAUD_DIV = 16'd16;        // 简化的分频值，用于快速仿真
    parameter BAUD_PERIOD = CLK_PERIOD * BAUD_DIV;  // 波特率周期
    
    // 测试信号
    reg clk;
    reg rst;
    
    // UART配置
    reg [15:0] baud_div;
    reg data_bits_8;
    reg parity_enable;
    reg parity_odd;
    reg stop_bits_2;
    
    // 发送接口
    reg [7:0] tx_data;
    reg tx_data_valid;
    wire tx_data_ready;
    
    // 接收接口
    wire [7:0] rx_data;
    wire rx_data_valid;
    reg rx_data_ready;
    
    // 错误信号
    wire rx_parity_error;
    wire rx_frame_error;
    
    // 串行接口
    wire uart_tx;
    wire uart_rx;
    
    // 测试用变量
    reg [7:0] test_data_queue [0:15];   // 测试数据队列
    reg [3:0] send_index;
    reg [7:0] recv_index;               // 修改为8位，避免溢出
    reg [7:0] received_data [0:15];     // 接收数据存储
    
    // 环回连接（发送输出连接到接收输入）
    assign uart_rx = uart_tx;
    
    // 实例化被测试模块
    COREUART uut (
        .clk(clk),
        .rst(rst),
        
        .baud_div(baud_div),
        .data_bits_8(data_bits_8),
        .parity_enable(parity_enable),
        .parity_odd(parity_odd),
        .stop_bits_2(stop_bits_2),
        
        .tx_data(tx_data),
        .tx_data_valid(tx_data_valid),
        .tx_data_ready(tx_data_ready),
        
        .rx_data(rx_data),
        .rx_data_valid(rx_data_valid),
        .rx_data_ready(rx_data_ready),
        
        .rx_parity_error(rx_parity_error),
        .rx_frame_error(rx_frame_error),
        
        .uart_tx(uart_tx),
        .uart_rx(uart_rx)
    );
    
    // 时钟生成
    initial begin
        clk = 0;
        forever #(CLK_PERIOD/2) clk = ~clk;
    end
    
    // 初始化测试数据
    initial begin
        test_data_queue[0]  = 8'h55;    // 01010101 - 测试数据位
        test_data_queue[1]  = 8'hAA;    // 10101010 - 测试数据位
        test_data_queue[2]  = 8'h00;    // 00000000 - 全0测试
        test_data_queue[3]  = 8'hFF;    // 11111111 - 全1测试
        test_data_queue[4]  = 8'h0F;    // 00001111 - 混合测试
        test_data_queue[5]  = 8'hF0;    // 11110000 - 混合测试
        test_data_queue[6]  = "A";      // ASCII字符测试
        test_data_queue[7]  = "B";
        test_data_queue[8]  = "C";
        test_data_queue[9]  = "D";
        test_data_queue[10] = 8'h7F;    // 7位数据最大值
        test_data_queue[11] = 8'h80;    // 测试最高位
        test_data_queue[12] = 8'h01;    // 最小非零值
        test_data_queue[13] = 8'h7E;    // 其他测试值
        test_data_queue[14] = 8'h81;
        test_data_queue[15] = 8'h42;
    end
    
    // 接收数据处理
    always @(posedge clk) begin
        if (rx_data_valid && rx_data_ready) begin
            received_data[recv_index] <= rx_data;
            recv_index <= recv_index + 1;
            $display("时间 %0t: 接收到数据 0x%02h (索引 %0d)", $time, rx_data, recv_index);

            // 检查奇偶校验错误
            if (rx_parity_error) begin
                $display("错误: 检测到奇偶校验错误!");
            end

            // 检查帧错误
            if (rx_frame_error) begin
                $display("错误: 检测到帧错误!");
            end
        end
    end

    // rx_data_ready信号管理
    always @(posedge clk) begin
        if (rst) begin
            rx_data_ready <= 1'b1;
        end
        // rx_data_ready保持为1，始终准备接收数据
    end
    
    // 主测试流程
    initial begin
        $display("================================================================================");
        $display("COREUART 模块测试开始");
        $display("================================================================================");
        
        // 初始化信号
        rst = 1;
        baud_div = BAUD_DIV;
        data_bits_8 = 1'b1;
        parity_enable = 1'b1;
        parity_odd = 1'b1;
        stop_bits_2 = 1'b1;
        tx_data = 8'h00;
        tx_data_valid = 1'b0;
        rx_data_ready = 1'b1;
        send_index = 0;
        recv_index = 0;  // 初始化接收索引
        
        // 复位
        #(CLK_PERIOD * 10);
        rst = 0;
        #(CLK_PERIOD * 50);  // 增加等待时间，确保UART完全初始化
        
        $display("测试1: 基本发送接收测试 (8位数据, 奇校验, 2停止位)");
        test_basic_transmission();
        
        $display("\n测试2: 7位数据测试");
        test_7bit_data();
        
        $display("\n测试3: 偶校验测试");
        test_even_parity();
        
        $display("\n测试4: 无校验测试");
        test_no_parity();
        
        $display("\n测试5: 1个停止位测试");
        test_1_stop_bit();
        
        $display("\n测试6: 连续数据传输测试");
        test_continuous_transmission();
        
        // 等待所有传输完成
        #(BAUD_PERIOD * 20);
        
        $display("\n================================================================================");
        $display("测试完成 - 数据验证");
        $display("================================================================================");
        verify_results();
        
        $display("\n================================================================================");
        $display("COREUART 模块测试结束");
        $display("================================================================================");
        
        $finish;
    end
    
    // 测试任务定义
    task test_basic_transmission;
        begin
            $display("发送测试数据: 0x55, 0xAA, 0x00, 0xFF");
            send_byte(8'h55);
            send_byte(8'hAA);
            send_byte(8'h00);
            send_byte(8'hFF);
            #(BAUD_PERIOD * 5);  // 等待传输完成
        end
    endtask
    
    task test_7bit_data;
        begin
            data_bits_8 = 1'b0;  // 切换到7位数据
            #(CLK_PERIOD * 5);
            $display("发送7位数据: 0x7F, 0x0F");
            send_byte(8'h7F);
            send_byte(8'h0F);
            #(BAUD_PERIOD * 5);
            data_bits_8 = 1'b1;  // 恢复8位数据
        end
    endtask
    
    task test_even_parity;
        begin
            parity_odd = 1'b0;   // 切换到偶校验
            #(CLK_PERIOD * 5);
            $display("发送偶校验数据: 0xF0, 0x0F");
            send_byte(8'hF0);
            send_byte(8'h0F);
            #(BAUD_PERIOD * 5);
            parity_odd = 1'b1;   // 恢复奇校验
        end
    endtask
    
    task test_no_parity;
        begin
            parity_enable = 1'b0; // 禁用校验
            #(CLK_PERIOD * 5);
            $display("发送无校验数据: 0x42, 0x81");
            send_byte(8'h42);
            send_byte(8'h81);
            #(BAUD_PERIOD * 5);
            parity_enable = 1'b1; // 恢复校验
        end
    endtask
    
    task test_1_stop_bit;
        begin
            stop_bits_2 = 1'b0;  // 切换到1个停止位
            #(CLK_PERIOD * 5);
            $display("发送1停止位数据: 0x7E, 0x81");
            send_byte(8'h7E);
            send_byte(8'h81);
            #(BAUD_PERIOD * 5);
            stop_bits_2 = 1'b1;  // 恢复2个停止位
        end
    endtask
    
    task test_continuous_transmission;
        begin
            $display("连续发送ASCII字符: A, B, C, D");
            send_byte("A");
            send_byte("B");
            send_byte("C");
            send_byte("D");
            #(BAUD_PERIOD * 8);
        end
    endtask
    
    task send_byte;
        input [7:0] data;
        begin
            @(posedge clk);
            while (!tx_data_ready) @(posedge clk);  // 等待发送就绪
            
            tx_data = data;
            tx_data_valid = 1'b1;
            @(posedge clk);
            tx_data_valid = 1'b0;
            
            $display("时间 %0t: 发送数据 0x%02h", $time, data);
        end
    endtask
    
    task verify_results;
        integer i;
        integer errors;
        integer total_received;
        begin
            errors = 0;
            total_received = recv_index;
            $display("验证接收到的数据:");

            // 显示所有接收到的数据
            for (i = 0; i < total_received; i = i + 1) begin
                $display("接收[%0d]: 0x%02h", i, received_data[i]);
            end

            $display("总共接收到 %0d 个字节", total_received);

            if (total_received == 0) begin
                $display("错误: 没有接收到任何数据!");
                errors = errors + 1;
            end
            else if (total_received < 10) begin
                $display("警告: 接收到的数据少于预期");
            end

            if (errors == 0) begin
                $display("✓ 所有测试通过!");
            end else begin
                $display("✗ 发现 %0d 个错误", errors);
            end
        end
    endtask

endmodule
