module uart_clk_gen(
    // 时钟和复位信号
    input wire clk,                    // 系统时钟
    input wire rst,                    // 复位信号，高电平有效
    
    // 波特率配置
    input wire [15:0] baud_div,        // 波特率分频值（系统时钟频率 / 波特率）
    
    // 输出时钟信号
    output reg baud_clk,               // 波特率时钟（用于发送和接收状态机）
    output reg sample_clk              // 采样时钟（波特率时钟的16倍，用于接收采样）
);

    // 内部计数器
    reg [15:0] baud_counter;           // 波特率分频计数器
    reg [3:0] sample_counter;          // 采样时钟分频计数器（16分频）
    
    // 波特率时钟生成
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            baud_counter <= 16'h0000;
            baud_clk <= 1'b0;
        end
        else begin
            if (baud_counter >= baud_div - 1) begin
                baud_counter <= 16'h0000;    // 计数器复位
                baud_clk <= 1'b1;            // 产生一个时钟周期的脉冲
            end
            else begin
                baud_counter <= baud_counter + 1'b1;
                baud_clk <= 1'b0;            // 其他时候保持低电平
            end
        end
    end
    
    // 采样时钟生成（波特率时钟的16倍频率）
    reg [15:0] sample_counter_16x;     // 16倍采样计数器
    wire [15:0] sample_div;            // 采样分频值
    assign sample_div = baud_div >> 4; // baud_div / 16

    always @(posedge clk or posedge rst) begin
        if (rst) begin
            sample_counter_16x <= 16'h0000;
            sample_clk <= 1'b0;
        end
        else begin
            if (sample_counter_16x >= sample_div - 1) begin
                sample_counter_16x <= 16'h0000;    // 计数器复位
                sample_clk <= 1'b1;                // 产生一个时钟周期的脉冲
            end
            else begin
                sample_counter_16x <= sample_counter_16x + 1'b1;
                sample_clk <= 1'b0;                // 其他时候保持低电平
            end
        end
    end

endmodule

/*
================================================================================
波特率分频值计算说明
================================================================================

计算公式：
    baud_div = 系统时钟频率(Hz) / 目标波特率(bps)

常用配置示例：

40MHz系统时钟：
    9600   bps -> baud_div = 40,000,000 / 9600   = 4167
    19200  bps -> baud_div = 40,000,000 / 19200  = 2083
    38400  bps -> baud_div = 40,000,000 / 38400  = 1042
    57600  bps -> baud_div = 40,000,000 / 57600  = 694
    115200 bps -> baud_div = 40,000,000 / 115200 = 347
    230400 bps -> baud_div = 40,000,000 / 230400 = 174

50MHz系统时钟：
    9600   bps -> baud_div = 50,000,000 / 9600   = 5208
    115200 bps -> baud_div = 50,000,000 / 115200 = 434
    230400 bps -> baud_div = 50,000,000 / 230400 = 217

100MHz系统时钟：
    9600   bps -> baud_div = 100,000,000 / 9600   = 10417
    115200 bps -> baud_div = 100,000,000 / 115200 = 868
    230400 bps -> baud_div = 100,000,000 / 230400 = 434

使用示例：
    // 40MHz系统时钟，115200波特率
    uart_clk_gen clk_gen_inst (
        .clk(clk_40mhz),
        .rst(rst),
        .baud_div(16'd347),          // 40,000,000 / 115200 = 347
        .baud_clk(baud_clk),
        .sample_clk(sample_clk)
    );

注意事项：
    1. baud_div值必须大于0
    2. 建议baud_div值不要小于10，以确保时钟稳定性
    3. 实际波特率 = 系统时钟频率 / baud_div
    4. sample_clk频率是baud_clk的16倍，用于接收模块的精确采样

================================================================================
*/
