module uart_clk_gen(
    // 时钟和复位信号
    input wire clk,                    // 系统时钟
    input wire rst,                    // 复位信号，高电平有效
    
    // 波特率配置
    input wire [15:0] baud_div,        // 波特率分频值（系统时钟频率 / 波特率）
    
    // 输出时钟信号
    output reg baud_clk,               // 波特率时钟（用于发送和接收状态机）
    output reg sample_clk              // 采样时钟（波特率时钟的16倍，用于接收采样）
);

    // 内部计数器
    reg [15:0] baud_counter;           // 波特率分频计数器
    reg [3:0] sample_counter;          // 采样时钟分频计数器（16分频）
    
    // 波特率时钟生成
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            baud_counter <= 16'h0000;
            baud_clk <= 1'b0;
        end
        else begin
            if (baud_counter >= baud_div - 1) begin
                baud_counter <= 16'h0000;    // 计数器复位
                baud_clk <= 1'b1;            // 产生一个时钟周期的脉冲
            end
            else begin
                baud_counter <= baud_counter + 1'b1;
                baud_clk <= 1'b0;            // 其他时候保持低电平
            end
        end
    end
    
    // 采样时钟生成（波特率时钟的16倍频率）
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            sample_counter <= 4'h0;
            sample_clk <= 1'b0;
        end
        else begin
            if (baud_counter >= (baud_div >> 4) - 1) begin  // baud_div / 16
                if (sample_counter >= 15) begin
                    sample_counter <= 4'h0;
                    sample_clk <= 1'b1;         // 产生采样时钟脉冲
                end
                else begin
                    sample_counter <= sample_counter + 1'b1;
                    sample_clk <= 1'b0;
                end
            end
            else begin
                sample_clk <= 1'b0;
            end
        end
    end

endmodule

// 波特率配置计算辅助模块
module baud_rate_calculator(
    input wire [31:0] sys_clk_freq,   // 系统时钟频率（Hz）
    input wire [31:0] target_baud,    // 目标波特率（bps）
    output wire [15:0] baud_div       // 计算出的分频值
);

    // 计算分频值：系统时钟频率 / 波特率
    assign baud_div = sys_clk_freq / target_baud;

endmodule

// 常用波特率预定义参数模块
module uart_baud_params;
    
    // 40MHz系统时钟的常用波特率分频值
    parameter BAUD_9600_40MHZ   = 16'd4167;   // 40,000,000 / 9600
    parameter BAUD_19200_40MHZ  = 16'd2083;   // 40,000,000 / 19200
    parameter BAUD_38400_40MHZ  = 16'd1042;   // 40,000,000 / 38400
    parameter BAUD_57600_40MHZ  = 16'd694;    // 40,000,000 / 57600
    parameter BAUD_115200_40MHZ = 16'd347;    // 40,000,000 / 115200
    parameter BAUD_230400_40MHZ = 16'd174;    // 40,000,000 / 230400
    parameter BAUD_460800_40MHZ = 16'd87;     // 40,000,000 / 460800
    parameter BAUD_921600_40MHZ = 16'd43;     // 40,000,000 / 921600
    
    // 50MHz系统时钟的常用波特率分频值
    parameter BAUD_9600_50MHZ   = 16'd5208;   // 50,000,000 / 9600
    parameter BAUD_19200_50MHZ  = 16'd2604;   // 50,000,000 / 19200
    parameter BAUD_38400_50MHZ  = 16'd1302;   // 50,000,000 / 38400
    parameter BAUD_57600_50MHZ  = 16'd868;    // 50,000,000 / 57600
    parameter BAUD_115200_50MHZ = 16'd434;    // 50,000,000 / 115200
    parameter BAUD_230400_50MHZ = 16'd217;    // 50,000,000 / 230400
    parameter BAUD_460800_50MHZ = 16'd109;    // 50,000,000 / 460800
    parameter BAUD_921600_50MHZ = 16'd54;     // 50,000,000 / 921600
    
    // 100MHz系统时钟的常用波特率分频值
    parameter BAUD_9600_100MHZ   = 16'd10417;  // 100,000,000 / 9600
    parameter BAUD_19200_100MHZ  = 16'd5208;   // 100,000,000 / 19200
    parameter BAUD_38400_100MHZ  = 16'd2604;   // 100,000,000 / 38400
    parameter BAUD_57600_100MHZ  = 16'd1736;   // 100,000,000 / 57600
    parameter BAUD_115200_100MHZ = 16'd868;    // 100,000,000 / 115200
    parameter BAUD_230400_100MHZ = 16'd434;    // 100,000,000 / 230400
    parameter BAUD_460800_100MHZ = 16'd217;    // 100,000,000 / 460800
    parameter BAUD_921600_100MHZ = 16'd109;    // 100,000,000 / 921600

endmodule
