module uart_rx(
    // 时钟和复位信号
    input wire clk,                    // 系统时钟
    input wire rst,                    // 复位信号，高电平有效
    input wire rx_clk,                 // 接收时钟脉冲（波特率时钟）
    
    // 数据接口 - 简化的valid/ready握手协议
    output reg [7:0] data_out,         // 接收到的数据
    output reg data_valid,             // 数据有效信号，高电平表示有新数据
    input wire data_ready,             // 数据就绪信号，高电平表示外部可以接收数据
    
    // 配置参数
    input wire data_bits_8,            // 数据位数：1=8位数据，0=7位数据
    input wire parity_enable,          // 奇偶校验使能：1=使能校验，0=禁用校验
    input wire parity_odd,             // 校验方式：1=奇校验，0=偶校验
    
    // 错误信号
    output reg parity_error,           // 奇偶校验错误标志
    output reg frame_error,            // 帧错误标志（停止位错误）
    
    // 串行数据输入
    input wire rx_in                   // 串行数据输入
);

    // 内部寄存器定义
    reg [7:0] rx_shift_reg;            // 接收移位寄存器
    reg [3:0] bit_counter;             // 位计数器
    reg [3:0] sample_counter;          // 采样计数器（16倍过采样）
    reg rx_parity_calc;                // 奇偶校验计算
    
    // 接收状态机状态定义
    reg [2:0] rx_state;
    localparam STATE_IDLE       = 3'b000;  // 空闲状态，等待起始位
    localparam STATE_START_BIT  = 3'b001;  // 接收起始位
    localparam STATE_DATA_BITS  = 3'b010;  // 接收数据位
    localparam STATE_PARITY_BIT = 3'b011;  // 接收校验位
    localparam STATE_STOP_BIT   = 3'b100;  // 接收停止位
    localparam STATE_DATA_READY = 3'b101;  // 数据准备就绪
    
    // 输入信号同步和滤波
    reg [2:0] rx_sync;                 // 3级同步寄存器
    reg rx_filtered;                   // 滤波后的接收信号
    
    // 输入信号同步（防止亚稳态）
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            rx_sync <= 3'b111;            // 空闲时为高电平
        end
        else begin
            rx_sync <= {rx_sync[1:0], rx_in};
        end
    end
    
    // 简单的多数表决滤波
    always @(*) begin
        case (rx_sync)
            3'b000, 3'b001, 3'b010, 3'b100: rx_filtered = 1'b0;
            3'b011, 3'b101, 3'b110, 3'b111: rx_filtered = 1'b1;
            default: rx_filtered = 1'b1;
        endcase
    end
    
    // 数据输出控制
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            data_valid <= 1'b0;
        end
        else begin
            // 当数据准备好且外部就绪时，输出数据
            if (rx_state == STATE_DATA_READY && !data_valid) begin
                data_valid <= 1'b1;       // 设置数据有效
            end
            // 当外部确认读取数据后，清除有效标志
            else if (data_valid && data_ready) begin
                data_valid <= 1'b0;       // 清除数据有效标志
            end
        end
    end
    
    // 接收状态机
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            rx_state <= STATE_IDLE;
            sample_counter <= 4'h0;
            bit_counter <= 4'h0;
            rx_shift_reg <= 8'h00;
            data_out <= 8'h00;
            rx_parity_calc <= 1'b0;
            parity_error <= 1'b0;
            frame_error <= 1'b0;
        end
        else if (rx_clk) begin             // 只在接收时钟有效时进行状态转换
            case (rx_state)
                STATE_IDLE: begin
                    sample_counter <= 4'h0;
                    bit_counter <= 4'h0;
                    rx_parity_calc <= 1'b0;
                    parity_error <= 1'b0;
                    frame_error <= 1'b0;
                    
                    // 检测起始位（下降沿）
                    if (!rx_filtered) begin
                        rx_state <= STATE_START_BIT;
                    end
                end
                
                STATE_START_BIT: begin
                    sample_counter <= sample_counter + 1'b1;
                    
                    // 在起始位中间采样确认
                    if (sample_counter == 4'h7) begin  // 第8个采样点
                        if (!rx_filtered) begin         // 确认是起始位
                            rx_state <= STATE_DATA_BITS;
                            sample_counter <= 4'h0;
                        end
                        else begin                      // 假起始位，回到空闲
                            rx_state <= STATE_IDLE;
                        end
                    end
                end
                
                STATE_DATA_BITS: begin
                    sample_counter <= sample_counter + 1'b1;
                    
                    // 在数据位中间采样
                    if (sample_counter == 4'hF) begin  // 第16个采样点
                        sample_counter <= 4'h0;
                        
                        // 移位接收数据（LSB先接收）
                        rx_shift_reg <= {rx_filtered, rx_shift_reg[7:1]};
                        
                        // 计算奇偶校验
                        if (parity_enable) begin
                            rx_parity_calc <= rx_parity_calc ^ rx_filtered;
                        end
                        
                        bit_counter <= bit_counter + 1'b1;
                        
                        // 检查是否接收完所有数据位
                        if ((data_bits_8 && bit_counter == 4'h7) ||     // 8位数据
                            (!data_bits_8 && bit_counter == 4'h6)) begin // 7位数据
                            
                            if (parity_enable) begin
                                rx_state <= STATE_PARITY_BIT;
                            end
                            else begin
                                rx_state <= STATE_STOP_BIT;
                            end
                        end
                    end
                end
                
                STATE_PARITY_BIT: begin
                    sample_counter <= sample_counter + 1'b1;
                    
                    // 在校验位中间采样
                    if (sample_counter == 4'hF) begin
                        sample_counter <= 4'h0;
                        
                        // 检查奇偶校验
                        if (parity_odd) begin
                            // 奇校验：数据位1的个数 + 校验位1的个数 应该为奇数
                            parity_error <= !(rx_parity_calc ^ rx_filtered);
                        end
                        else begin
                            // 偶校验：数据位1的个数 + 校验位1的个数 应该为偶数
                            parity_error <= rx_parity_calc ^ rx_filtered;
                        end
                        
                        rx_state <= STATE_STOP_BIT;
                    end
                end
                
                STATE_STOP_BIT: begin
                    sample_counter <= sample_counter + 1'b1;
                    
                    // 在停止位中间采样
                    if (sample_counter == 4'hF) begin
                        sample_counter <= 4'h0;
                        
                        // 检查停止位（应该为高电平）
                        if (rx_filtered) begin
                            // 停止位正确，准备输出数据
                            if (data_bits_8) begin
                                data_out <= rx_shift_reg;
                            end
                            else begin
                                data_out <= {1'b0, rx_shift_reg[6:0]};  // 7位数据，取低7位，高位补0
                            end
                            rx_state <= STATE_DATA_READY;
                        end
                        else begin
                            // 停止位错误
                            frame_error <= 1'b1;
                            rx_state <= STATE_IDLE;
                        end
                    end
                end
                
                STATE_DATA_READY: begin
                    // 等待外部读取数据
                    if (data_valid && data_ready) begin
                        rx_state <= STATE_IDLE;  // 数据被读取，回到空闲状态
                    end
                end
                
                default: begin
                    rx_state <= STATE_IDLE;
                end
            endcase
        end
    end

endmodule
