module uart_tx(
    // 时钟和复位信号
    input wire clk,                    // 系统时钟
    input wire rst,                    // 复位信号，高电平有效
    input wire tx_clk,                 // 发送时钟脉冲（波特率时钟）
    
    // 数据接口 - 简化的valid/ready握手协议
    input wire [7:0] data_in,          // 待发送数据
    input wire data_valid,             // 数据有效信号，高电平表示有数据要发送
    output reg data_ready,             // 数据就绪信号，高电平表示可以接收新数据
    
    // 配置参数
    input wire data_bits_8,            // 数据位数：1=8位数据，0=7位数据
    input wire parity_enable,          // 奇偶校验使能：1=使能校验，0=禁用校验
    input wire parity_odd,             // 校验方式：1=奇校验，0=偶校验
    input wire stop_bits_2,            // 停止位数：1=2个停止位，0=1个停止位
    
    // 输出信号
    output reg tx_out                  // 串行数据输出
);

    // 内部寄存器定义
    reg [7:0] tx_shift_reg;            // 发送移位寄存器
    reg [3:0] bit_counter;             // 位计数器
    reg tx_parity_bit;                 // 奇偶校验位
    
    // 发送状态机状态定义
    reg [2:0] tx_state;
    localparam STATE_IDLE       = 3'b000;  // 空闲状态
    localparam STATE_START_BIT  = 3'b001;  // 发送起始位
    localparam STATE_DATA_BITS  = 3'b010;  // 发送数据位
    localparam STATE_PARITY_BIT = 3'b011;  // 发送校验位
    localparam STATE_STOP_BIT1  = 3'b100;  // 发送第一个停止位
    localparam STATE_STOP_BIT2  = 3'b101;  // 发送第二个停止位
    
    // 数据接收和就绪信号控制
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            data_ready <= 1'b1;           // 复位后可以接收数据
            tx_shift_reg <= 8'h00;
        end
        else begin
            // 当有有效数据且模块就绪时，接收数据
            if (data_valid && data_ready) begin
                tx_shift_reg <= data_in;   // 锁存输入数据
                data_ready <= 1'b0;       // 设置为忙碌状态
            end
            // 当数据被发送状态机锁存后，重新变为就绪状态
            else if (tx_clk && tx_state == STATE_IDLE && !data_ready) begin
                data_ready <= 1'b1;       // 可以接收下一个数据
            end
        end
    end
    
    // 发送状态机
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            tx_state <= STATE_IDLE;
            bit_counter <= 4'h0;
            tx_out <= 1'b1;               // 空闲时输出高电平
            tx_parity_bit <= 1'b0;
        end
        else if (tx_clk) begin             // 只在发送时钟有效时进行状态转换
            case (tx_state)
                STATE_IDLE: begin
                    tx_out <= 1'b1;       // 空闲时保持高电平
                    bit_counter <= 4'h0;
                    tx_parity_bit <= 1'b0;
                    
                    // 如果有数据要发送，开始发送序列
                    if (!data_ready) begin
                        tx_state <= STATE_START_BIT;
                    end
                end
                
                STATE_START_BIT: begin
                    tx_out <= 1'b0;       // 发送起始位（低电平）
                    tx_state <= STATE_DATA_BITS;
                    bit_counter <= 4'h0;
                end
                
                STATE_DATA_BITS: begin
                    // 发送数据位（LSB先发送）
                    tx_out <= tx_shift_reg[bit_counter];
                    
                    // 计算奇偶校验位
                    if (parity_enable) begin
                        tx_parity_bit <= tx_parity_bit ^ tx_shift_reg[bit_counter];
                    end
                    
                    // 检查是否发送完所有数据位
                    if ((data_bits_8 && bit_counter == 4'h7) ||     // 8位数据
                        (!data_bits_8 && bit_counter == 4'h6)) begin // 7位数据
                        
                        if (parity_enable) begin
                            tx_state <= STATE_PARITY_BIT;
                        end
                        else begin
                            tx_state <= STATE_STOP_BIT1;
                        end
                    end
                    else begin
                        bit_counter <= bit_counter + 1'b1;
                    end
                end
                
                STATE_PARITY_BIT: begin
                    // 发送校验位
                    tx_out <= parity_odd ? ~tx_parity_bit : tx_parity_bit;
                    tx_state <= STATE_STOP_BIT1;
                end
                
                STATE_STOP_BIT1: begin
                    tx_out <= 1'b1;       // 发送第一个停止位（高电平）
                    
                    if (stop_bits_2) begin
                        tx_state <= STATE_STOP_BIT2;  // 如果需要2个停止位
                    end
                    else begin
                        tx_state <= STATE_IDLE;       // 否则回到空闲状态
                    end
                end
                
                STATE_STOP_BIT2: begin
                    tx_out <= 1'b1;       // 发送第二个停止位（高电平）
                    tx_state <= STATE_IDLE; // 回到空闲状态
                end
                
                default: begin
                    tx_state <= STATE_IDLE;
                    tx_out <= 1'b1;
                end
            endcase
        end
    end

endmodule
