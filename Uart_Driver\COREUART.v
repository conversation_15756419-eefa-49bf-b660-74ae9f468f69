module COREUART(
    input clk,
    input rst_n,

    input cs_n,  //片选信号  
    input [7:0]baud_val, //波特率设置
    input bit8,  //数据位数
    input parity_en,  //奇偶校验使能
    input odd_n_even,  //奇偶校验方式
    input stop_bits = 1'b1,  //停止位配置: 0=1位停止位, 1=2位停止位 (默认2位停止位，保持兼容性)

    input we_n,  //写使能信号
    input [7:0]data_in, //待写数据
    output wire txrdy,  //发送就绪标志
    output wire tx,  //发送数据

    input re_n,  //读使能信号
    input rx,  //接收数据
    output reg rxrdy,  //接收就绪标志
    output reg [7:0]data_out,  //读数据
    output wire parity_err,  //奇偶校验错误标志
    output wire overflow    //溢出错误标志
);

//时钟信号
wire baud_pulse,tx_pulse;

// 添加寄存器捕获re_n的上一个状态
reg re_n_d1;
reg we_n_d1;

//发送模块
reg uart_tx_en;
reg [7:0] tx_data_temp;

//接收模块
reg read_rx_byte;
wire [7:0] rx_byte;
wire receive_full;
//奇偶校验
reg clear_parity;
reg clear_parity_temp;
wire clear_parity_en;

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        we_n_d1 <= 1'b1; 
    end
    else begin
        we_n_d1 <= we_n;  
    end
end

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        uart_tx_en <= 1'b0;
        tx_data_temp <= 8'b00000000;
    end
    else begin
        if (cs_n == 1'b0 && we_n == 1'b0 && we_n_d1 == 1'b1) begin
            uart_tx_en <= 1'b1;
            tx_data_temp <= data_in;
        end
        else
            uart_tx_en <= 1'b0;
    end
end

// 添加寄存器更新逻辑，保存re_n的上一个状态
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        re_n_d1 <= 1'b1;  // 复位时假设re_n为高电平
    end
    else begin
        re_n_d1 <= re_n;  // 保存当前状态
    end
end

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        read_rx_byte <= 1'b0;
        data_out <= 8'b00000000;
        rxrdy <= 1'b0;
    end
    else begin
        data_out <= rx_byte;
        rxrdy <= receive_full;
        // 修改为下降沿检测：检测re_n从高到低的转变
        if (cs_n == 1'b0 && re_n == 1'b0 && re_n_d1 == 1'b1) 
            read_rx_byte <= 1'b1;
        else
            read_rx_byte <= 1'b0;  
    end    
end

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        clear_parity <= 1'b0;
        clear_parity_temp <= 1'b0;
    end
    else begin
        clear_parity_temp <= clear_parity_en;
        clear_parity <= clear_parity_temp;
    end
end

//模块连接
uart_clk_gen uart_clk_gen_inst(
    .clk(clk),
    .rst_n(rst_n),
    .baud_val(baud_val),
    .baud_pulse(baud_pulse),
    .tx_pulse(tx_pulse)
);

uart_tx uart_tx_inst(
    .clk(clk),
    .rst_n(rst_n),
    .tx_pulse(tx_pulse),
    .uart_tx_en(uart_tx_en),
    .tx_data(tx_data_temp),
    .bit8(bit8),
    .parity_en(parity_en),
    .odd_n_even(odd_n_even),
    .stop_bits(stop_bits),
    .txrdy(txrdy),
    .tx_bit(tx)
);

uart_rx uart_rx_inst(
    .clk(clk),
    .rst_n(rst_n),
    .baud_pulse(baud_pulse),
    .bit8(bit8),
    .parity_en(parity_en),
    .odd_n_even(odd_n_even),
    .clear_parity(clear_parity),
    .read_rx_byte(read_rx_byte),
    .rx_bit(rx),
    .overflow(overflow),
    .parity_err(parity_err),
    .clear_parity_en(clear_parity_en),
    .receive_full(receive_full),
    .rx_byte(rx_byte)
);

endmodule