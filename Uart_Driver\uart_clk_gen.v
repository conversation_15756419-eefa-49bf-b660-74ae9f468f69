module uart_clk_gen(
    input clk,
    input rst_n,
    input [7:0] baud_val,

    output reg baud_pulse,
    output reg tx_pulse
);

reg [7:0] baud_pulse_cnt;
reg baud_pulse_temp;

reg [3:0] tx_pulse_cnt;
reg tx_pulse_temp;

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        baud_pulse_cnt <= 8'd0;
        baud_pulse_temp <= 1'b0;
    end
    else begin
        if (baud_pulse_cnt == 8'd0) begin
            baud_pulse_cnt <= baud_val;
            baud_pulse_temp <= 1'b1;
        end
        else begin
            baud_pulse_cnt <= baud_pulse_cnt - 1'b1;
            baud_pulse_temp <= 1'b0;
        end
    end
end

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        tx_pulse_cnt <= 4'd0;
        tx_pulse_temp <= 1'b0;
    end
    else begin
        if (baud_pulse_temp) begin
            tx_pulse_cnt <= tx_pulse_cnt + 1'b1;
            if (tx_pulse_cnt == 4'd15) begin
                tx_pulse_temp <= 1'b1;
                tx_pulse_cnt <= 4'd0;
            end
            else 
                tx_pulse_temp <= 1'b0;
            end 
        end
    end

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        baud_pulse <= 1'b0;
        tx_pulse <= 1'b0;
    end
    else begin
        baud_pulse <= baud_pulse_temp;
        tx_pulse <= tx_pulse_temp & baud_pulse_temp;
    end 
end

endmodule