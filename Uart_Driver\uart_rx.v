module uart_rx(
    input clk,
    input rst_n,
    input baud_pulse,
   
    input bit8,  // 选用7位数据(bit8=0)还是8位数据(bit8=1)

    input parity_en,    //奇偶校验使能
    input odd_n_even,   //奇偶校验方式，0为偶校验，1为奇校验
    input clear_parity, //清除奇偶校验错误

    input read_rx_byte, //外部数据读取完毕
    input rx_bit,           //接收数据

    output reg overflow, //溢出错误，上一字节数据没有读取
    output reg parity_err, //奇偶校验错误标志位
    output reg clear_parity_en, //清除奇偶校验错误使能
    output reg receive_full, //接收数据满标志，提醒外部读取
    output reg [7:0] rx_byte //接收数据(串转并)
);

reg [3:0] receive_count;  //对baud_pulse做16倍过采样
reg [3:0] rx_bit_cnt;     //接收数据位计数器
reg [3:0] rx_bit_num;  //接收的数据位数

reg [2:0] samples;        //滑动窗口，包含最近的3个接收位值
reg rx_bit_filtered;      //过滤后的接收数据

reg [2:0] rx_state;            //接收状态机
reg [7:0] rx_byte_temp;        //接收数据
reg [8:0] rx_shift;            //接收数据移位寄存器

reg receive_full_int;       //接收数据满标志

reg overflow_int;           //溢出错误
reg overflow_temp;      //溢出错误临时寄存器

reg rx_parity_calc;         //奇偶校验计算
reg parity_err_temp;        //奇偶校验错误临时寄存器
reg clear_parity_en_temp;  //清除奇偶校验错误使能临时寄存器

// 接收状态机
parameter RX_IDLE = 3'b000;
parameter RX_RECEIVE_BITS = 3'b001;
parameter RX_STOP_BIT = 3'b010;

//中间变量赋值给端口
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        overflow <= 1'b0;
        parity_err <= 1'b0;
        clear_parity_en <= 1'b0;
        receive_full <= 1'b0;
        rx_byte <= 8'b00000000;
    end
    else begin
        overflow <= overflow_temp;
        parity_err <= parity_err_temp;
        clear_parity_en <= clear_parity_en_temp;
        receive_full <= receive_full_int;
        rx_byte <= rx_byte_temp;
    end
end

// 多数表决滤波器
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) 
        samples <= 3'b000;
    else if (baud_pulse) begin
        samples <= {samples[1:0], rx_bit};
    end
end

always @(samples) begin
   case (samples)
    3'b000: rx_bit_filtered <= 1'b0;
    3'b001: rx_bit_filtered <= 1'b0;
    3'b010: rx_bit_filtered <= 1'b0;
    3'b011: rx_bit_filtered <= 1'b1;
    3'b100: rx_bit_filtered <= 1'b0;
    3'b101: rx_bit_filtered <= 1'b1;
    3'b110: rx_bit_filtered <= 1'b1;
    3'b111: rx_bit_filtered <= 1'b1;
   endcase
end

// receive_count 每计数16次接收1bit数据,相当于对baud_pulse 做16倍过采样
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        receive_count <= 4'b0000;
    end
    else if (baud_pulse) begin
        // 传输还未开始时，接收位rx始终为高电平，samples==111，rx_bit_filtered为1
        if (rx_state == RX_IDLE && (rx_bit_filtered == 1'b1 || receive_count == 4'b1000)) 
            receive_count <= 4'b0000;
        else 
            receive_count <= receive_count + 4'b0001;
    end
end

// 根据数据位数和奇偶校验位数，确定数据位数
always @(*) begin
  case ({bit8, parity_en})
    2'b00:  rx_bit_num <= 4'b0111; // 7位数据，无校验
    2'b10:  rx_bit_num <= 4'b1000; // 8位数据，无校验
    2'b01:  rx_bit_num <= 4'b1000; // 7位数据，有校验
    2'b11:  rx_bit_num <= 4'b1001; // 8位数据，有校验
  endcase
end

// 接收状态机
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        rx_state <= RX_IDLE;
        rx_byte_temp <= 8'b00000000;
        overflow_int <= 1'b0;
    end
    else if (baud_pulse) begin
        overflow_int <= 1'b0;
        case (rx_state)
            RX_IDLE: begin
                if (receive_count == 4'b1000)
                    rx_state <= RX_RECEIVE_BITS;
                else
                    rx_state <= RX_IDLE;
            end

            RX_RECEIVE_BITS: begin
                if (rx_bit_cnt == rx_bit_num) begin
                    rx_state <= RX_STOP_BIT;
                    overflow_int <= receive_full_int;
                    if (receive_full_int == 1'b0) begin
                        rx_byte_temp <= {bit8 & rx_shift[7], rx_shift[6:0]};
                    end
                end
                else
                    rx_state <= RX_RECEIVE_BITS;
            end

            RX_STOP_BIT: begin
                if (receive_count == 4'b1111)
                    rx_state <= RX_IDLE;
                else
                    rx_state <= RX_STOP_BIT;
            end

            default: begin
                rx_state <= RX_IDLE;
            end
        endcase
    end
end

// 接收数据移位寄存器
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        rx_shift <= 9'b000000000;
        rx_bit_cnt <= 4'b0000;
    end
    else if (baud_pulse) begin
        if (rx_state == RX_IDLE) begin
            rx_shift <= 9'b000000000;
            rx_bit_cnt <= 4'b0000;
        end
        else begin
            if (receive_count == 4'b1111) begin
                rx_bit_cnt <= rx_bit_cnt + 4'b0001;
                case ({bit8, parity_en})
                    2'b00: begin // 7位数据，无校验
                        rx_shift[6:0] <= {rx_bit_filtered, rx_shift[6:1]};
                    end
                    2'b10: begin // 8位数据，无校验
                        rx_shift[7:0] <= {rx_bit_filtered, rx_shift[7:1]};
                    end
                    2'b01: begin // 7位数据，有校验
                        rx_shift[7:0] <= {rx_bit_filtered, rx_shift[7:1]};
                    end
                    2'b11: begin // 8位数据，有校验
                        rx_shift[8:0] <= {rx_bit_filtered, rx_shift[8:1]};
                    end
                endcase        
            end
        end
    end
end

// 奇偶校验计算
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) 
        rx_parity_calc <= 1'b0;
    else if (baud_pulse) begin
        if (rx_state == RX_STOP_BIT) 
            rx_parity_calc <= 1'b0;
        else if (receive_count == 4'b1111 && parity_en) 
            rx_parity_calc <= rx_parity_calc ^ rx_bit_filtered; 
    end
end

// 根据情况判断是否发生奇偶校验错误
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        parity_err_temp <= 1'b0;
    end
    else begin
        if (clear_parity) begin
            parity_err_temp <= 1'b0;
        end
        else if (baud_pulse && receive_count == 4'b1111 && parity_en) begin
            case ({bit8, odd_n_even})
                2'b00: begin // 7位数据，偶校验(数据位加上校验位,1的数量为偶数)
                    if (rx_bit_cnt == 4'b0111)
                        parity_err_temp <= rx_parity_calc ^ rx_bit_filtered;
                end
                2'b01: begin // 7位数据，奇校验
                    if (rx_bit_cnt == 4'b0111)
                        parity_err_temp <= !(rx_parity_calc ^ rx_bit_filtered);
                end
                2'b10: begin // 8位数据，偶校验
                    if (rx_bit_cnt == 4'b1000)
                        parity_err_temp <= rx_parity_calc ^ rx_bit_filtered;
                end
                2'b11: begin // 8位数据，奇校验
                    if (rx_bit_cnt == 4'b1000)
                        parity_err_temp <= !(rx_parity_calc ^ rx_bit_filtered); 
                end
            endcase
        end
    end
end

// 接收数据满标志处理，提醒外部模块接收数据，以及可以清除校验错误的时机
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        receive_full_int <= 1'b0;
        clear_parity_en_temp <= 1'b0;
    end
    else begin
        clear_parity_en_temp <= 1'b0;
        if (read_rx_byte) 
            receive_full_int <= 1'b0;
        else if (baud_pulse) begin
        case ({bit8, parity_en})
            2'b00: begin // 7位数据，无校验
                if (rx_bit_cnt == 4'b0111 && rx_state == RX_RECEIVE_BITS)begin
                    clear_parity_en_temp <= 1'b1;
                    receive_full_int <= 1'b1;
                end
            end
            2'b01: begin // 7位数据，有校验
                if (rx_bit_cnt == 4'b1000 && rx_state == RX_RECEIVE_BITS)begin
                    clear_parity_en_temp <= 1'b1;
                    receive_full_int <= 1'b1;
                end
            end
            2'b10: begin // 8位数据，无校验
                if (rx_bit_cnt == 4'b1000 && rx_state == RX_RECEIVE_BITS)begin
                    clear_parity_en_temp <= 1'b1;
                    receive_full_int <= 1'b1;
                end
            end
            2'b11: begin // 8位数据，有校验
                if (rx_bit_cnt == 4'b1001 && rx_state == RX_RECEIVE_BITS)begin
                    clear_parity_en_temp <= 1'b1;
                    receive_full_int <= 1'b1;
                end
            end
        endcase
        end
    end
end

// 处理外部溢出标志
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        overflow_temp <= 1'b0;
    end
	 else begin
	 if (read_rx_byte)
	     overflow_temp <= 1'b0;
    else if (baud_pulse) begin
        if (overflow_int) begin
            overflow_temp <= 1'b1;
        end
    end
	 end
end


endmodule