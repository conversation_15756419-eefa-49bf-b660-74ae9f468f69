module uart_tx(
    input clk,
    input rst_n,
    input tx_pulse,

    input uart_tx_en,
    input [7:0] tx_data,

    input bit8,
    input parity_en,
    input odd_n_even,
    input stop_bits,  // 停止位配置: 0=1位停止位, 1=2位停止位

    output reg txrdy,
    output reg tx_bit
);

reg [7:0] tx_byte;         //发送数据寄存器
reg [3:0] tx_bit_sel;      //位选择计数器

reg txrdy_int;            //内部发送就绪标志
reg tx_parity;            //奇偶校验计算结果
reg tx_bit_temp;          //发送数据位临时寄存器

reg [2:0] tx_state;       //发送状态机

parameter TX_IDLE = 3'b000;
parameter TX_LOAD = 3'b001;
parameter TX_START_BIT = 3'b010;
parameter TX_DATA_BITS = 3'b011;
parameter PARITY_BIT = 3'b100;
parameter TX_STOP_BIT1 = 3'b101;    // 第一个停止位
parameter TX_STOP_BIT2 = 3'b110;    // 新增：第二个停止位

// 中间变量赋值到端口
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        txrdy <= 1'b1;
        tx_bit <= 1'b1;
    end
    else begin
        txrdy <= txrdy_int;
        tx_bit <= tx_bit_temp;
    end
end

//下一帧数据是否可以接收
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        txrdy_int <= 1'b1;
    end
    else begin
        if (tx_pulse) begin
            if (tx_state == TX_LOAD) 
                txrdy_int <= 1'b1;
        end
        if (uart_tx_en) 
            txrdy_int <= 1'b0;
    end
end

//发送状态机
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        tx_state <= TX_IDLE;
        tx_byte <= 8'b00000000;
    end
    else if (tx_pulse) begin
        case (tx_state)
            TX_IDLE: begin
                if (!txrdy_int)
                    tx_state <= TX_LOAD;
                else
                    tx_state <= TX_IDLE;    
            end
            TX_LOAD: begin
                tx_byte <= tx_data;
                tx_state <= TX_START_BIT;
            end
            TX_START_BIT: begin       //发送起始位
                tx_state <= TX_DATA_BITS;
            end
            TX_DATA_BITS: begin       //发送数据位
                if (bit8) begin       //数据位数为7位还是8位
                    if (tx_bit_sel == 4'b0111) begin
                        if (parity_en)   //是否校验
                            tx_state <= PARITY_BIT;
                        else
                            tx_state <= TX_STOP_BIT1;
                    end
                    else
                        tx_state <= TX_DATA_BITS;
                end
                else begin
                    if (tx_bit_sel == 4'b0110) begin
                        if (parity_en)
                            tx_state <= PARITY_BIT;
                        else
                            tx_state <= TX_STOP_BIT1;
                    end
                    else
                        tx_state <= TX_DATA_BITS;
                end
            end
            PARITY_BIT: begin
                tx_state <= TX_STOP_BIT1;
            end
            TX_STOP_BIT1: begin           // 第一个停止位
                if (stop_bits) 
                    tx_state <= TX_STOP_BIT2;  // 2位停止位，进入第二个停止位
                else 
                    tx_state <= TX_IDLE;      // 1位停止位，直接回到空闲状态
            end
            TX_STOP_BIT2: begin           // 第二个停止位
                tx_state <= TX_IDLE;       // 回到空闲状态
            end
            default: begin
                tx_state <= TX_IDLE;
            end
        endcase
    end
end

//位选择计数器
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        tx_bit_sel <= 4'b0000;
    end
    else if (tx_pulse) begin
        if (tx_state == TX_DATA_BITS) 
            tx_bit_sel <= tx_bit_sel + 1;
        else
            tx_bit_sel <= 4'b0000;
    end
end

//根据当前状态控制tx输出
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        tx_bit_temp <= 1'b1;
    end
    else if (tx_pulse) begin
        case (tx_state)
            TX_IDLE: begin
                tx_bit_temp <= 1'b1;
            end
            TX_LOAD: begin
                tx_bit_temp <= 1'b1;
            end 
            TX_START_BIT: begin
                tx_bit_temp <= 1'b0;
            end
            TX_DATA_BITS: begin
                tx_bit_temp <= tx_byte[tx_bit_sel];
            end
            PARITY_BIT: begin
                tx_bit_temp <= odd_n_even ^ tx_parity;
            end
            TX_STOP_BIT1: begin       // 修改：第一个停止位
                tx_bit_temp <= 1'b1;
            end     
            TX_STOP_BIT2: begin       // 新增：第二个停止位
                tx_bit_temp <= 1'b1;
            end
            default: begin
                tx_bit_temp <= 1'b1;
            end
        endcase
    end
end

//校验位计算
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        tx_parity <= 1'b0;
    end
    else if (tx_pulse) begin
        // 根据停止位配置决定何时复位校验位
        if ((stop_bits && tx_state == TX_STOP_BIT2) || 
            (!stop_bits && tx_state == TX_STOP_BIT1)) begin
            tx_parity <= 1'b0;  // 同步复位
        end
        else if (parity_en) begin
            if (tx_state == TX_DATA_BITS) 
                tx_parity <= tx_parity ^ tx_byte[tx_bit_sel];
        end
    end
end

endmodule