module acc_command_channel (
    input wire clk,                // 系统时钟
    input wire rst_n,              // 复位信号，低电平有效
    input wire [7:0] baud_val,     // 波特率配置
    
    // UART接收
    input wire rx,                 // RS422接收线
    
    // 状态参数
    output reg [7:0] work_mode,    // 工作模式状态
    
    // PID参数
    output reg [31:0] pid_p_x1,    // X1通道比例参数
    output reg [31:0] pid_i_x1,    // X1通道积分参数
    output reg [31:0] pid_d_x1,    // X1通道微分参数 
    output reg [31:0] pid_p_x2,    // X2通道比例参数
    output reg [31:0] pid_i_x2,    // X2通道积分参数
    output reg [31:0] pid_d_x2,    // X2通道微分参数
    output reg [31:0] pid_p_y1,    // Y1通道比例参数
    output reg [31:0] pid_i_y1,    // Y1通道积分参数
    output reg [31:0] pid_d_y1,    // Y1通道微分参数
    output reg [31:0] pid_p_y2,    // Y2通道比例参数
    output reg [31:0] pid_i_y2,    // Y2通道积分参数
    output reg [31:0] pid_d_y2,    // Y2通道微分参数
    output reg [31:0] pid_p_z1,    // Z1通道比例参数
    output reg [31:0] pid_i_z1,    // Z1通道积分参数
    output reg [31:0] pid_d_z1,    // Z1通道微分参数
    output reg [31:0] pid_p_z2,    // Z2通道比例参数
    output reg [31:0] pid_i_z2,    // Z2通道积分参数
    output reg [31:0] pid_d_z2,    // Z2通道微分参数
    output reg [7:0]  pid_setting_index,   // PID参数设置索引
    
    // ADC/DAC偏值
    output reg [23:0] adc_offset_x1,      // X1通道ADC偏值
    output reg [23:0] adc_offset_x2,      // X2通道ADC偏值
    output reg [23:0] adc_offset_y1,      // Y1通道ADC偏值
    output reg [23:0] adc_offset_y2,      // Y2通道ADC偏值
    output reg [23:0] adc_offset_z1,      // Z1通道ADC偏值
    output reg [23:0] adc_offset_z2,      // Z2通道ADC偏值
    output reg [23:0] dac_offset_x1,      // X1通道DAC偏值
    output reg [23:0] dac_offset_x2,      // X2通道DAC偏值
    output reg [23:0] dac_offset_y1,      // Y1通道DAC偏值
    output reg [23:0] dac_offset_y2,      // Y2通道DAC偏值
    output reg [23:0] dac_offset_z1,      // Z1通道DAC偏值
    output reg [23:0] dac_offset_z2,      // Z2通道DAC偏值
    output reg [7:0]  offset_setting_index,// 偏值设置索引
    
    // 相位参数
    output reg [15:0] phase_x1,    // X1通道相位参数
    output reg [15:0] phase_x2,    // X2通道相位参数
    output reg [15:0] phase_y1,    // Y1通道相位参数
    output reg [15:0] phase_y2,    // Y2通道相位参数
    output reg [15:0] phase_z1,    // Z1通道相位参数
    output reg [15:0] phase_z2,    // Z2通道相位参数
    
    // 阈值参数
    output reg [23:0] vinct1_threshold,  // Vinct1阈值
    output reg [23:0] vinct2_threshold,  // Vinct2阈值
    output reg [23:0] vinct3_threshold,  // Vinct3阈值
    output reg [15:0] t1_threshold,      // t1阈值
    output reg [15:0] t3_threshold,      // t3阈值
    output reg [23:0] integral_threshold, // 积分阈值
    
    // 命令和状态计数
    output reg [7:0] correct_cmd_cnt,     // 正确指令计数
    output reg [7:0] last_correct_cmd,    // 最后正确指令类型
    output reg [7:0] error_cmd_cnt,       // 错误指令计数
    output reg [7:0] last_error_cmd,      // 最后错误指令类型
    output reg [7:0] error_type,          // 指令出错类型
    output reg [7:0] uart_reset_cnt,      // 异步串口复位计数
    output reg [15:0] program_pkg_cnt,    // 上注包计数
    output reg [7:0] dsp_version,         // DSP程序版本
    output reg [15:0] self_check_status,  // 自检状态
    
    // 遥测请求
    output reg telemetry_req              // 遥测请求信号
);

    // ================== 常量定义 ==================
    // 帧结构参数
    localparam FRAME_HEAD       = 16'hEB90;  // 帧头
    localparam FRAME_TAIL       = 16'h09D7;  // 帧尾
    
    // 接收缓冲区大小
    localparam RX_BUFFER_SIZE   = 128;       // 接收缓冲区大小
    localparam RX_BUFFER_MASK   = 8'h7F;      // 接收缓冲区掩码
    
    // 命令相关常量
    localparam TELEMETRY_REQ_TYPE = 8'h08;  // 遥测请求服务类型
    localparam TELEMETRY_REQ_ID   = 8'h00;  // 遥测请求标识
    localparam PROGRAM_UPLOAD_TYPE = 8'h0F; // 程序上注服务类型
    
    // 静电悬浮加速度计命令代码
    localparam CMD_SW_INJECT_REQ  = 8'h01;  // 软件请求注入指令
    localparam CMD_SW_INJECT_END  = 8'h02;  // 软件注入结束指令
    localparam CMD_START_ONLINE   = 8'h03;  // 启动在线编程指令
    localparam CMD_LARGE_RANGE    = 8'h04;  // 大量程模式切换指令
    localparam CMD_SMALL_RANGE    = 8'h05;  // 小量程模式切换指令
    localparam CMD_SW_RESET       = 8'h06;  // 软件复位指令
    localparam CMD_PROGRAM_MODE   = 8'h07;  // 切换至上注模式指令
    localparam CMD_DAC_SETTING    = 8'h0A;  // DAC设置指令
    localparam CMD_PHASE_SETTING  = 8'h0B;  // 相敏解调设置指令
    localparam CMD_OFFSET_SETTING = 8'h0C;  // 偏值设置指令
    localparam CMD_THRESHOLD_SET  = 8'h0D;  // 阈值设置指令
    localparam CMD_PID_PARAM_SET  = 8'h0E;  // PID控制参数注入指令
    localparam CMD_PROGRAM_PKG    = 8'h0F;  // 程序上注数据包
    
    // ================== 内部信号和寄存器 ==================
    // UART接口信号
    reg cs_n;                      // 片选信号
    reg re_n;                      // 读使能信号
    wire [7:0] rx_data;            // 接收数据
    wire rxrdy;                    // 接收就绪标志
    wire parity_err;               // 奇偶校验错误标志
    reg rxrdy_d1;                  // 寄存器捕获rxrdy的上一个状态
    wire rxrdy_posedge;            // rxrdy的上升沿脉冲信号
    assign rxrdy_posedge = rxrdy & ~rxrdy_d1;

    // 接收状态机
    reg [3:0] rx_state;            // 接收状态
    reg [7:0] rx_byte_cnt;         // 接收字节计数
    reg [7:0] rx_buffer[0:RX_BUFFER_SIZE-1]; // 接收缓冲区
    reg [7:0] rx_head;             // 接收缓冲区头指针
    reg [7:0] rx_tail;             // 接收缓冲区尾指针
    reg [7:0] data_length_h;       // 数据长度高字节
    reg [7:0] data_length_l;       // 数据长度低字节
    reg [15:0] data_length;        // 数据长度
    reg [15:0] checksum;           // 校验和
    reg [7:0] cmd_service_type;    // 服务类型
    reg [7:0] cmd_service_status;  // 服务状态
    reg [7:0] cmd_id;              // 命令标识
    reg [7:0] cmd_length;          // 命令长度
    reg [7:0] cmd_data[0:127];     // 命令数据缓存数组
    
    // 状态机状态定义
    localparam RX_IDLE      = 4'h0; // 空闲状态
    localparam RX_HEAD_1    = 4'h1; // 帧头1
    localparam RX_HEAD_2    = 4'h2; // 帧头2
    localparam RX_LEN_1     = 4'h3; // 长度1
    localparam RX_LEN_2     = 4'h4; // 长度2
    localparam RX_SERVICE   = 4'h5; // 服务类型
    localparam RX_STATUS    = 4'h6; // 服务状态
    localparam RX_DATA      = 4'h7; // 数据域
    localparam RX_CHECK_1   = 4'h8; // 校验码1
    localparam RX_CHECK_2   = 4'h9; // 校验码2
    localparam RX_TAIL_1    = 4'hA; // 帧尾1
    localparam RX_TAIL_2    = 4'hB; // 帧尾2
    localparam RX_PROCESS   = 4'hC; // 处理命令
    
    // ================== UART实例化 ==================
    COREUART uart_inst (
        .clk(clk),
        .rst_n(rst_n),
        
        .cs_n(cs_n),
        .baud_val(baud_val),
        .bit8(1'b1),        // 8位数据位
        .parity_en(1'b1),   // 使能奇偶校验
        .odd_n_even(1'b1),  // 奇校验
        
        .re_n(re_n),        // 读使能信号
        .rx(rx),            // 接收线
        .rxrdy(rxrdy),      // 接收就绪标志
        .data_out(rx_data), // 接收数据
        .parity_err(parity_err), // 奇偶校验错误
        
        .we_n(1'b1),        // 禁用发送功能
        .data_in(),         // 发送数据不使用
        .txrdy(),           // 发送就绪标志不使用
        .tx(),              // 发送线不使用
        .overflow()         // 溢出标志不使用
    );
    
    // 寄存器更新逻辑，保存rxrdy的上一个状态
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            rxrdy_d1 <= 1'b0;  
        end
        else begin  
            rxrdy_d1 <= rxrdy;  // 保存当前状态
        end
    end

    // ================== 接收状态机 ==================
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            // 复位初始化
            // 状态机初始状态
            rx_state <= RX_IDLE;
            rx_byte_cnt <= 8'h00;
            cs_n <= 1'b0;
            re_n <= 1'b1;
            rx_head <= 8'h00;
            rx_tail <= 8'h00;
            telemetry_req <= 1'b0;
            
            // 默认工作模式为捕获模式
            work_mode <= 8'hAA;
            
              // ADC/DAC偏值初始化
            adc_offset_x1 <= 24'h000000;
            adc_offset_x2 <= 24'h000000;
            adc_offset_y1 <= 24'h000000;
            adc_offset_y2 <= 24'h000000;
            adc_offset_z1 <= 24'h000000;
            adc_offset_z2 <= 24'h000000;
            dac_offset_x1 <= 24'h000000;
            dac_offset_x2 <= 24'h000000;
            dac_offset_y1 <= 24'h000000;
            dac_offset_y2 <= 24'h000000;
            dac_offset_z1 <= 24'h000000;
            dac_offset_z2 <= 24'h000000;
            offset_setting_index <= 8'h00;
                        
            // 阈值参数初始化
            vinct1_threshold <= 24'h020000;  // 设置默认阈值
            vinct2_threshold <= 24'h010000;
            vinct3_threshold <= 24'h008000;
            t1_threshold <= 16'h03E8;      // 1000
            t3_threshold <= 16'h03E8;      // 1000
            integral_threshold <= 24'h040000;

            // PID参数默认值
            pid_p_x1 <= 32'h00010000;
            pid_i_x1 <= 32'h00002000;
            pid_d_x1 <= 32'h00000800;
            pid_p_x2 <= 32'h00010000;
            pid_i_x2 <= 32'h00002000;
            pid_d_x2 <= 32'h00000800;
            pid_p_y1 <= 32'h00010000;
            pid_i_y1 <= 32'h00002000;
            pid_d_y1 <= 32'h00000800;
            pid_p_y2 <= 32'h00010000;
            pid_i_y2 <= 32'h00002000;
            pid_d_y2 <= 32'h00000800;
            pid_p_z1 <= 32'h00010000;
            pid_i_z1 <= 32'h00002000;
            pid_d_z1 <= 32'h00000800;
            pid_p_z2 <= 32'h00010000;
            pid_i_z2 <= 32'h00002000;
            pid_d_z2 <= 32'h00000800;
            pid_setting_index <= 8'h00;
            
            // 命令和状态计数初始值
            correct_cmd_cnt <= 8'h00;
            last_correct_cmd <= 8'h00;
            error_cmd_cnt <= 8'h00;
            last_error_cmd <= 8'h00;
            error_type <= 8'h00;
            uart_reset_cnt <= 8'h00;
            program_pkg_cnt <= 16'h0000;
            dsp_version <= 8'h01;
            self_check_status <= 16'h0000;
        end
        else begin
            // 处理UART接收
            if (rxrdy && !re_n) begin
                // 接收到数据，存入缓冲区
                rx_buffer[rx_head] <= rx_data;
                rx_head <= (rx_head + 1'b1) & RX_BUFFER_MASK;
                re_n <= 1'b1;
            end
            else if (rxrdy_posedge) begin
                // 准备接收新数据
                re_n <= 1'b0;
            end
            
            // 清除遥测请求标志
            if (telemetry_req) begin
                telemetry_req <= 1'b0;
            end
            
            // 接收状态机
            case (rx_state)
                RX_IDLE: begin
                    // 检查是否有数据可以处理
                    if (rx_head != rx_tail) begin
                        // 检查帧头第一个字节
                        if (rx_buffer[rx_tail] == FRAME_HEAD[15:8]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_HEAD_2;
                        end
                        else begin
                            // 无效数据，跳过
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        end
                    end
                end
                
                RX_HEAD_2: begin
                    if (rx_head != rx_tail) begin
                        if (rx_buffer[rx_tail] == FRAME_HEAD[7:0]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_LEN_1;
                        end
                        else begin
                            // 无效帧头，返回空闲状态
                            rx_state <= RX_IDLE;
                        end
                    end
                end
                
                RX_LEN_1: begin
                    if (rx_head != rx_tail) begin
                        data_length_h <= rx_buffer[rx_tail];
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        rx_state <= RX_LEN_2;
                    end
                end
                
                RX_LEN_2: begin
                    if (rx_head != rx_tail) begin
                        data_length_l <= rx_buffer[rx_tail];
                        data_length <= {data_length_h, rx_buffer[rx_tail]};
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        rx_state <= RX_SERVICE;
                        
                        // 初始化校验和计算
                        checksum <= {data_length_h, rx_buffer[rx_tail]};
                    end
                end
                
                RX_SERVICE: begin
                    if (rx_head != rx_tail) begin
                        cmd_service_type <= rx_buffer[rx_tail];
                        checksum <= checksum + rx_buffer[rx_tail];
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        rx_state <= RX_STATUS;
                    end
                end
                
                RX_STATUS: begin
                    if (rx_head != rx_tail) begin
                        cmd_service_status <= rx_buffer[rx_tail];
                        checksum <= checksum + rx_buffer[rx_tail];
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        rx_byte_cnt <= 8'h00;
                        rx_state <= RX_DATA;
                    end
                end
                
                RX_DATA: begin
                    if (rx_head != rx_tail) begin
                        // 根据数据索引存储不同字段
                        if (rx_byte_cnt == 8'h00) begin
                            cmd_id <= rx_buffer[rx_tail];
                        end
                        else if (rx_byte_cnt == 8'h01) begin
                            cmd_length <= rx_buffer[rx_tail];
                        end
                        else begin
                            // 其他数据字段暂存在接收缓冲区中
                            if (rx_byte_cnt >= 2) begin
                                // 从第三个字节开始存储到cmd_data数组
                                cmd_data[rx_byte_cnt - 2] <= rx_buffer[rx_tail];
                            end
                        end
                        
                        // 更新校验和
                        checksum <= checksum + rx_buffer[rx_tail];
                        
                        // 增加已接收字节计数
                        rx_byte_cnt <= rx_byte_cnt + 1'b1;
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        
                        // 检查是否接收完所有数据字段
                        if (rx_byte_cnt == data_length - 16'h0003) begin
                            rx_state <= RX_CHECK_1;
                        end
                    end
                end
                
                RX_CHECK_1: begin
                    if (rx_head != rx_tail) begin
                        // 收到校验和高字节
                        if (rx_buffer[rx_tail] == checksum[15:8]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_CHECK_2;
                        end
                        else begin
                            // 校验和错误
                            error_cmd_cnt <= error_cmd_cnt + 1'b1;
                            last_error_cmd <= cmd_id;
                            error_type <= 8'h01; // 校验错误类型
                            rx_state <= RX_IDLE;
                        end
                    end
                end
                
                RX_CHECK_2: begin
                    if (rx_head != rx_tail) begin
                        // 收到校验和低字节
                        if (rx_buffer[rx_tail] == checksum[7:0]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_TAIL_1;
                        end
                        else begin
                            // 校验和错误
                            error_cmd_cnt <= error_cmd_cnt + 1'b1;
                            last_error_cmd <= cmd_id;
                            error_type <= 8'h01; // 校验错误类型
                            rx_state <= RX_IDLE;
                        end
                    end
                end
                
                RX_TAIL_1: begin
                    if (rx_head != rx_tail) begin
                        if (rx_buffer[rx_tail] == FRAME_TAIL[15:8]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_TAIL_2;
                        end
                        else begin
                            // 帧尾错误
                            rx_state <= RX_IDLE;
                        end
                    end
                end
                
                RX_TAIL_2: begin
                    if (rx_head != rx_tail) begin
                        if (rx_buffer[rx_tail] == FRAME_TAIL[7:0]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_PROCESS;
                        end
                        else begin
                            // 帧尾错误
                            rx_state <= RX_IDLE;
                        end
                    end
                end
                
                RX_PROCESS: begin
                    // 处理完整接收到的命令
                    process_command();
                    rx_state <= RX_IDLE;
                end
                
                default: begin
                    rx_state <= RX_IDLE;
                end
            endcase
        end
    end
    
    // ================== 命令处理任务 ==================
    task process_command;
    begin
        // 检查服务类型和命令类型
        if (cmd_service_type == TELEMETRY_REQ_TYPE && cmd_id == TELEMETRY_REQ_ID) begin
            // 遥测请求
            telemetry_req <= 1'b1;
            correct_cmd_cnt <= correct_cmd_cnt + 1'b1;
            last_correct_cmd <= cmd_id;
        end
        else if (cmd_service_type == TELEMETRY_REQ_TYPE) begin
            // 遥控指令（服务类型0x08）
            case (cmd_id)
                CMD_LARGE_RANGE: begin
                    // 大量程模式切换
                    work_mode <= 8'h55;
                    correct_cmd_cnt <= correct_cmd_cnt + 1'b1;
                    last_correct_cmd <= cmd_id;
                end
                
                CMD_SMALL_RANGE: begin
                    // 小量程模式切换
                    work_mode <= 8'h33;
                    correct_cmd_cnt <= correct_cmd_cnt + 1'b1;
                    last_correct_cmd <= cmd_id;
                end
                
                CMD_PROGRAM_MODE: begin
                    // 上注模式切换
                    work_mode <= 8'h11;
                    correct_cmd_cnt <= correct_cmd_cnt + 1'b1;
                    last_correct_cmd <= cmd_id;
                end
                
                CMD_SW_RESET: begin
                    // 软件复位
                    // 仅增加命令计数，实际复位由主控实现
                    correct_cmd_cnt <= correct_cmd_cnt + 1'b1;
                    last_correct_cmd <= cmd_id;
                end
                
                CMD_SW_INJECT_REQ, CMD_SW_INJECT_END, CMD_START_ONLINE: begin
                    // 程序上注相关命令，不实现具体功能
                    program_pkg_cnt <= program_pkg_cnt + 1'b1;
                    correct_cmd_cnt <= correct_cmd_cnt + 1'b1;
                    last_correct_cmd <= cmd_id;
                end
                
                CMD_DAC_SETTING: begin
                    // DAC设置指令处理
                    case (cmd_data[0])
                        8'h11: begin // X1通道
                            // 组合24位设置值
                            dac_offset_x1 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                        end
                        8'h22: begin // X2通道
                            dac_offset_x2 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                        end
                        8'h33: begin // Y1通道
                            dac_offset_y1 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                        end
                        8'h44: begin // Y2通道
                            dac_offset_y2 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                        end
                        8'h55: begin // Z1通道
                            dac_offset_z1 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                        end
                        8'h66: begin // Z2通道
                            dac_offset_z2 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                        end
                    endcase
                    correct_cmd_cnt <= correct_cmd_cnt + 1'b1;
                    last_correct_cmd <= cmd_id;
                end
                
                CMD_PHASE_SETTING: begin
                    // 相敏解调设置指令处理
                    case (cmd_data[0])
                        8'h11: phase_x1 <= {cmd_data[1], cmd_data[2]};
                        8'h22: phase_x2 <= {cmd_data[1], cmd_data[2]};
                        8'h33: phase_y1 <= {cmd_data[1], cmd_data[2]};
                        8'h44: phase_y2 <= {cmd_data[1], cmd_data[2]};
                        8'h55: phase_z1 <= {cmd_data[1], cmd_data[2]};
                        8'h66: phase_z2 <= {cmd_data[1], cmd_data[2]};
                    endcase
                    correct_cmd_cnt <= correct_cmd_cnt + 1'b1;
                    last_correct_cmd <= cmd_id;
                end
                
                CMD_OFFSET_SETTING: begin
                    // 偏值设置指令处理
                    case (cmd_data[0])
                        8'h11: begin // X1通道ADC
                            adc_offset_x1 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                            offset_setting_index <= 8'h00;
                        end
                        8'h22: begin // X2通道ADC
                            adc_offset_x2 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                            offset_setting_index <= 8'h01;
                        end
                        8'h33: begin // Y1通道ADC
                            adc_offset_y1 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                            offset_setting_index <= 8'h00;
                        end
                        8'h44: begin // Y2通道ADC
                            adc_offset_y2 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                            offset_setting_index <= 8'h01;
                        end
                        8'h55: begin // Z1通道ADC
                            adc_offset_z1 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                            offset_setting_index <= 8'h00;
                        end
                        8'h66: begin // Z2通道ADC
                            adc_offset_z2 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                            offset_setting_index <= 8'h01;
                        end
                        8'h88: begin // X1通道DAC
                            dac_offset_x1 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                            offset_setting_index <= 8'h02;
                        end
                        8'h99: begin // X2通道DAC
                            dac_offset_x2 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                            offset_setting_index <= 8'h03;
                        end
                        8'hAA: begin // Y1通道DAC
                            dac_offset_y1 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                            offset_setting_index <= 8'h02;
                        end
                        8'hBB: begin // Y2通道DAC
                            dac_offset_y2 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                            offset_setting_index <= 8'h03;
                        end
                        8'hCC: begin // Z1通道DAC
                            dac_offset_z1 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                            offset_setting_index <= 8'h02;
                        end
                        8'hDD: begin // Z2通道DAC
                            dac_offset_z2 <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                            offset_setting_index <= 8'h03;
                        end
                    endcase
                    correct_cmd_cnt <= correct_cmd_cnt + 1'b1;
                    last_correct_cmd <= cmd_id;
                end
                
                CMD_THRESHOLD_SET: begin
                    // 阈值设置指令处理
                    case (cmd_data[0])
                        8'h11: vinct1_threshold <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                        8'h22: vinct2_threshold <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                        8'h33: vinct3_threshold <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                        8'h44: t1_threshold <= {cmd_data[1], cmd_data[2]};
                        8'h55: t3_threshold <= {cmd_data[1], cmd_data[2]};
                        8'h66: integral_threshold <= {cmd_data[1], cmd_data[2], cmd_data[3]};
                    endcase
                    correct_cmd_cnt <= correct_cmd_cnt + 1'b1;
                    last_correct_cmd <= cmd_id;
                end
                
                CMD_PID_PARAM_SET: begin
                    // PID控制参数注入指令处理
                    // cmd_data[0]是工作模式
                    // cmd_data[1:3]是检测电压Vd值
                    // cmd_data[4]是坐标轴选择
                    case (cmd_data[4]) // 坐标轴选择 (Byte12)
                        8'h11, 8'h44: begin // 设置或查看X轴参数
                            pid_setting_index <= 8'h00;
                            if (cmd_data[4] == 8'h11) begin // 设置参数
                                // X1路参数 (Byte13~Byte24)
                                pid_p_x1 <= {cmd_data[5], cmd_data[6], cmd_data[7], cmd_data[8]};  // Byte13~Byte16: X1路比例参数
                                pid_i_x1 <= {cmd_data[13], cmd_data[14], cmd_data[15], cmd_data[16]}; // Byte21~Byte24: X1路积分参数
                                pid_d_x1 <= {cmd_data[21], cmd_data[22], cmd_data[23], cmd_data[24]}; // Byte29~Byte32: X1路微分参数
                                
                                // X2路参数 (Byte17~Byte36)
                                pid_p_x2 <= {cmd_data[9], cmd_data[10], cmd_data[11], cmd_data[12]}; // Byte17~Byte20: X2路比例参数
                                pid_i_x2 <= {cmd_data[17], cmd_data[18], cmd_data[19], cmd_data[20]}; // Byte25~Byte28: X2路积分参数
                                pid_d_x2 <= {cmd_data[25], cmd_data[26], cmd_data[27], cmd_data[28]}; // Byte33~Byte36: X2路微分参数
                            end
                        end
                        8'h22, 8'h55: begin // 设置或查看Y轴参数
                            pid_setting_index <= 8'h01;
                            if (cmd_data[4] == 8'h22) begin // 设置参数
                                // Y1路参数
                                pid_p_y1 <= {cmd_data[5], cmd_data[6], cmd_data[7], cmd_data[8]};  // Y1路比例参数
                                pid_i_y1 <= {cmd_data[13], cmd_data[14], cmd_data[15], cmd_data[16]}; // Y1路积分参数
                                pid_d_y1 <= {cmd_data[21], cmd_data[22], cmd_data[23], cmd_data[24]}; // Y1路微分参数
                                
                                // Y2路参数
                                pid_p_y2 <= {cmd_data[9], cmd_data[10], cmd_data[11], cmd_data[12]}; // Y2路比例参数
                                pid_i_y2 <= {cmd_data[17], cmd_data[18], cmd_data[19], cmd_data[20]}; // Y2路积分参数
                                pid_d_y2 <= {cmd_data[25], cmd_data[26], cmd_data[27], cmd_data[28]}; // Y2路微分参数
                            end
                        end
                        8'h33, 8'h66: begin // 设置或查看Z轴参数
                            pid_setting_index <= 8'h02;
                            if (cmd_data[4] == 8'h33) begin // 设置参数
                                // Z1路参数
                                pid_p_z1 <= {cmd_data[5], cmd_data[6], cmd_data[7], cmd_data[8]};  // Z1路比例参数
                                pid_i_z1 <= {cmd_data[13], cmd_data[14], cmd_data[15], cmd_data[16]}; // Z1路积分参数
                                pid_d_z1 <= {cmd_data[21], cmd_data[22], cmd_data[23], cmd_data[24]}; // Z1路微分参数
                                
                                // Z2路参数
                                pid_p_z2 <= {cmd_data[9], cmd_data[10], cmd_data[11], cmd_data[12]}; // Z2路比例参数
                                pid_i_z2 <= {cmd_data[17], cmd_data[18], cmd_data[19], cmd_data[20]}; // Z2路积分参数
                                pid_d_z2 <= {cmd_data[25], cmd_data[26], cmd_data[27], cmd_data[28]}; // Z2路微分参数
                            end
                        end
                    endcase
                    correct_cmd_cnt <= correct_cmd_cnt + 1'b1;
                    last_correct_cmd <= cmd_id;
                end
                
                default: begin
                    // 未知命令类型
                    error_cmd_cnt <= error_cmd_cnt + 1'b1;
                    last_error_cmd <= cmd_id;
                    error_type <= 8'h02; // 未知命令类型
                end
            endcase
        end
        else if (cmd_service_type == PROGRAM_UPLOAD_TYPE && cmd_id == CMD_PROGRAM_PKG) begin
            // 程序上注数据包处理（服务类型0x0F）
            // cmd_id对应指令标识(Byte6)
            // cmd_data[0]对应程序上注包长度(Byte7)
            // cmd_data[1:2]对应上注包序号(Byte8~Byte9)
            program_pkg_cnt <= {cmd_data[1], cmd_data[2]};
            // cmd_data[3:4]对应程序代码总长度(Byte10~Byte11)
            // cmd_data[5:120]对应程序上注包内容(Byte12~Byte127)
            // 更新DSP程序版本
            dsp_version <= dsp_version + 1'b1;
            
            correct_cmd_cnt <= correct_cmd_cnt + 1'b1;
            last_correct_cmd <= cmd_id;
        end
        else begin
            // 未知服务类型
            error_cmd_cnt <= error_cmd_cnt + 1'b1;
            last_error_cmd <= cmd_id;
            error_type <= 8'h03; // 未知服务类型
        end
    end
    endtask

endmodule 