module acc_shared_state (
    input wire clk,                // 系统时钟
    input wire rst_n,              // 复位信号，低电平有效
    
    // 系统时间
    input wire [31:0] sys_time_s,  // 系统时间(秒)
    input wire [15:0] sys_time_ms, // 系统时间(毫秒)
    
    // 工作模式
    output reg [7:0] work_mode,    // 工作模式状态
    
    // PID参数
    output reg [31:0] pid_p_x1,    // X1通道比例参数
    output reg [31:0] pid_i_x1,    // X1通道积分参数
    output reg [31:0] pid_d_x1,    // X1通道微分参数
    output reg [31:0] pid_p_x2,    // X2通道比例参数
    output reg [31:0] pid_i_x2,    // X2通道积分参数
    output reg [31:0] pid_d_x2,    // X2通道微分参数
    output reg [31:0] pid_p_y1,    // Y1通道比例参数
    output reg [31:0] pid_i_y1,    // Y1通道积分参数
    output reg [31:0] pid_d_y1,    // Y1通道微分参数
    output reg [31:0] pid_p_y2,    // Y2通道比例参数
    output reg [31:0] pid_i_y2,    // Y2通道积分参数
    output reg [31:0] pid_d_y2,    // Y2通道微分参数
    output reg [31:0] pid_p_z1,    // Z1通道比例参数
    output reg [31:0] pid_i_z1,    // Z1通道积分参数
    output reg [31:0] pid_d_z1,    // Z1通道微分参数
    output reg [31:0] pid_p_z2,    // Z2通道比例参数
    output reg [31:0] pid_i_z2,    // Z2通道积分参数
    output reg [31:0] pid_d_z2,    // Z2通道微分参数
    output reg [7:0] pid_setting_index,   // PID参数设置索引
    
    // ADC/DAC偏值
    output reg [23:0] adc_offset_x1,      // X1通道ADC偏值
    output reg [23:0] adc_offset_x2,      // X2通道ADC偏值
    output reg [23:0] adc_offset_y1,      // Y1通道ADC偏值
    output reg [23:0] adc_offset_y2,      // Y2通道ADC偏值
    output reg [23:0] adc_offset_z1,      // Z1通道ADC偏值
    output reg [23:0] adc_offset_z2,      // Z2通道ADC偏值
    output reg [23:0] dac_offset_x1,      // X1通道DAC偏值
    output reg [23:0] dac_offset_x2,      // X2通道DAC偏值
    output reg [23:0] dac_offset_y1,      // Y1通道DAC偏值
    output reg [23:0] dac_offset_y2,      // Y2通道DAC偏值
    output reg [23:0] dac_offset_z1,      // Z1通道DAC偏值
    output reg [23:0] dac_offset_z2,      // Z2通道DAC偏值
    output reg [7:0] offset_setting_index, // 偏值设置索引
    
    // 相位参数
    output reg [15:0] phase_x1,    // X1通道相位参数
    output reg [15:0] phase_x2,    // X2通道相位参数
    output reg [15:0] phase_y1,    // Y1通道相位参数
    output reg [15:0] phase_y2,    // Y2通道相位参数
    output reg [15:0] phase_z1,    // Z1通道相位参数
    output reg [15:0] phase_z2,    // Z2通道相位参数
    
    // 阈值参数
    output reg [23:0] vinct1_threshold,  // Vinct1阈值
    output reg [23:0] vinct2_threshold,  // Vinct2阈值
    output reg [23:0] vinct3_threshold,  // Vinct3阈值
    output reg [15:0] t1_threshold,      // t1阈值
    output reg [15:0] t3_threshold,      // t3阈值
    output reg [23:0] integral_threshold, // 积分阈值
    
    // 命令和状态计数
    output reg [7:0] correct_cmd_cnt,     // 正确指令计数
    output reg [7:0] last_correct_cmd,    // 最后正确指令类型
    output reg [7:0] error_cmd_cnt,       // 错误指令计数
    output reg [7:0] last_error_cmd,      // 最后错误指令类型
    output reg [7:0] error_type,          // 指令出错类型
    output reg [7:0] uart_reset_cnt,      // 异步串口复位计数
    output reg [15:0] program_pkg_cnt,    // 上注包计数
    output reg [7:0] dsp_version,         // DSP程序版本
    output reg [15:0] self_check_status,  // 自检状态
    
    // 状态输出
    output reg [7:0] status                // 状态指示
);

    // =============== 初始化参数值 ===============
    // 按协议定义的默认值初始化所有参数
    
    // 参数初始化与状态更新
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            // 默认工作模式为捕获模式
            work_mode <= 8'hAA;
            
            // PID参数默认值设置
            pid_p_x1 <= 32'h00010000;
            pid_i_x1 <= 32'h00002000;
            pid_d_x1 <= 32'h00000800;
            pid_p_x2 <= 32'h00010000;
            pid_i_x2 <= 32'h00002000;
            pid_d_x2 <= 32'h00000800;
            pid_p_y1 <= 32'h00010000;
            pid_i_y1 <= 32'h00002000;
            pid_d_y1 <= 32'h00000800;
            pid_p_y2 <= 32'h00010000;
            pid_i_y2 <= 32'h00002000;
            pid_d_y2 <= 32'h00000800;
            pid_p_z1 <= 32'h00010000;
            pid_i_z1 <= 32'h00002000;
            pid_d_z1 <= 32'h00000800;
            pid_p_z2 <= 32'h00010000;
            pid_i_z2 <= 32'h00002000;
            pid_d_z2 <= 32'h00000800;
            pid_setting_index <= 8'h00;
            
            // ADC/DAC偏值默认值
            adc_offset_x1 <= 24'h000000;
            adc_offset_x2 <= 24'h000000;
            adc_offset_y1 <= 24'h000000;
            adc_offset_y2 <= 24'h000000;
            adc_offset_z1 <= 24'h000000;
            adc_offset_z2 <= 24'h000000;
            dac_offset_x1 <= 24'h000000;
            dac_offset_x2 <= 24'h000000;
            dac_offset_y1 <= 24'h000000;
            dac_offset_y2 <= 24'h000000;
            dac_offset_z1 <= 24'h000000;
            dac_offset_z2 <= 24'h000000;
            offset_setting_index <= 8'h00;
            
            // 相位参数默认值
            phase_x1 <= 16'h8000;
            phase_x2 <= 16'h8000;
            phase_y1 <= 16'h8000;
            phase_y2 <= 16'h8000;
            phase_z1 <= 16'h8000;
            phase_z2 <= 16'h8000;
            
            // 阈值参数默认值
            vinct1_threshold <= 24'h200000;
            vinct2_threshold <= 24'h100000;
            vinct3_threshold <= 24'h080000;
            t1_threshold <= 16'h0100;
            t3_threshold <= 16'h0200;
            integral_threshold <= 24'h010000;
            
            // 命令和状态计数初始值
            correct_cmd_cnt <= 8'h00;
            last_correct_cmd <= 8'h00;
            error_cmd_cnt <= 8'h00;
            last_error_cmd <= 8'h00;
            error_type <= 8'h00;
            uart_reset_cnt <= 8'h00;
            program_pkg_cnt <= 16'h0000;
            dsp_version <= 8'h01;      // 初始版本号为1
            self_check_status <= 16'h0000;  // 初始自检状态为正常
            
            // 状态指示初始值
            status <= 8'h00;
        end
        else begin
            // 正常运行时的状态更新逻辑
            // 此处可添加其他状态更新代码
            // 简单的状态指示更新示例
            if (work_mode == 8'hAA) begin
                // 捕获模式
                status <= 8'h01;
            end
            else if (work_mode == 8'h55) begin
                // 大量程模式
                status <= 8'h02;
            end
            else if (work_mode == 8'h33) begin
                // 小量程模式
                status <= 8'h03;
            end
            else if (work_mode == 8'h11) begin
                // 程序上注模式
                status <= 8'h04;
            end
            else begin
                // 未知模式
                status <= 8'h00;
            end
        end
    end
        
endmodule 