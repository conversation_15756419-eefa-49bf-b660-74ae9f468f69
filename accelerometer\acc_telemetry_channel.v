module acc_telemetry_channel (
    input wire clk,                // 系统时钟
    input wire rst_n,              // 复位信号，低电平有效
    input wire [7:0] baud_val,     // 波特率配置
    
    // 遥测请求触发
    input wire telemetry_req,      // 遥测请求触发信号
    
    // 系统时间
    input wire [31:0] sys_time_s,  // 系统时间(秒)
    input wire [15:0] sys_time_ms, // 系统时间(毫秒)
    
    // 状态参数
    input wire [7:0] work_mode,    // 工作模式状态
    
    // PID参数
    input wire [31:0] pid_p_x1,    // X1通道比例参数
    input wire [31:0] pid_i_x1,    // X1通道积分参数
    input wire [31:0] pid_d_x1,    // X1通道微分参数
    input wire [31:0] pid_p_x2,    // X2通道比例参数
    input wire [31:0] pid_i_x2,    // X2通道积分参数
    input wire [31:0] pid_d_x2,    // X2通道微分参数
    input wire [31:0] pid_p_y1,    // Y1通道比例参数
    input wire [31:0] pid_i_y1,    // Y1通道积分参数
    input wire [31:0] pid_d_y1,    // Y1通道微分参数
    input wire [31:0] pid_p_y2,    // Y2通道比例参数
    input wire [31:0] pid_i_y2,    // Y2通道积分参数
    input wire [31:0] pid_d_y2,    // Y2通道微分参数
    input wire [31:0] pid_p_z1,    // Z1通道比例参数
    input wire [31:0] pid_i_z1,    // Z1通道积分参数
    input wire [31:0] pid_d_z1,    // Z1通道微分参数
    input wire [31:0] pid_p_z2,    // Z2通道比例参数
    input wire [31:0] pid_i_z2,    // Z2通道积分参数
    input wire [31:0] pid_d_z2,    // Z2通道微分参数
    input wire [7:0] pid_setting_index,   // PID参数设置索引
    
    // ADC/DAC偏值
    input wire [23:0] adc_offset_x1,      // X1通道ADC偏值
    input wire [23:0] adc_offset_x2,      // X2通道ADC偏值
    input wire [23:0] adc_offset_y1,      // Y1通道ADC偏值
    input wire [23:0] adc_offset_y2,      // Y2通道ADC偏值
    input wire [23:0] adc_offset_z1,      // Z1通道ADC偏值
    input wire [23:0] adc_offset_z2,      // Z2通道ADC偏值
    input wire [23:0] dac_offset_x1,      // X1通道DAC偏值
    input wire [23:0] dac_offset_x2,      // X2通道DAC偏值
    input wire [23:0] dac_offset_y1,      // Y1通道DAC偏值
    input wire [23:0] dac_offset_y2,      // Y2通道DAC偏值
    input wire [23:0] dac_offset_z1,      // Z1通道DAC偏值
    input wire [23:0] dac_offset_z2,      // Z2通道DAC偏值
    input wire [7:0] offset_setting_index, // 偏值设置索引
           
    // 阈值参数
    input wire [23:0] vinct1_threshold,  // Vinct1阈值
    input wire [23:0] vinct2_threshold,  // Vinct2阈值
    input wire [23:0] vinct3_threshold,  // Vinct3阈值
    input wire [15:0] t1_threshold,      // t1阈值
    input wire [15:0] t3_threshold,      // t3阈值
    input wire [23:0] integral_threshold, // 积分阈值
    
    // 命令和状态计数
    input wire [7:0] correct_cmd_cnt,     // 正确指令计数
    input wire [7:0] last_correct_cmd,    // 最后正确指令类型
    input wire [7:0] error_cmd_cnt,       // 错误指令计数
    input wire [7:0] last_error_cmd,      // 最后错误指令类型
    input wire [7:0] error_type,          // 指令出错类型
    input wire [7:0] uart_reset_cnt,      // 异步串口复位计数
    input wire [15:0] program_pkg_cnt,    // 上注包计数
    input wire [7:0] dsp_version,         // DSP程序版本
    input wire [15:0] self_check_status,  // 自检状态
    
    // UART输出
    output wire tx                 // RS422发送线
);

    // ================== 内部参数定义 ==================
    // 遥测数据包定义，固定为156字节
    localparam TELEMETRY_DATA_SIZE = 156;
    
    // 遥测数据包字段位置定义
    localparam FRAME_HEAD_POS      = 0;  // 帧头位置(2字节)
    localparam DATA_LEN_POS        = 2;  // 数据长度位置(2字节)
    localparam SERVICE_TYPE_POS    = 4;  // 服务类型位置(1字节)
    localparam SERVICE_STATUS_POS  = 5;  // 服务状态位置(1字节)
    localparam FRAME_CNT_POS       = 6;  // 帧计数位置(3字节)
    localparam USEC_CNT_POS        = 9;  // 微秒计数位置(3字节)
    localparam SEC_CNT_POS         = 12; // 秒计数位置(4字节)
    localparam WORK_MODE_POS       = 16; // 工作模式位置(1字节)
    localparam TEMP_DATA_POS       = 17; // 温度数据起始位置(4*3=12字节)
    localparam POSITION_DATA_POS   = 29; // 位移数据起始位置(6*3=18字节)
    localparam ACCEL_DATA_POS      = 47; // 加速度数据起始位置(6*3=18字节)
    localparam PHASE_DATA_POS      = 65; // 相位数据起始位置(6*2=12字节)
    localparam ADC_DAC_OFFSET_POS  = 77; // ADC/DAC偏值起始位置(6*3=18字节)
    localparam OFFSET_INDEX_POS    = 95; // 偏值设置索引位置(1字节)
    localparam PID_PARAM_POS       = 96; // PID参数起始位置(24字节)
    localparam PID_INDEX_POS       = 120; // PID参数设置索引位置(1字节)
    localparam VD_VALUE_POS        = 121; // Vd检测电压值位置(3字节)
    localparam VINCT_THRESHOLD_POS = 124; // Vinct阈值起始位置(9字节)
    localparam T_THRESHOLD_POS     = 133; // 时间阈值起始位置(4字节)
    localparam INTEGRAL_THRESHOLD_POS = 137; // 积分阈值位置(3字节)
    localparam PROGRAM_PKG_CNT_POS = 140; // 上注包计数位置(2字节)
    localparam DSP_VERSION_POS     = 142; // DSP程序版本位置(1字节)
    localparam SELF_CHECK_POS      = 143; // 自检状态位置(2字节)
    localparam CMD_COUNT_POS       = 145; // 命令计数起始位置(6字节)
    localparam RESERVED_BYTE_POS   = 151; // 保留字节位置(1字节)
    localparam CHECKSUM_POS        = 152; // 校验码位置(2字节)
    localparam FRAME_TAIL_POS      = 154; // 帧尾位置(2字节)
    
    // 固定参数值
    localparam [15:0] FRAME_HEAD   = 16'hEB90; // 帧头
    localparam [15:0] DATA_LEN     = 16'h0094; // 数据长度(148)
    localparam [7:0] SERVICE_TYPE  = 8'h01;    // 服务类型(遥测信息)
    localparam [7:0] SUCCESS_STATUS = 8'h55;   // 成功状态
    localparam [7:0] FAILURE_STATUS = 8'hAA;   // 失败状态
    localparam [15:0] FRAME_TAIL   = 16'h09D7; // 帧尾
    
    // UART通信参数
    reg cs_n;                      // 片选信号
    reg we_n;                      // 写使能信号
    reg [7:0] data_in;             // 待写数据
    wire txrdy;                    // 发送就绪标志
    reg txrdy_prev;
    wire txrdy_rf;
    assign txrdy_rf = txrdy & ~txrdy_prev;
    
    // ================== 内部寄存器定义 ==================
    reg [2:0] state;               // 发送状态机状态
    reg [23:0] frame_counter;      // 帧计数器
    reg [7:0] send_index;          // 发送索引
    
    // 遥测数据包缓冲区
    reg [7:0] telemetry_data [0:TELEMETRY_DATA_SIZE-1];
    
    // 科学数据缓冲区 
    reg [23:0] temp_data [0:3];       // 4个温度数据
    reg [23:0] position_data [0:5];   // 6个通道的位移数据
    reg [23:0] accel_data [0:5];      // 6个通道的加速度数据
    reg [15:0] phase_data [0:5];      // 6个通道的相位数据
    
    // 发送状态机状态定义
    localparam IDLE      = 3'b000; // 空闲状态
    localparam BUILD_PKG = 3'b001; // 构建数据包
    localparam SEND_PKG  = 3'b010; // 发送数据包
    localparam WAIT_DONE = 3'b011; // 等待发送完成
    
    // ================== UART实例化 ==================
    // 使用COREUART模块发送遥测数据
    COREUART uart_inst (
        .clk(clk),
        .rst_n(rst_n),
        
        .cs_n(cs_n),
        .baud_val(baud_val),
        .bit8(1'b1),        // 8位数据位
        .parity_en(1'b1),   // 使能奇偶校验
        .odd_n_even(1'b1),  // 奇校验
        
        .we_n(we_n),        // 写使能信号
        .data_in(data_in),  // 待写数据
        .txrdy(txrdy),      // 发送就绪标志
        .tx(tx),            // 发送数据线
        
        .re_n(1'b1),        // 禁用接收功能
        .rx(),              // 接收线不使用
        .rxrdy(),
        .data_out(),
        .parity_err(),
        .overflow()
    );
    
    // 寄存器更新逻辑，保存txrdy的上一个状态
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n)
            txrdy_prev <= 1'b0;
        else
            txrdy_prev <= txrdy;
    end
    // ================== 遥测数据包构建和发送状态机 ==================
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            state <= IDLE;
            frame_counter <= 24'd0;
            send_index <= 8'd0;
            cs_n <= 1'b0;
            we_n <= 1'b1;
            data_in <= 8'h00;
        end
        else begin
            case (state)
                IDLE: begin
                    // 等待遥测请求触发
                    if (telemetry_req) begin
                        state <= BUILD_PKG;
                    end
                    else begin
                        state <= IDLE;
                    end
                end
                
                BUILD_PKG: begin
                    // 构建遥测数据包
                    build_telemetry_package();
                    
                    // 更新帧计数
                    frame_counter <= frame_counter + 1'b1;
                    
                    // 准备发送
                    send_index <= 8'd0;
                    state <= SEND_PKG;
                end
                
                SEND_PKG: begin
                    // 发送遥测数据包
                    if (send_index < TELEMETRY_DATA_SIZE) begin
                        if (send_index == 8'd0) begin
                            if (txrdy) begin
                                // UART就绪，发送下一个字节
                                we_n <= 1'b0;
                                data_in <= telemetry_data[send_index];
                                send_index <= send_index + 1'b1;
                            end
                            else begin
                                // UART忙，等待就绪
                                we_n <= 1'b1;
                            end
                        end
                        else begin
                            if (txrdy_rf) begin
                                we_n <= 1'b0;
                                data_in <= telemetry_data[send_index];
                                send_index <= send_index + 1'b1;
                            end
                            else begin
                                // UART忙，等待就绪
                                we_n <= 1'b1;
                            end
                        end
                    end
                    else begin
                        // 所有数据已发送
                        we_n <= 1'b1;
                        state <= WAIT_DONE;
                    end
                end
                
                WAIT_DONE: begin
                    // 等待一段时间后回到空闲状态
                    state <= IDLE;
                end
                
                default: begin
                    state <= IDLE;
                end
            endcase
        end
    end
    
    // ================== 遥测数据包构建任务 ==================
    // 构建完整的156字节遥测数据包
    task build_telemetry_package;
        integer i;
        reg [15:0] checksum;
    begin
        // 根据工作模式生成模拟数据（温度、位移、加速度和相位数据）
        generate_sensor_data();
        
        // 1. 帧头 (2字节)
        telemetry_data[FRAME_HEAD_POS] = FRAME_HEAD[15:8];
        telemetry_data[FRAME_HEAD_POS+1] = FRAME_HEAD[7:0];
        
        // 2. 数据长度 (2字节)
        telemetry_data[DATA_LEN_POS] = DATA_LEN[15:8];
        telemetry_data[DATA_LEN_POS+1] = DATA_LEN[7:0];
        
        // 3. 服务类型 (1字节)
        telemetry_data[SERVICE_TYPE_POS] = SERVICE_TYPE;
        
        // 4. 服务状态 (1字节) - 根据错误状态设置成功/失败
        if (error_type == 8'h00) begin
            telemetry_data[SERVICE_STATUS_POS] = SUCCESS_STATUS;
        end
        else begin
            telemetry_data[SERVICE_STATUS_POS] = FAILURE_STATUS;
        end
        
        // 5. 帧计数 (3字节)
        telemetry_data[FRAME_CNT_POS] = frame_counter[23:16];
        telemetry_data[FRAME_CNT_POS+1] = frame_counter[15:8];
        telemetry_data[FRAME_CNT_POS+2] = frame_counter[7:0];
        
        // 6. 微秒计数 (3字节)
        telemetry_data[USEC_CNT_POS] = sys_time_ms[15:8];
        telemetry_data[USEC_CNT_POS+1] = sys_time_ms[7:0];
        telemetry_data[USEC_CNT_POS+2] = 8'h00; // 微秒低8位简化为0
        
        // 7. 秒计数 (4字节)
        telemetry_data[SEC_CNT_POS] = sys_time_s[31:24];
        telemetry_data[SEC_CNT_POS+1] = sys_time_s[23:16];
        telemetry_data[SEC_CNT_POS+2] = sys_time_s[15:8];
        telemetry_data[SEC_CNT_POS+3] = sys_time_s[7:0];
        
        // 8. 工作模式 (1字节)
        telemetry_data[WORK_MODE_POS] = work_mode;
        
        // 9. 温度数据 (4*3=12字节)
        for (i = 0; i < 4; i = i + 1) begin
            telemetry_data[TEMP_DATA_POS+i*3] = temp_data[i][23:16];
            telemetry_data[TEMP_DATA_POS+i*3+1] = temp_data[i][15:8];
            telemetry_data[TEMP_DATA_POS+i*3+2] = temp_data[i][7:0];
        end
        
        // 10. 位移数据 (6*3=18字节)
        for (i = 0; i < 6; i = i + 1) begin
            telemetry_data[POSITION_DATA_POS+i*3] = position_data[i][23:16];
            telemetry_data[POSITION_DATA_POS+i*3+1] = position_data[i][15:8];
            telemetry_data[POSITION_DATA_POS+i*3+2] = position_data[i][7:0];
        end
        
        // 11. 加速度数据 (6*3=18字节)
        for (i = 0; i < 6; i = i + 1) begin
            telemetry_data[ACCEL_DATA_POS+i*3] = accel_data[i][23:16];
            telemetry_data[ACCEL_DATA_POS+i*3+1] = accel_data[i][15:8];
            telemetry_data[ACCEL_DATA_POS+i*3+2] = accel_data[i][7:0];
        end
        
        // 12. 相位数据 (6*2=12字节)
        for (i = 0; i < 6; i = i + 1) begin
            telemetry_data[PHASE_DATA_POS+i*2] = phase_data[i][15:8];
            telemetry_data[PHASE_DATA_POS+i*2+1] = phase_data[i][7:0];
        end
        
        case(offset_setting_index)
            8'h00,8'h01: begin
                // 13. ADC/DAC偏值 (6*3=18字节)
                telemetry_data[ADC_DAC_OFFSET_POS]   = adc_offset_x1[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+1] = adc_offset_x1[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+2] = adc_offset_x1[7:0];
                
                telemetry_data[ADC_DAC_OFFSET_POS+3] = adc_offset_x2[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+4] = adc_offset_x2[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+5] = adc_offset_x2[7:0];
                
                telemetry_data[ADC_DAC_OFFSET_POS+6] = adc_offset_y1[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+7] = adc_offset_y1[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+8] = adc_offset_y1[7:0];
                
                telemetry_data[ADC_DAC_OFFSET_POS+9] =  adc_offset_y2[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+10] = adc_offset_y2[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+11] = adc_offset_y2[7:0];
                
                telemetry_data[ADC_DAC_OFFSET_POS+12] = adc_offset_z1[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+13] = adc_offset_z1[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+14] = adc_offset_z1[7:0];
                
                telemetry_data[ADC_DAC_OFFSET_POS+15] = adc_offset_z2[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+16] = adc_offset_z2[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+17] = adc_offset_z2[7:0];
            end
            8'h02,8'h03: begin
                telemetry_data[ADC_DAC_OFFSET_POS]   = dac_offset_x1[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+1] = dac_offset_x1[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+2] = dac_offset_x1[7:0];
                
                telemetry_data[ADC_DAC_OFFSET_POS+3] = dac_offset_x2[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+4] = dac_offset_x2[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+5] = dac_offset_x2[7:0];

                telemetry_data[ADC_DAC_OFFSET_POS+6] = dac_offset_y1[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+7] = dac_offset_y1[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+8] = dac_offset_y1[7:0];
                
                telemetry_data[ADC_DAC_OFFSET_POS+9] = dac_offset_y2[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+10] = dac_offset_y2[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+11] = dac_offset_y2[7:0];

                telemetry_data[ADC_DAC_OFFSET_POS+12] = dac_offset_z1[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+13] = dac_offset_z1[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+14] = dac_offset_z1[7:0];
                
                telemetry_data[ADC_DAC_OFFSET_POS+15] = dac_offset_z2[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+16] = dac_offset_z2[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+17] = dac_offset_z2[7:0];
            end

            default: begin
                telemetry_data[ADC_DAC_OFFSET_POS]   = adc_offset_x1[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+1] = adc_offset_x1[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+2] = adc_offset_x1[7:0];
                
                telemetry_data[ADC_DAC_OFFSET_POS+3] = adc_offset_x2[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+4] = adc_offset_x2[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+5] = adc_offset_x2[7:0];
                
                telemetry_data[ADC_DAC_OFFSET_POS+6] = adc_offset_y1[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+7] = adc_offset_y1[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+8] = adc_offset_y1[7:0];
                
                telemetry_data[ADC_DAC_OFFSET_POS+9] =  adc_offset_y2[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+10] = adc_offset_y2[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+11] = adc_offset_y2[7:0];
                
                telemetry_data[ADC_DAC_OFFSET_POS+12] = adc_offset_z1[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+13] = adc_offset_z1[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+14] = adc_offset_z1[7:0];
                
                telemetry_data[ADC_DAC_OFFSET_POS+15] = adc_offset_z2[23:16];
                telemetry_data[ADC_DAC_OFFSET_POS+16] = adc_offset_z2[15:8];
                telemetry_data[ADC_DAC_OFFSET_POS+17] = adc_offset_z2[7:0];
            end
        endcase

         // 14. 偏值设置索引 (1字节)
        telemetry_data[OFFSET_INDEX_POS] = offset_setting_index;
                
        // 15. PID参数 (24字节)
        // X1/Y1/Z1路PID参数 (12字节)
        // 根据PID参数设置索引选择
        case(pid_setting_index)
            8'h00: begin  // X1/X2通道
                // X1路比例参数
                telemetry_data[PID_PARAM_POS]   = pid_p_x1[31:24];
                telemetry_data[PID_PARAM_POS+1] = pid_p_x1[23:16];
                telemetry_data[PID_PARAM_POS+2] = pid_p_x1[15:8];
                telemetry_data[PID_PARAM_POS+3] = pid_p_x1[7:0];
                
                // X1路积分参数
                telemetry_data[PID_PARAM_POS+4] = pid_i_x1[31:24];
                telemetry_data[PID_PARAM_POS+5] = pid_i_x1[23:16];
                telemetry_data[PID_PARAM_POS+6] = pid_i_x1[15:8];
                telemetry_data[PID_PARAM_POS+7] = pid_i_x1[7:0];
                
                // X1路微分参数
                telemetry_data[PID_PARAM_POS+8] = pid_d_x1[31:24];
                telemetry_data[PID_PARAM_POS+9] = pid_d_x1[23:16];
                telemetry_data[PID_PARAM_POS+10] = pid_d_x1[15:8];
                telemetry_data[PID_PARAM_POS+11] = pid_d_x1[7:0];
                
                // X2路比例参数
                telemetry_data[PID_PARAM_POS+12] = pid_p_x2[31:24];
                telemetry_data[PID_PARAM_POS+13] = pid_p_x2[23:16];
                telemetry_data[PID_PARAM_POS+14] = pid_p_x2[15:8];
                telemetry_data[PID_PARAM_POS+15] = pid_p_x2[7:0];
                
                // X2路积分参数
                telemetry_data[PID_PARAM_POS+16] = pid_i_x2[31:24];
                telemetry_data[PID_PARAM_POS+17] = pid_i_x2[23:16];
                telemetry_data[PID_PARAM_POS+18] = pid_i_x2[15:8];
                telemetry_data[PID_PARAM_POS+19] = pid_i_x2[7:0];
                
                // X2路微分参数
                telemetry_data[PID_PARAM_POS+20] = pid_d_x2[31:24];
                telemetry_data[PID_PARAM_POS+21] = pid_d_x2[23:16];
                telemetry_data[PID_PARAM_POS+22] = pid_d_x2[15:8];
                telemetry_data[PID_PARAM_POS+23] = pid_d_x2[7:0];
            end
            
            8'h01: begin  // Y1/Y2通道
                // Y1路比例参数
                telemetry_data[PID_PARAM_POS]   = pid_p_y1[31:24];
                telemetry_data[PID_PARAM_POS+1] = pid_p_y1[23:16];
                telemetry_data[PID_PARAM_POS+2] = pid_p_y1[15:8];
                telemetry_data[PID_PARAM_POS+3] = pid_p_y1[7:0];
                
                // Y1路积分参数
                telemetry_data[PID_PARAM_POS+4] = pid_i_y1[31:24];
                telemetry_data[PID_PARAM_POS+5] = pid_i_y1[23:16];
                telemetry_data[PID_PARAM_POS+6] = pid_i_y1[15:8];
                telemetry_data[PID_PARAM_POS+7] = pid_i_y1[7:0];
                
                // Y1路微分参数
                telemetry_data[PID_PARAM_POS+8] = pid_d_y1[31:24];
                telemetry_data[PID_PARAM_POS+9] = pid_d_y1[23:16];
                telemetry_data[PID_PARAM_POS+10] = pid_d_y1[15:8];
                telemetry_data[PID_PARAM_POS+11] = pid_d_y1[7:0];
                
                // Y2路比例参数
                telemetry_data[PID_PARAM_POS+12] = pid_p_y2[31:24];
                telemetry_data[PID_PARAM_POS+13] = pid_p_y2[23:16];
                telemetry_data[PID_PARAM_POS+14] = pid_p_y2[15:8];
                telemetry_data[PID_PARAM_POS+15] = pid_p_y2[7:0];
                
                // Y2路积分参数
                telemetry_data[PID_PARAM_POS+16] = pid_i_y2[31:24];
                telemetry_data[PID_PARAM_POS+17] = pid_i_y2[23:16];
                telemetry_data[PID_PARAM_POS+18] = pid_i_y2[15:8];
                telemetry_data[PID_PARAM_POS+19] = pid_i_y2[7:0];
                
                // Y2路微分参数
                telemetry_data[PID_PARAM_POS+20] = pid_d_y2[31:24];
                telemetry_data[PID_PARAM_POS+21] = pid_d_y2[23:16];
                telemetry_data[PID_PARAM_POS+22] = pid_d_y2[15:8];
                telemetry_data[PID_PARAM_POS+23] = pid_d_y2[7:0];
            end
            
            8'h02: begin  // Z1/Z2通道
                // Z1路比例参数
                telemetry_data[PID_PARAM_POS]   = pid_p_z1[31:24];
                telemetry_data[PID_PARAM_POS+1] = pid_p_z1[23:16];
                telemetry_data[PID_PARAM_POS+2] = pid_p_z1[15:8];
                telemetry_data[PID_PARAM_POS+3] = pid_p_z1[7:0];
                
                // Z1路积分参数
                telemetry_data[PID_PARAM_POS+4] = pid_i_z1[31:24];
                telemetry_data[PID_PARAM_POS+5] = pid_i_z1[23:16];
                telemetry_data[PID_PARAM_POS+6] = pid_i_z1[15:8];
                telemetry_data[PID_PARAM_POS+7] = pid_i_z1[7:0];
                
                // Z1路微分参数
                telemetry_data[PID_PARAM_POS+8] = pid_d_z1[31:24];
                telemetry_data[PID_PARAM_POS+9] = pid_d_z1[23:16];
                telemetry_data[PID_PARAM_POS+10] = pid_d_z1[15:8];
                telemetry_data[PID_PARAM_POS+11] = pid_d_z1[7:0];
                
                // Z2路比例参数
                telemetry_data[PID_PARAM_POS+12] = pid_p_z2[31:24];
                telemetry_data[PID_PARAM_POS+13] = pid_p_z2[23:16];
                telemetry_data[PID_PARAM_POS+14] = pid_p_z2[15:8];
                telemetry_data[PID_PARAM_POS+15] = pid_p_z2[7:0];
                
                // Z2路积分参数
                telemetry_data[PID_PARAM_POS+16] = pid_i_z2[31:24];
                telemetry_data[PID_PARAM_POS+17] = pid_i_z2[23:16];
                telemetry_data[PID_PARAM_POS+18] = pid_i_z2[15:8];
                telemetry_data[PID_PARAM_POS+19] = pid_i_z2[7:0];
                
                // Z2路微分参数
                telemetry_data[PID_PARAM_POS+20] = pid_d_z2[31:24];
                telemetry_data[PID_PARAM_POS+21] = pid_d_z2[23:16];
                telemetry_data[PID_PARAM_POS+22] = pid_d_z2[15:8];
                telemetry_data[PID_PARAM_POS+23] = pid_d_z2[7:0];
            end
            
            default: begin // 默认使用X1/X2通道
                // X1路比例参数
                telemetry_data[PID_PARAM_POS]   = pid_p_x1[31:24];
                telemetry_data[PID_PARAM_POS+1] = pid_p_x1[23:16];
                telemetry_data[PID_PARAM_POS+2] = pid_p_x1[15:8];
                telemetry_data[PID_PARAM_POS+3] = pid_p_x1[7:0];
                
                // X1路积分参数
                telemetry_data[PID_PARAM_POS+4] = pid_i_x1[31:24];
                telemetry_data[PID_PARAM_POS+5] = pid_i_x1[23:16];
                telemetry_data[PID_PARAM_POS+6] = pid_i_x1[15:8];
                telemetry_data[PID_PARAM_POS+7] = pid_i_x1[7:0];
                
                // X1路微分参数
                telemetry_data[PID_PARAM_POS+8] = pid_d_x1[31:24];
                telemetry_data[PID_PARAM_POS+9] = pid_d_x1[23:16];
                telemetry_data[PID_PARAM_POS+10] = pid_d_x1[15:8];
                telemetry_data[PID_PARAM_POS+11] = pid_d_x1[7:0];
                
                // X2路比例参数
                telemetry_data[PID_PARAM_POS+12] = pid_p_x2[31:24];
                telemetry_data[PID_PARAM_POS+13] = pid_p_x2[23:16];
                telemetry_data[PID_PARAM_POS+14] = pid_p_x2[15:8];
                telemetry_data[PID_PARAM_POS+15] = pid_p_x2[7:0];
                
                // X2路积分参数
                telemetry_data[PID_PARAM_POS+16] = pid_i_x2[31:24];
                telemetry_data[PID_PARAM_POS+17] = pid_i_x2[23:16];
                telemetry_data[PID_PARAM_POS+18] = pid_i_x2[15:8];
                telemetry_data[PID_PARAM_POS+19] = pid_i_x2[7:0];
                
                // X2路微分参数
                telemetry_data[PID_PARAM_POS+20] = pid_d_x2[31:24];
                telemetry_data[PID_PARAM_POS+21] = pid_d_x2[23:16];
                telemetry_data[PID_PARAM_POS+22] = pid_d_x2[15:8];
                telemetry_data[PID_PARAM_POS+23] = pid_d_x2[7:0];
            end
        endcase
        
        // 16. PID参数设置索引 (1字节)
        telemetry_data[PID_INDEX_POS] = pid_setting_index;
        
        // 17. Vd检测电压值 (3字节)
        // 简化实现，使用生成的模拟温度数据作为Vd值
        telemetry_data[VD_VALUE_POS]   = temp_data[0][23:16];
        telemetry_data[VD_VALUE_POS+1] = temp_data[0][15:8];
        telemetry_data[VD_VALUE_POS+2] = temp_data[0][7:0];
        
        // 18. Vinct阈值 (3*3=9字节)
        telemetry_data[VINCT_THRESHOLD_POS]   = vinct1_threshold[23:16];
        telemetry_data[VINCT_THRESHOLD_POS+1] = vinct1_threshold[15:8];
        telemetry_data[VINCT_THRESHOLD_POS+2] = vinct1_threshold[7:0];
        
        telemetry_data[VINCT_THRESHOLD_POS+3] = vinct2_threshold[23:16];
        telemetry_data[VINCT_THRESHOLD_POS+4] = vinct2_threshold[15:8];
        telemetry_data[VINCT_THRESHOLD_POS+5] = vinct2_threshold[7:0];
        
        telemetry_data[VINCT_THRESHOLD_POS+6] = vinct3_threshold[23:16];
        telemetry_data[VINCT_THRESHOLD_POS+7] = vinct3_threshold[15:8];
        telemetry_data[VINCT_THRESHOLD_POS+8] = vinct3_threshold[7:0];
        
        // 19. 时间阈值 (2*2=4字节)
        telemetry_data[T_THRESHOLD_POS]   = t1_threshold[15:8];
        telemetry_data[T_THRESHOLD_POS+1] = t1_threshold[7:0];
        telemetry_data[T_THRESHOLD_POS+2] = t3_threshold[15:8];
        telemetry_data[T_THRESHOLD_POS+3] = t3_threshold[7:0];
        
        // 22. 积分阈值 (3字节)
        telemetry_data[INTEGRAL_THRESHOLD_POS]   = integral_threshold[23:16];
        telemetry_data[INTEGRAL_THRESHOLD_POS+1] = integral_threshold[15:8];
        telemetry_data[INTEGRAL_THRESHOLD_POS+2] = integral_threshold[7:0];
        
        // 23. 上注包计数 (2字节)
        telemetry_data[PROGRAM_PKG_CNT_POS]   = program_pkg_cnt[15:8];
        telemetry_data[PROGRAM_PKG_CNT_POS+1] = program_pkg_cnt[7:0];
        
        // 24. DSP程序版本 (1字节)
        telemetry_data[DSP_VERSION_POS] = dsp_version;
        
        // 25. 自检状态 (2字节)
        telemetry_data[SELF_CHECK_POS]   = self_check_status[15:8];
        telemetry_data[SELF_CHECK_POS+1] = self_check_status[7:0];
        
        // 26. 命令计数部分 (6字节)
        telemetry_data[CMD_COUNT_POS]   = correct_cmd_cnt;      // 正确指令计数
        telemetry_data[CMD_COUNT_POS+1] = last_correct_cmd;     // 最后正确指令类型
        telemetry_data[CMD_COUNT_POS+2] = error_cmd_cnt;        // 错误指令计数
        telemetry_data[CMD_COUNT_POS+3] = last_error_cmd;       // 最后错误指令类型
        telemetry_data[CMD_COUNT_POS+4] = error_type;           // 指令出错类型
        telemetry_data[CMD_COUNT_POS+5] = uart_reset_cnt;       // 异步串口复位计数
        
        // 27. 保留字节 (1字节)
        telemetry_data[RESERVED_BYTE_POS] = 8'h55;
        
        // 28. 计算校验和 (数据长度和有效数据字段的校验和)
        checksum = 16'h0000;
        // 数据长度字段
        checksum = checksum + telemetry_data[DATA_LEN_POS];
        checksum = checksum + telemetry_data[DATA_LEN_POS+1];
        // 有效数据字段 (从服务类型到保留字节)
        for (i = SERVICE_TYPE_POS; i <= RESERVED_BYTE_POS; i = i + 1) begin
            checksum = checksum + telemetry_data[i];
        end
        
        // 29. 校验码 (2字节)
        telemetry_data[CHECKSUM_POS] = checksum[15:8];
        telemetry_data[CHECKSUM_POS+1] = checksum[7:0];
        
        // 30. 帧尾 (2字节)
        telemetry_data[FRAME_TAIL_POS] = FRAME_TAIL[15:8];
        telemetry_data[FRAME_TAIL_POS+1] = FRAME_TAIL[7:0];
    end
    endtask
    
    // ================== 传感器数据生成任务 ==================
    // 生成模拟的传感器数据 (温度、位移、加速度和相位数据)
    task generate_sensor_data;
        integer i;
        reg [15:0] sine_value;
    begin
        // 生成一个简单的正弦波近似值（使用帧计数器）
        sine_value = frame_counter[15:0] ^ 16'hA5A5; // 简单异或操作生成伪随机变化
        
        // 根据工作模式生成不同特性的数据
        case (work_mode)
            8'hAA: begin // 捕获模式
                // 生成温度数据
                for (i = 0; i < 4; i = i + 1) begin
                    temp_data[i] = 24'h200000 + (i << 16) + sine_value[15:0];
                end
                
                // 生成位移数据
                for (i = 0; i < 6; i = i + 1) begin
                    position_data[i] = 24'h300000 + (i << 16) + sine_value[7:0];
                end
                
                // 生成加速度数据
                for (i = 0; i < 6; i = i + 1) begin
                    accel_data[i] = 24'h400000 + (i << 16) + sine_value[15:8];
                end
                
                // 生成相位数据（2字节）
                for (i = 0; i < 6; i = i + 1) begin
                    phase_data[i] = 16'h1000 + (i << 8) + frame_counter[7:0];
                end
            end
            
            8'h55: begin // 大量程模式
                // 生成温度数据
                for (i = 0; i < 4; i = i + 1) begin
                    temp_data[i] = 24'h210000 + (i << 16) + (sine_value[7:0] << 8);
                end
                
                // 生成较大范围的位移数据
                for (i = 0; i < 6; i = i + 1) begin
                    position_data[i] = 24'h500000 + (i << 16) + (sine_value[15:0] ^ 16'h5A5A);
                end
                
                // 生成较大范围的加速度数据
                for (i = 0; i < 6; i = i + 1) begin
                    accel_data[i] = 24'h600000 + (i << 16) + sine_value[15:0];
                end
                
                // 生成相位数据（2字节）
                for (i = 0; i < 6; i = i + 1) begin
                    phase_data[i] = 16'h2000 + (i << 8) + frame_counter[7:0];
                end
            end
            
            8'h33: begin // 小量程模式
                // 生成温度数据
                for (i = 0; i < 4; i = i + 1) begin
                    temp_data[i] = 24'h220000 + (i << 16) + frame_counter[7:0];
                end
                
                // 生成较小范围的位移数据
                for (i = 0; i < 6; i = i + 1) begin
                    position_data[i] = 24'h100000 + (i << 16) + sine_value[7:0];
                end
                
                // 生成较小范围的加速度数据
                for (i = 0; i < 6; i = i + 1) begin
                    accel_data[i] = 24'h100000 + (i << 16) + (sine_value[3:0] << 4);
                end
                
                // 生成相位数据（2字节）
                for (i = 0; i < 6; i = i + 1) begin
                    phase_data[i] = 16'h3000 + (i << 8) + frame_counter[7:0];
                end
            end
            
            8'h11: begin // 程序上注模式
                // 特殊模式，使用固定数据
                for (i = 0; i < 4; i = i + 1) begin
                    temp_data[i] = 24'h000111 * (i+1);
                end
                
                for (i = 0; i < 6; i = i + 1) begin
                    position_data[i] = 24'h000111 * (i+1);
                    accel_data[i] = 24'h000111 * (i+1);
                    phase_data[i] = 16'h1100 + (i << 4);
                end
            end
            
            default: begin // 未知模式，使用默认数据
                for (i = 0; i < 4; i = i + 1) begin
                    temp_data[i] = 24'h000000;
                end
                
                for (i = 0; i < 6; i = i + 1) begin
                    position_data[i] = 24'h000000;
                    accel_data[i] = 24'h000000;
                    phase_data[i] = 16'h0000;
                end
            end
        endcase
    end
    endtask

endmodule 