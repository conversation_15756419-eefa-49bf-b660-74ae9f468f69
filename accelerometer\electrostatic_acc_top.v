module electrostatic_acc_top (
    input wire clk,                // 系统时钟
    input wire rst_n,              // 复位信号，低电平有效
    input wire [7:0] baud_val,     // 波特率配?
    
    // 系统时间接口
    input wire [31:0] sys_time_s,  // 系统时间(?)
    input wire [15:0] sys_time_ms, // 系统时间(毫秒)
    
    // 三路RS422接口
    output wire sci_tx,            // 科学数据发?线
    input wire cmd_rx,             // 遥控指令接收?
    output wire telemetry_tx       // 遥测数据发?线
);

    // ================ 内部连线声明 ================
    // 共享状?接?
    wire [7:0] work_mode;          // 工作模式状??
    
    wire [31:0] pid_p_x1;          // X1通道比例参数
    wire [31:0] pid_i_x1;          // X1通道积分参数
    wire [31:0] pid_d_x1;          // X1通道微分参数
    wire [31:0] pid_p_x2;          // X2通道比例参数
    wire [31:0] pid_i_x2;          // X2通道积分参数
    wire [31:0] pid_d_x2;          // X2通道微分参数
    wire [31:0] pid_p_y1;          // Y1通道比例参数
    wire [31:0] pid_i_y1;          // Y1通道积分参数
    wire [31:0] pid_d_y1;          // Y1通道微分参数
    wire [31:0] pid_p_y2;          // Y2通道比例参数
    wire [31:0] pid_i_y2;          // Y2通道积分参数
    wire [31:0] pid_d_y2;          // Y2通道微分参数
    wire [31:0] pid_p_z1;          // Z1通道比例参数
    wire [31:0] pid_i_z1;          // Z1通道积分参数
    wire [31:0] pid_d_z1;          // Z1通道微分参数
    wire [31:0] pid_p_z2;          // Z2通道比例参数
    wire [31:0] pid_i_z2;          // Z2通道积分参数
    wire [31:0] pid_d_z2;          // Z2通道微分参数
    wire [7:0] pid_setting_index;  // PID参数设置索引
    
    wire [23:0] adc_offset_x1;     // X1通道ADC偏??
    wire [23:0] adc_offset_x2;     // X2通道ADC偏??
    wire [23:0] adc_offset_y1;     // Y1通道ADC偏??
    wire [23:0] adc_offset_y2;     // Y2通道ADC偏??
    wire [23:0] adc_offset_z1;     // Z1通道ADC偏??
    wire [23:0] adc_offset_z2;     // Z2通道ADC偏??
    wire [23:0] dac_offset_x1;     // X1通道DAC偏??
    wire [23:0] dac_offset_x2;     // X2通道DAC偏??
    wire [23:0] dac_offset_y1;     // Y1通道DAC偏??
    wire [23:0] dac_offset_y2;     // Y2通道DAC偏??
    wire [23:0] dac_offset_z1;     // Z1通道DAC偏??
    wire [23:0] dac_offset_z2;     // Z2通道DAC偏??
    wire [7:0] offset_setting_index; // 偏?设置索?
    
    wire [23:0] vinct1_threshold;  // Vinct1阈??
    wire [23:0] vinct2_threshold;  // Vinct2阈??
    wire [23:0] vinct3_threshold;  // Vinct3阈??
    wire [15:0] t1_threshold;      // t1阈??
    wire [15:0] t3_threshold;      // t3阈??
    wire [23:0] integral_threshold; // 积分阈??
    
    wire [7:0] correct_cmd_cnt;    // 正确指令计数
    wire [7:0] last_correct_cmd;   // ?后正确指令类?
    wire [7:0] error_cmd_cnt;      // 错误指令计数
    wire [7:0] last_error_cmd;     // ?后错误指令类?
    wire [7:0] uart_reset_cnt;     // 异步串口复位计数
    wire [15:0] program_pkg_cnt;   // 上注包计?
    wire [7:0] dsp_version;        // DSP程序版本
    wire [15:0] self_check_status; // 自检状??
    wire error_type;
    // 命令?测和处理
    wire telemetry_req;            // 遥测请求信号

    // ================ 模块实例? ================
    
    // 科学数据发??道
    acc_science_data_channel science_data_inst (
        .clk(clk),
        .rst_n(rst_n),
        .baud_val(baud_val),
        
        // 系统时间
        .sys_time_s(sys_time_s),
        .sys_time_ms(sys_time_ms),
        
        // 状?参?
        .work_mode(work_mode),
        // UART输出
        .tx(sci_tx)
    );
    
    // 遥控指令接收通道
    acc_command_channel command_inst (
        .clk(clk),
        .rst_n(rst_n),
        .baud_val(baud_val),
        
        // UART接收
        .rx(cmd_rx),
        
        // 状?参数更?
        .work_mode(work_mode),
        
        // PID参数
        .pid_p_x1(pid_p_x1),
        .pid_i_x1(pid_i_x1),
        .pid_d_x1(pid_d_x1),
        .pid_p_x2(pid_p_x2),
        .pid_i_x2(pid_i_x2),
        .pid_d_x2(pid_d_x2),
        .pid_p_y1(pid_p_y1),
        .pid_i_y1(pid_i_y1),
        .pid_d_y1(pid_d_y1),
        .pid_p_y2(pid_p_y2),
        .pid_i_y2(pid_i_y2),
        .pid_d_y2(pid_d_y2),
        .pid_p_z1(pid_p_z1),
        .pid_i_z1(pid_i_z1),
        .pid_d_z1(pid_d_z1),
        .pid_p_z2(pid_p_z2),
        .pid_i_z2(pid_i_z2),
        .pid_d_z2(pid_d_z2),
        .pid_setting_index(pid_setting_index),
        
        // ADC/DAC偏??
        .adc_offset_x1(adc_offset_x1),
        .adc_offset_x2(adc_offset_x2),
        .adc_offset_y1(adc_offset_y1),
        .adc_offset_y2(adc_offset_y2),
        .adc_offset_z1(adc_offset_z1),
        .adc_offset_z2(adc_offset_z2),
        .dac_offset_x1(dac_offset_x1),
        .dac_offset_x2(dac_offset_x2),
        .dac_offset_y1(dac_offset_y1),
        .dac_offset_y2(dac_offset_y2),
        .dac_offset_z1(dac_offset_z1),
        .dac_offset_z2(dac_offset_z2),
        .offset_setting_index(offset_setting_index),
        
        // 阈?参?
        .vinct1_threshold(vinct1_threshold),
        .vinct2_threshold(vinct2_threshold),
        .vinct3_threshold(vinct3_threshold),
        .t1_threshold(t1_threshold),
        .t3_threshold(t3_threshold),
        .integral_threshold(integral_threshold),
        
        // 命令和状态计?
        .correct_cmd_cnt(correct_cmd_cnt),
        .last_correct_cmd(last_correct_cmd),
        .error_cmd_cnt(error_cmd_cnt),
        .last_error_cmd(last_error_cmd),
        .error_type(error_type),
        .uart_reset_cnt(uart_reset_cnt),
        .program_pkg_cnt(program_pkg_cnt),
        .dsp_version(dsp_version),
        .self_check_status(self_check_status),
        
        // 遥测请求
        .telemetry_req(telemetry_req)
    );
    
    // 遥测数据发??道
    acc_telemetry_channel telemetry_inst (
        .clk(clk),
        .rst_n(rst_n),
        .baud_val(baud_val),
        
        // 遥测请求触发
        .telemetry_req(telemetry_req),
        
        // 系统时间
        .sys_time_s(sys_time_s),
        .sys_time_ms(sys_time_ms),
        
        // 状?参?
        .work_mode(work_mode),
        
        // PID参数
        .pid_p_x1(pid_p_x1),
        .pid_i_x1(pid_i_x1),
        .pid_d_x1(pid_d_x1),
        .pid_p_x2(pid_p_x2),
        .pid_i_x2(pid_i_x2),
        .pid_d_x2(pid_d_x2),
        .pid_p_y1(pid_p_y1),
        .pid_i_y1(pid_i_y1),
        .pid_d_y1(pid_d_y1),
        .pid_p_y2(pid_p_y2),
        .pid_i_y2(pid_i_y2),
        .pid_d_y2(pid_d_y2),
        .pid_p_z1(pid_p_z1),
        .pid_i_z1(pid_i_z1),
        .pid_d_z1(pid_d_z1),
        .pid_p_z2(pid_p_z2),
        .pid_i_z2(pid_i_z2),
        .pid_d_z2(pid_d_z2),
        .pid_setting_index(pid_setting_index),
        
        // ADC/DAC偏??
        .adc_offset_x1(adc_offset_x1),
        .adc_offset_x2(adc_offset_x2),
        .adc_offset_y1(adc_offset_y1),
        .adc_offset_y2(adc_offset_y2),
        .adc_offset_z1(adc_offset_z1),
        .adc_offset_z2(adc_offset_z2),
        .dac_offset_x1(dac_offset_x1),
        .dac_offset_x2(dac_offset_x2),
        .dac_offset_y1(dac_offset_y1),
        .dac_offset_y2(dac_offset_y2),
        .dac_offset_z1(dac_offset_z1),
        .dac_offset_z2(dac_offset_z2),
        .offset_setting_index(offset_setting_index),
                
        // 阈?参?
        .vinct1_threshold(vinct1_threshold),
        .vinct2_threshold(vinct2_threshold),
        .vinct3_threshold(vinct3_threshold),
        .t1_threshold(t1_threshold),
        .t3_threshold(t3_threshold),
        .integral_threshold(integral_threshold),
        
        // 命令和状态计?
        .correct_cmd_cnt(correct_cmd_cnt),
        .last_correct_cmd(last_correct_cmd),
        .error_cmd_cnt(error_cmd_cnt),
        .last_error_cmd(last_error_cmd),
        .error_type(error_type),
        .uart_reset_cnt(uart_reset_cnt),
        .program_pkg_cnt(program_pkg_cnt),
        .dsp_version(dsp_version),
        .self_check_status(self_check_status),
        
        // UART输出
        .tx(telemetry_tx)
    );

endmodule 