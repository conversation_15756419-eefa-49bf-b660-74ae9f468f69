`timescale 1ns/1ps

module tb_electrostatic_acc_top();

reg clk;
reg rst_n;
wire sci_tx;        // Scientific data output channel
wire telemetry_tx;  // Telemetry data output channel
reg cmd_rx;         // Command input channel
reg [7:0] rx_byte;
reg [7:0] sci_data[0:95];    // Array for science data (96 bytes)
reg [7:0] tlm_data[0:155];   // Array for telemetry data (156 bytes)
integer sci_count = 0;
integer tlm_count = 0;
integer i;
reg [7:0] tx_cmd[0:11]; // Array for telemetry request command (12 bytes)

// Baud rate settings
parameter CLK_FREQ = 50_000_000; // 50MHz clock
parameter BAUD_RATE = 115200;
parameter BIT_TIME = 1_000_000_000/BAUD_RATE; // Bit time in nanoseconds

// Packet sizes
parameter SCI_PACKET_SIZE = 96;  // Science data packet size
parameter TLM_PACKET_SIZE = 156; // Telemetry data packet size

electrostatic_acc_top electrostatic_acc_top_inst(
    .clk(clk),
    .rst_n(rst_n),
    .baud_val(8'd26), // 115200bps @ 50MHz
    .sys_time_s(32'd0),
    .sys_time_ms(16'd0),
    .sci_tx(sci_tx),
    .cmd_rx(cmd_rx),
    .telemetry_tx(telemetry_tx)
);

initial clk = 0;
always #10 clk = ~clk; // 50MHz clock

// Initialize telemetry request command
initial begin
    // Initialize telemetry request command (Table 4-3 format)
    // Frame header: 0xEB90
    tx_cmd[0] = 8'hEB;
    tx_cmd[1] = 8'h90;
    // Data length: 0x0004
    tx_cmd[2] = 8'h00;
    tx_cmd[3] = 8'h04;
    // Service type: 0x08
    tx_cmd[4] = 8'h08;
    // Service status: 0x00
    tx_cmd[5] = 8'h00;
    // Command identifier: 0x00 (telemetry polling)
    tx_cmd[6] = 8'h00;
    // Command length: 0x00
    tx_cmd[7] = 8'h00;
    // Checksum (sum of bytes 2-7)
    tx_cmd[8] = 8'h00;
    tx_cmd[9] = 8'h0C; // 0x00 + 0x04 + 0x08 + 0x00 + 0x00 + 0x00 = 0x0C
    // Frame tail: 0x09D7
    tx_cmd[10] = 8'h09;
    tx_cmd[11] = 8'hD7;
    
    // Default RX line state is high (idle)
    cmd_rx = 1;
end

initial begin
    rst_n = 0;
    #100 rst_n = 1;
    
    // Start UART monitoring and test sequence tasks
    fork
        sci_monitor;     // Monitor for science data
        tlm_monitor;     // Monitor for telemetry data
        test_sequence;   // Main test sequence
    join
end

// Test sequence task
task test_sequence;
    begin
        // Wait for some time to let the device initialize
        #500000;
        
        // First, observe the science data (should be sent automatically)
        $display("Waiting for science data packet...");
        wait(sci_count == SCI_PACKET_SIZE-1);
        // Science data received and displayed by display_sci_packet()
        
        // Wait a bit before sending telemetry request
        #100000;
        
        // Send telemetry request command
        $display("Sending telemetry request command...");
        send_command();
        
        // Wait for telemetry response (should come within 2ms)
        $display("Waiting for telemetry packet response...");
        wait(tlm_count == TLM_PACKET_SIZE-1);
        // Telemetry data received and displayed by display_tlm_packet()
        
        // Test complete
        $display("Test sequence completed successfully");
    end
endtask

// Science data UART receiver task
task sci_monitor;
    forever begin
        // Wait for start bit (low level)
        @(negedge sci_tx);
        
        // Middle of start bit
        #(BIT_TIME/2);
        
        // Confirm it's a start bit
        if (sci_tx == 0) begin
            rx_byte = 0;
            
            // Read 8 data bits (LSB first)
            for (i = 0; i < 8; i = i + 1) begin
                #BIT_TIME;
                rx_byte[i] = sci_tx;
            end
            
            // Read parity bit
            #BIT_TIME;
            // Parity check code can be added here
            
            // Read stop bit
            #BIT_TIME;
            
            // Save received byte to science data array
            sci_data[sci_count] = rx_byte;
            sci_count = sci_count + 1;
            
            // Print received byte
            $display("Time %0t: Received science byte: 0x%h", $time, rx_byte);
            
            // Check if we received a complete science packet
            if (sci_count == SCI_PACKET_SIZE) begin
                display_sci_packet();
                sci_count = 0;
            end
        end
    end
endtask

// Telemetry data UART receiver task
task tlm_monitor;
    forever begin
        // Wait for start bit (low level)
        @(negedge telemetry_tx);
        
        // Middle of start bit
        #(BIT_TIME/2);
        
        // Confirm it's a start bit
        if (telemetry_tx == 0) begin
            rx_byte = 0;
            
            // Read 8 data bits (LSB first)
            for (i = 0; i < 8; i = i + 1) begin
                #BIT_TIME;
                rx_byte[i] = telemetry_tx;
            end
            
            // Read parity bit
            #BIT_TIME;
            // Parity check code can be added here
            
            // Read stop bit
            #BIT_TIME;
            
            // Save received byte to telemetry data array
            tlm_data[tlm_count] = rx_byte;
            tlm_count = tlm_count + 1;
            
            // Print received byte
            $display("Time %0t: Received telemetry byte: 0x%h", $time, rx_byte);
            
            // Check if we received a complete telemetry packet
            if (tlm_count == TLM_PACKET_SIZE) begin
                display_tlm_packet();
                tlm_count = 0;
            end
        end
    end
endtask

// UART transmitter task to send command
task send_command;
    integer j;
    begin
        for (j = 0; j < 12; j = j + 1) begin
            // Start bit
            cmd_rx = 0;
            #BIT_TIME;
            
            // 8 data bits (LSB first)
            for (i = 0; i < 8; i = i + 1) begin
                cmd_rx = (tx_cmd[j] >> i) & 1'b1;
                #BIT_TIME;
            end
            
            // Parity bit (odd parity)
            cmd_rx = ~(^tx_cmd[j]); // XOR all bits and invert for odd parity
            #BIT_TIME;
            
            // Stop bit
            cmd_rx = 1;
            #BIT_TIME;
            
            // Small delay between bytes
            #(BIT_TIME/2);
        end
        
        $display("Command sent: Telemetry request");
    end
endtask

// Display complete science data packet
task display_sci_packet;
    begin
        $display("====== COMPLETE SCIENCE DATA PACKET ======");
        $display("Frame Header: 0x%h%h", sci_data[0], sci_data[1]);
        $display("Data Length: 0x%h%h", sci_data[2], sci_data[3]);
        $display("Service Type: 0x%h", sci_data[4]);
        $display("Service Status: 0x%h", sci_data[5]);
        $display("Frame Counter: 0x%h%h%h", sci_data[6], sci_data[7], sci_data[8]);
        $display("Working Mode Status: 0x%h", sci_data[16]);
        // More data parsing can be added here
        $display("Checksum: 0x%h%h", sci_data[92], sci_data[93]);
        $display("Frame Tail: 0x%h%h", sci_data[94], sci_data[95]);
        $display("==========================================");
    end
endtask

// Display complete telemetry data packet
task display_tlm_packet;
    begin
        $display("====== COMPLETE TELEMETRY DATA PACKET ======");
        $display("Frame Header: 0x%h%h", tlm_data[0], tlm_data[1]);
        $display("Data Length: 0x%h%h", tlm_data[2], tlm_data[3]);
        $display("Service Type: 0x%h", tlm_data[4]);
        $display("Service Status: 0x%h", tlm_data[5]);
        $display("Frame Counter: 0x%h%h%h", tlm_data[6], tlm_data[7], tlm_data[8]);
        $display("Working Mode Status: 0x%h", tlm_data[16]);
        $display("Temperature Data: 0x%h%h%h, 0x%h%h%h, 0x%h%h%h, 0x%h%h%h", 
                 tlm_data[17], tlm_data[18], tlm_data[19],
                 tlm_data[20], tlm_data[21], tlm_data[22],
                 tlm_data[23], tlm_data[24], tlm_data[25],
                 tlm_data[26], tlm_data[27], tlm_data[28]);
        // Add more telemetry data parsing as needed
        $display("Checksum: 0x%h%h", tlm_data[152], tlm_data[153]);
        $display("Frame Tail: 0x%h%h", tlm_data[154], tlm_data[155]);
        $display("=============================================");
    end
endtask

endmodule