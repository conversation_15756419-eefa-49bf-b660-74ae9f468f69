`timescale 1ns/1ps

module tb_electrostatic_acc_top_telemetry();

reg clk;
reg rst_n;
wire telemetry_tx;  // Telemetry data output channel
reg cmd_rx;         // Command input channel
reg [7:0] rx_byte;
reg [7:0] tlm_data[0:155];   // Array for telemetry data (156 bytes)
integer tlm_count = 0;
integer i;
reg [7:0] tx_cmd[0:11]; // Array for telemetry request command (12 bytes)
reg [7:0] tx_ctrl_cmd[0:15]; // Array for control command (up to 16 bytes)
wire [7:0] error_type;
// Baud rate settings
parameter BAUD_RATE = 115200;
parameter BIT_TIME = 1_000_000_000/BAUD_RATE; // Bit time in nanoseconds

// Packet size
parameter TLM_PACKET_SIZE = 156; // Telemetry data packet size

// Command types from Table 4-5
parameter CMD_SOFTWARE_INJECTION_REQ = 8'h01;
parameter CMD_SOFTWARE_INJECTION_END = 8'h02;
parameter CMD_ONLINE_PROGRAM_START = 8'h03;
parameter CMD_LARGE_RANGE_MODE = 8'h04;
parameter CMD_SMALL_RANGE_MODE = 8'h05;
parameter CMD_SOFTWARE_RESET = 8'h06;
parameter CMD_INJECTION_MODE = 8'h07;

electrostatic_acc_top electrostatic_acc_top_inst(
    .clk(clk),
    .rst_n(rst_n),
    .baud_val(8'd21), // 115200bps @ 50MHz
    .sys_time_s(32'd0),
    .sys_time_ms(16'd0),
    .cmd_rx(cmd_rx),
    .telemetry_tx(telemetry_tx)
    //.error_type(error_type)
    // sci_tx connection removed since we're not testing it
);

initial clk = 0;
always #12.5 clk = ~clk; // 50MHz clock

// Initialize telemetry request command
initial begin
    // Initialize telemetry request command (Table 4-3 format)
    // Frame header: 0xEB90
    tx_cmd[0] = 8'hEB;
    tx_cmd[1] = 8'h90;
    // Data length: 0x0004
    tx_cmd[2] = 8'h00;
    tx_cmd[3] = 8'h04;
    // Service type: 0x08
    tx_cmd[4] = 8'h08;
    // Service status: 0x00
    tx_cmd[5] = 8'h00;
    // Command identifier: 0x00 (telemetry polling)
    tx_cmd[6] = 8'h00;
    // Command length: 0x00
    tx_cmd[7] = 8'h00;
    // Checksum (sum of bytes 2-7)
    tx_cmd[8] = 8'h00;
    tx_cmd[9] = 8'h0C; // 0x00 + 0x04 + 0x08 + 0x00 + 0x00 + 0x00 = 0x0C
    // Frame tail: 0x09D7
    tx_cmd[10] = 8'h09;
    tx_cmd[11] = 8'hD7;
    
    // Default RX line state is high (idle)
    cmd_rx = 1;
end

initial begin
    rst_n = 0;
    #100 rst_n = 1;
    
    // Start UART monitoring and test sequence tasks
    fork
        tlm_monitor;     // Monitor for telemetry data
        test_sequence;   // Main test sequence
    join
end

// Test sequence task - telemetry polling and control commands
task test_sequence;
    integer cmd_index;
    begin
        // Wait for some time to let the device initialize
        #500000;
        
        // Initialize command index
        cmd_index = 0;
        
        // Start the test loop - run for 30 seconds
        repeat(10) begin  // 10 iterations = 30 seconds
            // Send telemetry request command
            $display("Time %0t: Sending telemetry request command...", $time);
            send_polling_command();
            
            // Wait for telemetry response
            $display("Time %0t: Waiting for telemetry packet response...", $time);
            wait(tlm_count == TLM_PACKET_SIZE);
            tlm_count = 0;
            
            // After 1 second, send control command
            #500000;
            $display("Time %0t: Sending control command (Type: 0x%h)...", $time, (cmd_index % 7) + 1);
            send_control_command((cmd_index % 7) + 1); // Use modulo to cycle through command types
            cmd_index = cmd_index + 1;
            
            // Wait for next telemetry polling (2 more seconds)
             #1000000;
        end
        
        // Test complete
        $display("Time %0t: Test sequence completed successfully", $time);
        #100000 $finish;
    end
endtask

// Telemetry data UART receiver task
task tlm_monitor;
    forever begin
        // Wait for start bit (low level)
        @(negedge telemetry_tx);
        
        // Middle of start bit
        #(BIT_TIME/2);
        
        // Confirm it's a start bit
        if (telemetry_tx == 0) begin
            rx_byte = 0;
            
            // Read 8 data bits (LSB first)
            for (i = 0; i < 8; i = i + 1) begin
                #BIT_TIME;
                rx_byte[i] = telemetry_tx;
            end
            
            // Read parity bit
            #BIT_TIME;
            // Parity check code can be added here
            
            // Read stop bit
            #BIT_TIME;
            
            // Save received byte to telemetry data array
            tlm_data[tlm_count] = rx_byte;
            tlm_count = tlm_count + 1;
            
            // Print received byte
            $display("Time %0t: Received telemetry byte: 0x%h", $time, rx_byte);
            
            // Check if we received a complete telemetry packet
            if (tlm_count == TLM_PACKET_SIZE) begin
                display_tlm_packet();
            end
        end
    end
endtask

// UART transmitter task to send polling command
task send_polling_command;
    integer j;
    begin
        for (j = 0; j < 12; j = j + 1) begin
            // Start bit
            cmd_rx = 0;
            #BIT_TIME;
            
            // 8 data bits (LSB first)
            for (i = 0; i < 8; i = i + 1) begin
                cmd_rx = (tx_cmd[j] >> i) & 1'b1;
                #BIT_TIME;
            end
            
            // Parity bit (odd parity)
            cmd_rx = ~(^tx_cmd[j]); // XOR all bits and invert for odd parity
            #BIT_TIME;
            
            // Stop bit
            cmd_rx = 1;
            #BIT_TIME;
            
            // Small delay between bytes
            #(BIT_TIME/2);
        end
        
        $display("Time %0t: Telemetry request command sent", $time);
    end
endtask

// Task to prepare control command based on command type
// Task to prepare control command based on command type
task prepare_control_command;
    input [7:0] cmd_type;
    reg [15:0] checksum;
    begin
        // Common header for all control commands (Table 4-4 format)
        // Frame header: 0xEB90
        tx_ctrl_cmd[0] = 8'hEB;
        tx_ctrl_cmd[1] = 8'h90;
        
        // Data length: 0x0008 (for 4-byte command)
        tx_ctrl_cmd[2] = 8'h00;
        tx_ctrl_cmd[3] = 8'h08;
        
        // Service type: 0x08
        tx_ctrl_cmd[4] = 8'h08;
        
        // Service status: 0x00
        tx_ctrl_cmd[5] = 8'h00;
        
        // Command identifier: depends on command type
        tx_ctrl_cmd[6] = cmd_type;
        
        // Command length: 0x04
        tx_ctrl_cmd[7] = 8'h04;
        
        // Command bytes (from Table 4-5)
        case (cmd_type)
            CMD_SOFTWARE_INJECTION_REQ: begin
                tx_ctrl_cmd[8] = 8'h11;
                tx_ctrl_cmd[9] = 8'h11;
                tx_ctrl_cmd[10] = 8'hFF;
                tx_ctrl_cmd[11] = 8'hFF;
            end
            
            CMD_SOFTWARE_INJECTION_END: begin
                tx_ctrl_cmd[8] = 8'h22;
                tx_ctrl_cmd[9] = 8'h22;
                tx_ctrl_cmd[10] = 8'hFF;
                tx_ctrl_cmd[11] = 8'hFF;
            end
            
            CMD_ONLINE_PROGRAM_START: begin
                tx_ctrl_cmd[8] = 8'h33;
                tx_ctrl_cmd[9] = 8'h33;
                tx_ctrl_cmd[10] = 8'hFF;
                tx_ctrl_cmd[11] = 8'hFF;
            end
            
            CMD_LARGE_RANGE_MODE: begin
                tx_ctrl_cmd[8] = 8'h44;
                tx_ctrl_cmd[9] = 8'h44;
                tx_ctrl_cmd[10] = 8'hFF;
                tx_ctrl_cmd[11] = 8'hFF;
            end
            
            CMD_SMALL_RANGE_MODE: begin
                tx_ctrl_cmd[8] = 8'h55;
                tx_ctrl_cmd[9] = 8'h55;
                tx_ctrl_cmd[10] = 8'hFF;
                tx_ctrl_cmd[11] = 8'hFF;
            end
            
            CMD_SOFTWARE_RESET: begin
                tx_ctrl_cmd[8] = 8'h66;
                tx_ctrl_cmd[9] = 8'h66;
                tx_ctrl_cmd[10] = 8'hFF;
                tx_ctrl_cmd[11] = 8'hFF;
            end
            
            CMD_INJECTION_MODE: begin
                tx_ctrl_cmd[8] = 8'h77;
                tx_ctrl_cmd[9] = 8'h77;
                tx_ctrl_cmd[10] = 8'hFF;
                tx_ctrl_cmd[11] = 8'hFF;
            end
            
            default: begin
                tx_ctrl_cmd[8] = 8'h11;
                tx_ctrl_cmd[9] = 8'h11;
                tx_ctrl_cmd[10] = 8'hFF;
                tx_ctrl_cmd[11] = 8'hFF;
            end
        endcase
        
        // Calculate checksum (sum of bytes 2-11)
        checksum = 16'h0000;
        
        checksum = checksum + {8'h00, tx_ctrl_cmd[2]};
        checksum = checksum + {8'h00, tx_ctrl_cmd[3]};
        checksum = checksum + {8'h00, tx_ctrl_cmd[4]};
        checksum = checksum + {8'h00, tx_ctrl_cmd[5]};
        checksum = checksum + {8'h00, tx_ctrl_cmd[6]};
        checksum = checksum + {8'h00, tx_ctrl_cmd[7]};
        checksum = checksum + {8'h00, tx_ctrl_cmd[8]};
        checksum = checksum + {8'h00, tx_ctrl_cmd[9]};
        checksum = checksum + {8'h00, tx_ctrl_cmd[10]};
        checksum = checksum + {8'h00, tx_ctrl_cmd[11]};
        
        // Store result in two bytes (big endian)
        tx_ctrl_cmd[12] = checksum[15:8]; // High byte
        tx_ctrl_cmd[13] = checksum[7:0];  // Low byte
        
        // Frame tail: 0x09D7
        tx_ctrl_cmd[14] = 8'h09;
        tx_ctrl_cmd[15] = 8'hD7;
    end
endtask

// Task to send control command
task send_control_command;
    input [7:0] cmd_type;
    integer j;
    begin
        // Prepare the command
        prepare_control_command(cmd_type);
        
        // Send the command (16 bytes)
        for (j = 0; j < 16; j = j + 1) begin
            // Start bit
            cmd_rx = 0;
            #BIT_TIME;
            
            // 8 data bits (LSB first)
            for (i = 0; i < 8; i = i + 1) begin
                cmd_rx = (tx_ctrl_cmd[j] >> i) & 1'b1;
                #BIT_TIME;
            end
            
            // Parity bit (odd parity)
            cmd_rx = ~(^tx_ctrl_cmd[j]); // XOR all bits and invert for odd parity
            #BIT_TIME;
            
            // Stop bit
            cmd_rx = 1;
            #BIT_TIME;
            
            // Small delay between bytes
            #(BIT_TIME/2);
        end
        
        case (cmd_type)
            CMD_SOFTWARE_INJECTION_REQ: $display("Time %0t: Control command sent: Software Injection Request", $time);
            CMD_SOFTWARE_INJECTION_END: $display("Time %0t: Control command sent: Software Injection End", $time);
            CMD_ONLINE_PROGRAM_START: $display("Time %0t: Control command sent: Online Program Start", $time);
            CMD_LARGE_RANGE_MODE: $display("Time %0t: Control command sent: Large Range Mode", $time);
            CMD_SMALL_RANGE_MODE: $display("Time %0t: Control command sent: Small Range Mode", $time);
            CMD_SOFTWARE_RESET: $display("Time %0t: Control command sent: Software Reset", $time);
            CMD_INJECTION_MODE: $display("Time %0t: Control command sent: Injection Mode", $time);
            default: $display("Time %0t: Control command sent: Unknown Type (0x%h)", $time, cmd_type);
        endcase
    end
endtask

// Display complete telemetry data packet
task display_tlm_packet;
    begin
        $display("====== COMPLETE TELEMETRY DATA PACKET ======");
        $display("Frame Header: 0x%h%h", tlm_data[0], tlm_data[1]);
        $display("Data Length: 0x%h%h", tlm_data[2], tlm_data[3]);
        $display("Service Type: 0x%h", tlm_data[4]);
        $display("Service Status: 0x%h", tlm_data[5]);
        
        // Frame counter and timing information
        $display("Frame Counter: 0x%h%h%h", tlm_data[6], tlm_data[7], tlm_data[8]);
        $display("Sampling Time (microseconds): 0x%h%h%h", tlm_data[9], tlm_data[10], tlm_data[11]);
        $display("Sampling Time (seconds): 0x%h%h%h%h", tlm_data[12], tlm_data[13], tlm_data[14], tlm_data[15]);
        
        // Working mode and temperature data
        $display("Working Mode Status: 0x%h", tlm_data[16]);
        $display("Temperature Data:");
        $display("  Channel 1: 0x%h%h%h", tlm_data[17], tlm_data[18], tlm_data[19]);
        $display("  Channel 2: 0x%h%h%h", tlm_data[20], tlm_data[21], tlm_data[22]);
        $display("  Channel 3: 0x%h%h%h", tlm_data[23], tlm_data[24], tlm_data[25]);
        $display("  Channel 4: 0x%h%h%h", tlm_data[26], tlm_data[27], tlm_data[28]);
        
        // Displacement data
        $display("Displacement Data:");
        $display("  X1 Channel: 0x%h%h%h", tlm_data[29], tlm_data[30], tlm_data[31]);
        $display("  X2 Channel: 0x%h%h%h", tlm_data[32], tlm_data[33], tlm_data[34]);
        $display("  Y1 Channel: 0x%h%h%h", tlm_data[35], tlm_data[36], tlm_data[37]);
        $display("  Y2 Channel: 0x%h%h%h", tlm_data[38], tlm_data[39], tlm_data[40]);
        $display("  Z1 Channel: 0x%h%h%h", tlm_data[41], tlm_data[42], tlm_data[43]);
        $display("  Z2 Channel: 0x%h%h%h", tlm_data[44], tlm_data[45], tlm_data[46]);
        
        // Acceleration data
        $display("Acceleration Data:");
        $display("  X1 Channel: 0x%h%h%h", tlm_data[47], tlm_data[48], tlm_data[49]);
        $display("  X2 Channel: 0x%h%h%h", tlm_data[50], tlm_data[51], tlm_data[52]);
        $display("  Y1 Channel: 0x%h%h%h", tlm_data[53], tlm_data[54], tlm_data[55]);
        $display("  Y2 Channel: 0x%h%h%h", tlm_data[56], tlm_data[57], tlm_data[58]);
        $display("  Z1 Channel: 0x%h%h%h", tlm_data[59], tlm_data[60], tlm_data[61]);
        $display("  Z2 Channel: 0x%h%h%h", tlm_data[62], tlm_data[63], tlm_data[64]);
        
        // Phase data
        $display("Phase Data:");
        $display("  X1 Channel: 0x%h%h", tlm_data[65], tlm_data[66]);
        $display("  X2 Channel: 0x%h%h", tlm_data[67], tlm_data[68]);
        $display("  Y1 Channel: 0x%h%h", tlm_data[69], tlm_data[70]);
        $display("  Y2 Channel: 0x%h%h", tlm_data[71], tlm_data[72]);
        $display("  Z1 Channel: 0x%h%h", tlm_data[73], tlm_data[74]);
        $display("  Z2 Channel: 0x%h%h", tlm_data[75], tlm_data[76]);
        
        // ADC/DAC offset values
        $display("ADC/DAC Offset Values:");
        $display("  X1 Channel: 0x%h%h%h", tlm_data[77], tlm_data[78], tlm_data[79]);
        $display("  X2 Channel: 0x%h%h%h", tlm_data[80], tlm_data[81], tlm_data[82]);
        $display("  Y1 Channel: 0x%h%h%h", tlm_data[83], tlm_data[84], tlm_data[85]);
        $display("  Y2 Channel: 0x%h%h%h", tlm_data[86], tlm_data[87], tlm_data[88]);
        $display("  Z1 Channel: 0x%h%h%h", tlm_data[89], tlm_data[90], tlm_data[91]);
        $display("  Z2 Channel: 0x%h%h%h", tlm_data[92], tlm_data[93], tlm_data[94]);
        $display("  Offset Setting Index: 0x%h", tlm_data[95]);
        
        // PID parameters
        $display("PID Parameters:");
        $display("  X1/Y1/Z1 Proportional: 0x%h%h%h%h", tlm_data[96], tlm_data[97], tlm_data[98], tlm_data[99]);
        $display("  X1/Y1/Z1 Integral: 0x%h%h%h%h", tlm_data[100], tlm_data[101], tlm_data[102], tlm_data[103]);
        $display("  X1/Y1/Z1 Differential: 0x%h%h%h%h", tlm_data[104], tlm_data[105], tlm_data[106], tlm_data[107]);
        $display("  X2/Y2/Z2 Proportional: 0x%h%h%h%h", tlm_data[108], tlm_data[109], tlm_data[110], tlm_data[111]);
        $display("  X2/Y2/Z2 Integral: 0x%h%h%h%h", tlm_data[112], tlm_data[113], tlm_data[114], tlm_data[115]);
        $display("  X2/Y2/Z2 Differential: 0x%h%h%h%h", tlm_data[116], tlm_data[117], tlm_data[118], tlm_data[119]);
        $display("  PID Parameter Setting Index: 0x%h", tlm_data[120]);
        
        // Additional parameters
        $display("Additional Parameters:");
        $display("  Vd Detection Voltage: 0x%h%h%h", tlm_data[121], tlm_data[122], tlm_data[123]);
        $display("  Mode Vinct1 Threshold: 0x%h%h%h", tlm_data[124], tlm_data[125], tlm_data[126]);
        $display("  Mode Vinct2 Threshold: 0x%h%h%h", tlm_data[127], tlm_data[128], tlm_data[129]);
        $display("  Mode Vinct3 Threshold: 0x%h%h%h", tlm_data[130], tlm_data[131], tlm_data[132]);
        $display("  Time t1 Threshold: 0x%h%h", tlm_data[133], tlm_data[134]);
        $display("  Time t3 Value: 0x%h%h", tlm_data[135], tlm_data[136]);
        $display("  Integration Threshold: 0x%h%h%h", tlm_data[137], tlm_data[138], tlm_data[139]);
        
        // Status information
        $display("Status Information:");
        $display("  Injection Package Counter: 0x%h%h", tlm_data[140], tlm_data[141]);
        $display("  DSP Program Version: 0x%h", tlm_data[142]);
        $display("  Self-test Status: 0x%h%h", tlm_data[143], tlm_data[144]);
        $display("  Correct Command Counter: 0x%h", tlm_data[145]);
        $display("  Last Correct Command Type: 0x%h", tlm_data[146]);
        $display("  Error Command Counter: 0x%h", tlm_data[147]);
        $display("  Last Error Command Type: 0x%h", tlm_data[148]);
        $display("  Command Error Type: 0x%h", tlm_data[149]);
        $display("  UART Reset Counter: 0x%h", tlm_data[150]);
        $display("  Reserved Byte: 0x%h", tlm_data[151]);
        
        // Checksum and frame tail
        $display("Checksum: 0x%h%h", tlm_data[152], tlm_data[153]);
        $display("Frame Tail: 0x%h%h", tlm_data[154], tlm_data[155]);
        
        // Response time
        $display("Response time: %0t ns", $time);
        $display("=============================================");
    end
endtask

endmodule