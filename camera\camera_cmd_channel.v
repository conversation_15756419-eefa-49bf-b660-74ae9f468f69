/**
 * 卫星监视相机命令处理模块
 * 功能：接收并解析来自星务计算机的命令，包括复位指令、遥测请求和遥控指令
 * 协议：RS422异步串行通信(115200bps，奇校验)
 */
module camera_cmd_channel (
    input wire clk,                       // 系统时钟,50MHz
    input wire rst_n,                     // 系统复位,低电平有效
    input wire [7:0] baud_val,            // 波特率设置
    input wire rs422_rx,                  // RS422接收引脚
    
    // 共享状态接口 - 输出信号
    output reg inc_cmd_cnt,               // 增加遥控指令计数
    output reg inc_cmd_err_cnt,           // 增加指令错误计数
    output reg inc_tm_req_cnt,            // 增加遥测请求计数
    output reg inc_uart_rst_cnt,          // 增加串口复位计数
    
    // 相机控制指令信号
    output reg update_cam_a_power,        // 更新相机A加电状态
    output reg update_cam_b_power,        // 更新相机B加电状态
    output reg update_cam_c_power,        // 更新相机C加电状态
    output reg [1:0] cam_a_power_val,     // 相机A加电状态值
    output reg [1:0] cam_b_power_val,     // 相机B加电状态值
    output reg [1:0] cam_c_power_val,     // 相机C加电状态值
    
    // 压缩核对接关系更新
    output reg update_core_connection,    // 更新压缩核对接关系
    output reg [1:0] core_a_connect,      // 压缩核A对接哪个相机
    output reg [1:0] core_b_connect,      // 压缩核B对接哪个相机
    
    // LVDS状态更新
    output reg update_lvds_status,        // 更新LVDS状态
    output reg [7:0] lvds_status_val,     // LVDS状态值
    
    // 相机灰度更新
    output reg update_cam_a_expect_gray,  // 更新相机A期望灰度
    output reg update_cam_b_expect_gray,  // 更新相机B期望灰度
    output reg update_cam_c_expect_gray,  // 更新相机C期望灰度
    output reg [7:0] cam_a_expect_gray,   // 相机A期望灰度值
    output reg [7:0] cam_b_expect_gray,   // 相机B期望灰度值
    output reg [7:0] cam_c_expect_gray,   // 相机C期望灰度值
    
    // 时间码更新
    output reg update_time_code,          // 更新时间码
    output reg [31:0] new_time_code,      // 新时间码值
    
    // 最近指令码更新
    output reg update_last_cmd,           // 更新最近指令码
    output reg [7:0] last_cmd_val,        // 最近指令码值
    
    // 遥测请求触发
    output reg tm_req_received,           // 收到遥测请求信号
    output reg trig_telemetry             // 触发遥测发送
);

    // ================== 常量定义 ==================
    // 帧结构参数
    localparam FRAME_HEAD       = 16'hEB90;  // 帧头
    localparam FRAME_TAIL       = 16'h09D7;  // 帧尾
    
    // 数据长度定义
    localparam CMD_DATA_LEN     = 16'h0008;  // 命令帧数据长度(8字节)
    
    // 服务类型定义
    localparam SVC_TELEMETRY_REQ = 8'h01;    // 遥测请求
    localparam SVC_RESET         = 8'h03;    // 设备复位
    localparam SVC_TC_CMD        = 8'h08;    // 遥控指令
    
    // 遥控指令CMD类型定义
    localparam CMD_CAM_POWER     = 8'h01;    // 相机加电通道选择
    localparam CMD_TIME_CODE     = 8'h02;    // 授时指令
    localparam CMD_LVDS_ON       = 8'h03;    // LVDS图像下传开
    localparam CMD_LVDS_OFF      = 8'h04;    // LVDS图像下传关
    localparam CMD_CAM_CHECK     = 8'h11;    // 相机自检
    localparam CMD_CAM_GRAY      = 8'h12;    // 期望灰度设置
    
    // 相机目标定义
    localparam CAM_A_TARGET      = 8'h11;    // 相机A目标
    localparam CAM_B_TARGET      = 8'h22;    // 相机B目标
    localparam CAM_C_TARGET      = 8'h33;    // 相机C目标
    
    // 接收缓冲区大小
    localparam RX_BUFFER_SIZE    = 32;       // 接收缓冲区大小
    localparam RX_BUFFER_MASK    = 5'h1F;    // 接收缓冲区掩码
    
    // 命令帧长度(固定16字节)
    localparam CMD_FRAME_LEN     = 16;
    
    // 状态机状态定义
    localparam RX_IDLE      = 4'h0; // 空闲状态
    localparam RX_HEAD_1    = 4'h1; // 帧头1
    localparam RX_HEAD_2    = 4'h2; // 帧头2
    localparam RX_LEN_1     = 4'h3; // 长度1
    localparam RX_LEN_2     = 4'h4; // 长度2
    localparam RX_SERVICE   = 4'h5; // 服务类型
    localparam RX_STATUS    = 4'h6; // 服务状态
    localparam RX_DATA      = 4'h7; // 数据域
    localparam RX_CHECK_1   = 4'h8; // 校验码1
    localparam RX_CHECK_2   = 4'h9; // 校验码2
    localparam RX_TAIL_1    = 4'hA; // 帧尾1
    localparam RX_TAIL_2    = 4'hB; // 帧尾2
    localparam RX_PROCESS   = 4'hC; // 处理命令
    
    // ================== 内部信号和寄存器 ==================
    // UART接口信号
    reg cs_n;                      // 片选信号
    reg re_n;                      // 读使能信号
    wire [7:0] rx_data;            // 接收数据
    wire rxrdy;                    // 接收就绪标志
    reg rxrdy_d1;                  // 寄存器捕获rxrdy的上一个状态   
    wire rxrdy_posedge;            // rxrdy的上升沿脉冲信号
    assign rxrdy_posedge = rxrdy & ~rxrdy_d1;
    wire parity_err;               // 奇偶校验错误标志
    
    // 接收状态机
    reg [3:0] rx_state;            // 接收状态
    reg [4:0] rx_byte_cnt;         // 接收字节计数
    reg [7:0] rx_buffer [0:RX_BUFFER_SIZE-1]; // 接收缓冲区
    reg [4:0] rx_head;             // 接收缓冲区头指针
    reg [4:0] rx_tail;             // 接收缓冲区尾指针
    reg [7:0] data_length_h;       // 数据长度高字节
    reg [7:0] data_length_l;       // 数据长度低字节
    reg [15:0] data_length;        // 数据长度
    reg [15:0] checksum;           // 校验和
    reg [7:0] cmd_service_type;    // 服务类型
    reg [7:0] cmd_service_status;  // 服务状态
    
    // 遥控指令解析变量
    reg [7:0] tc_cmd;              // 遥控指令CMD
    reg [7:0] tc_w1;               // 遥控指令参数W1
    reg [7:0] tc_w2;               // 遥控指令参数W2
    reg [7:0] tc_w3;               // 遥控指令参数W3
    reg [7:0] tc_w4;               // 遥控指令参数W4
    reg [7:0] tc_w5;               // 遥控指令参数W5
    
    // ================== UART实例化 ==================
    COREUART uart_inst (
        .clk(clk),
        .rst_n(rst_n),
        
        .cs_n(cs_n),
        .baud_val(baud_val),
        .bit8(1'b1),        // 8位数据位
        .parity_en(1'b1),   // 使能奇偶校验
        .odd_n_even(1'b1),  // 奇校验
        
        .re_n(re_n),        // 读使能信号
        .rx(rs422_rx),      // 接收线
        .rxrdy(rxrdy),      // 接收就绪标志
        .data_out(rx_data), // 接收数据
        .parity_err(parity_err), // 奇偶校验错误
        
        .we_n(1'b1),        // 禁用发送功能
        .data_in(),    // 发送数据不使用
        .txrdy(),           // 发送就绪标志不使用
        .tx(),              // 发送线不使用
        .overflow()         // 溢出标志不使用
    );
    // 寄存器更新逻辑，保存rxrdy的上一个状态
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            rxrdy_d1 <= 1'b0;  
        end
        else begin  
            rxrdy_d1 <= rxrdy;  // 保存当前状态
        end
    end 
    // ================== 接收状态机实现 ==================
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            // 复位初始化
            rx_state <= RX_IDLE;
            rx_byte_cnt <= 5'h00;
            cs_n <= 1'b0;
            re_n <= 1'b1;
            rx_head <= 5'h00;
            rx_tail <= 5'h00;
            data_length_h <= 8'h00;
            data_length_l <= 8'h00;
            data_length <= 16'h0000;
            checksum <= 16'h0000;
            cmd_service_type <= 8'h00;
            cmd_service_status <= 8'h00;
            
            // 遥控指令参数
            tc_cmd <= 8'h00;
            tc_w1 <= 8'h00;
            tc_w2 <= 8'h00;
            tc_w3 <= 8'h00;
            tc_w4 <= 8'h00;
            tc_w5 <= 8'h00;
            
            // 输出信号初始化
            inc_cmd_cnt <= 1'b0;
            inc_cmd_err_cnt <= 1'b0;
            inc_tm_req_cnt <= 1'b0;
            inc_uart_rst_cnt <= 1'b0;
            
            update_cam_a_power <= 1'b0;
            update_cam_b_power <= 1'b0;
            update_cam_c_power <= 1'b0;
            cam_a_power_val <= 2'b00;
            cam_b_power_val <= 2'b00;
            cam_c_power_val <= 2'b00;
            
            update_core_connection <= 1'b0;
            core_a_connect <= 2'b00;
            core_b_connect <= 2'b00;
            
            update_lvds_status <= 1'b0;
            lvds_status_val <= 8'h00;
            
            update_cam_a_expect_gray <= 1'b0;
            update_cam_b_expect_gray <= 1'b0;
            update_cam_c_expect_gray <= 1'b0;
            cam_a_expect_gray <= 8'h00;
            cam_b_expect_gray <= 8'h00;
            cam_c_expect_gray <= 8'h00;
            
            update_time_code <= 1'b0;
            new_time_code <= 32'h00000000;
            
            update_last_cmd <= 1'b0;
            last_cmd_val <= 8'h00;
            
            tm_req_received <= 1'b0;
            trig_telemetry <= 1'b0;
        end
        else begin
            // 默认状态，所有触发信号归零
            inc_cmd_cnt <= 1'b0;
            inc_cmd_err_cnt <= 1'b0;
            inc_tm_req_cnt <= 1'b0;
            inc_uart_rst_cnt <= 1'b0;
            
            update_cam_a_power <= 1'b0;
            update_cam_b_power <= 1'b0;
            update_cam_c_power <= 1'b0;
            
            update_core_connection <= 1'b0;
            
            update_lvds_status <= 1'b0;
            
            update_cam_a_expect_gray <= 1'b0;
            update_cam_b_expect_gray <= 1'b0;
            update_cam_c_expect_gray <= 1'b0;
            
            update_time_code <= 1'b0;
            
            update_last_cmd <= 1'b0;
            
            tm_req_received <= 1'b0;
            trig_telemetry <= 1'b0;
            
            // 处理UART接收
            if (rxrdy && !re_n) begin
                // 接收到数据，存入缓冲区
                rx_buffer[rx_head] <= rx_data;
                rx_head <= (rx_head + 1'b1) & RX_BUFFER_MASK;
                re_n <= 1'b1;
            end
            else if (rxrdy_posedge) begin
                // 准备接收新数据
                re_n <= 1'b0;
            end
            
            // 检测到奇偶校验错误
            if (parity_err) begin
                inc_cmd_err_cnt <= 1'b1;
            end
            
            // 接收状态机
            case (rx_state)
                RX_IDLE: begin
                    // 检查是否有数据可以处理
                    if (rx_head != rx_tail) begin
                        // 检查帧头第一个字节
                        if (rx_buffer[rx_tail] == FRAME_HEAD[15:8]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_HEAD_2;
                        end
                        else begin
                            // 无效数据，跳过
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        end
                    end
                end
                
                RX_HEAD_2: begin
                    // 检查帧头第二个字节
                    if (rx_head != rx_tail) begin
                        if (rx_buffer[rx_tail] == FRAME_HEAD[7:0]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_LEN_1;
                        end
                        else begin
                            // 帧头错误，返回空闲状态
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_IDLE;
                        end
                    end
                end
                
                RX_LEN_1: begin
                    // 接收数据长度高字节
                    if (rx_head != rx_tail) begin
                        data_length_h <= rx_buffer[rx_tail];
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        rx_state <= RX_LEN_2;
                    end
                end
                
                RX_LEN_2: begin
                    // 接收数据长度低字节
                    if (rx_head != rx_tail) begin
                        data_length_l <= rx_buffer[rx_tail];
                        data_length <= {data_length_h, rx_buffer[rx_tail]};
                        rx_byte_cnt <= 5'h00;
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        rx_state <= RX_SERVICE;
                        
                        // 初始化校验和计算
                        checksum <= {data_length_h, rx_buffer[rx_tail]};
                    end
                end
                
                RX_SERVICE: begin
                    // 接收服务类型
                    if (rx_head != rx_tail) begin
                        cmd_service_type <= rx_buffer[rx_tail];
                        checksum <= checksum + rx_buffer[rx_tail];
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        rx_state <= RX_STATUS;
                    end
                end
                
                RX_STATUS: begin
                    // 接收服务状态
                    if (rx_head != rx_tail) begin
                        cmd_service_status <= rx_buffer[rx_tail];
                        checksum <= checksum + rx_buffer[rx_tail];
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        
                        // 检查数据长度是否为8(0x0008)
                        if (data_length == CMD_DATA_LEN) begin
                            rx_state <= RX_DATA;
                        end
                        else begin
                            // 数据长度错误，跳过帧
                            inc_cmd_err_cnt <= 1'b1;
                            rx_state <= RX_IDLE;
                        end
                    end
                end
                
                RX_DATA: begin
                    // 接收数据字段
                    if (rx_head != rx_tail) begin
                        // 根据服务类型处理数据字段
                        case (cmd_service_type)
                            SVC_TELEMETRY_REQ: begin
                                // 遥测请求 - 空数据块不处理，仅累加校验和
                                checksum <= checksum + rx_buffer[rx_tail];
                            end
                            
                            SVC_RESET: begin
                                // 设备复位 - 空数据块不处理，仅累加校验和
                                checksum <= checksum + rx_buffer[rx_tail];
                            end
                            
                            SVC_TC_CMD: begin
                                // 遥控指令 - 处理6字节参数
                                case (rx_byte_cnt)
                                    5'h00: tc_cmd <= rx_buffer[rx_tail]; // 指令字
                                    5'h01: tc_w1 <= rx_buffer[rx_tail];  // 参数1
                                    5'h02: tc_w2 <= rx_buffer[rx_tail];  // 参数2
                                    5'h03: tc_w3 <= rx_buffer[rx_tail];  // 参数3
                                    5'h04: tc_w4 <= rx_buffer[rx_tail];  // 参数4
                                    5'h05: tc_w5 <= rx_buffer[rx_tail];  // 参数5
                                endcase
                                
                                // 累加校验和
                                checksum <= checksum + rx_buffer[rx_tail];
                            end
                            
                            default: begin
                                // 未知服务类型，仅累加校验和
                                checksum <= checksum + rx_buffer[rx_tail];
                            end
                        endcase
                        
                        // 更新字节计数和尾指针
                        rx_byte_cnt <= rx_byte_cnt + 1'b1;
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        
                        // 检查是否接收完所有数据
                        if (rx_byte_cnt == 5'h05) begin
                            // 6字节数据完成，继续接收校验和
                            rx_state <= RX_CHECK_1;
                        end
                    end
                end
                
                RX_CHECK_1: begin
                    // 接收校验码高字节
                    if (rx_head != rx_tail) begin
                        if (rx_buffer[rx_tail] != checksum[15:8]) begin
                            // 校验和错误
                            inc_cmd_err_cnt <= 1'b1;
                            rx_state <= RX_IDLE;
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        end 
                        else begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_CHECK_2;
                        end
                    end
                end
                
                RX_CHECK_2: begin
                    // 接收校验码低字节
                    if (rx_head != rx_tail) begin
                        if (rx_buffer[rx_tail] != checksum[7:0]) begin
                            // 校验和错误
                            inc_cmd_err_cnt <= 1'b1;
                            rx_state <= RX_IDLE;
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        end 
                        else begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_TAIL_1;
                        end
                    end
                end
                
                RX_TAIL_1: begin
                    // 接收帧尾第一个字节
                    if (rx_head != rx_tail) begin
                        if (rx_buffer[rx_tail] == FRAME_TAIL[15:8]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_TAIL_2;
                        end
                        else begin
                            // 帧尾错误，返回空闲状态
                            inc_cmd_err_cnt <= 1'b1;
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_IDLE;
                        end
                    end
                end
                
                RX_TAIL_2: begin
                    // 接收帧尾第二个字节
                    if (rx_head != rx_tail) begin
                        if (rx_buffer[rx_tail] == FRAME_TAIL[7:0]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_PROCESS;
                        end
                        else begin
                            // 帧尾错误，返回空闲状态
                            inc_cmd_err_cnt <= 1'b1;
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_IDLE;
                        end
                    end
                end
                
                RX_PROCESS: begin
                    // 处理命令，根据服务类型执行相应操作
                    case (cmd_service_type)
                        SVC_TELEMETRY_REQ: begin
                            // 遥测请求处理
                            inc_tm_req_cnt <= 1'b1;     // 增加遥测请求计数
                            tm_req_received <= 1'b1;    // 表示收到遥测请求
                            trig_telemetry <= 1'b1;     // 触发遥测发送
                        end
                        
                        SVC_RESET: begin
                            // 设备复位处理
                            // 复位不需要任何响应，只通知上层状态更新
                            inc_uart_rst_cnt <= 1'b1;
                        end
                        
                        SVC_TC_CMD: begin
                            // 遥控指令处理
                            update_last_cmd <= 1'b1;    // 更新最近指令码
                            last_cmd_val <= tc_cmd;     // 设置最近指令码为当前指令
                            
                            // 根据CMD类型处理不同命令
                            case (tc_cmd)
                                CMD_CAM_POWER: begin
                                    // 相机加电通道选择指令
                                    // 处理压缩核A对接相机(W1)
                                    case (tc_w1)
                                        8'h00: begin
                                            // 关闭对接压缩核A相机
                                            update_core_connection <= 1'b1;
                                            core_a_connect <= 2'b11; // 无对接
                                        end
                                        
                                        CAM_A_TARGET: begin
                                            // 第1路相机对接压缩核A
                                            update_cam_a_power <= 1'b1;
                                            cam_a_power_val <= 2'b01; // 加电并对接压缩核A
                                            
                                            update_core_connection <= 1'b1;
                                            core_a_connect <= 2'b00; // 对接相机A
                                        end
                                        
                                        CAM_B_TARGET: begin
                                            // 第2路相机对接压缩核A
                                            update_cam_b_power <= 1'b1;
                                            cam_b_power_val <= 2'b01; // 加电并对接压缩核A
                                            
                                            update_core_connection <= 1'b1;
                                            core_a_connect <= 2'b01; // 对接相机B
                                        end
                                        
                                        CAM_C_TARGET: begin
                                            // 第3路相机对接压缩核A
                                            update_cam_c_power <= 1'b1;
                                            cam_c_power_val <= 2'b01; // 加电并对接压缩核A
                                            
                                            update_core_connection <= 1'b1;
                                            core_a_connect <= 2'b10; // 对接相机C
                                        end
                                        
                                        default: begin
                                            // 无效参数
                                            inc_cmd_err_cnt <= 1'b1;
                                        end
                                    endcase
                                    
                                    // 处理压缩核B对接相机(W2)
                                    case (tc_w2)
                                        8'h00: begin
                                            // 关闭对接压缩核B相机
                                            update_core_connection <= 1'b1;
                                            core_b_connect <= 2'b11; // 无对接
                                        end
                                        
                                        CAM_A_TARGET: begin
                                            // 第1路相机对接压缩核B
                                            update_cam_a_power <= 1'b1;
                                            cam_a_power_val <= 2'b10; // 加电并对接压缩核B
                                            
                                            update_core_connection <= 1'b1;
                                            core_b_connect <= 2'b00; // 对接相机A
                                        end
                                        
                                        CAM_B_TARGET: begin
                                            // 第2路相机对接压缩核B
                                            update_cam_b_power <= 1'b1;
                                            cam_b_power_val <= 2'b10; // 加电并对接压缩核B
                                            
                                            update_core_connection <= 1'b1;
                                            core_b_connect <= 2'b01; // 对接相机B
                                        end
                                        
                                        CAM_C_TARGET: begin
                                            // 第3路相机对接压缩核B
                                            update_cam_c_power <= 1'b1;
                                            cam_c_power_val <= 2'b10; // 加电并对接压缩核B
                                            
                                            update_core_connection <= 1'b1;
                                            core_b_connect <= 2'b10; // 对接相机C
                                        end
                                        
                                        default: begin
                                            // 无效参数
                                            inc_cmd_err_cnt <= 1'b1;
                                        end
                                    endcase
                                    
                                    // 指令处理完成，增加指令计数
                                    inc_cmd_cnt <= 1'b1;
                                end
                                
                                CMD_TIME_CODE: begin
                                    // 授时指令
                                    update_time_code <= 1'b1;
                                    new_time_code <= {tc_w1, tc_w2, tc_w3, tc_w4}; // 4字节时间码
                                    
                                    // 指令处理完成，增加指令计数
                                    inc_cmd_cnt <= 1'b1;
                                end
                                
                                CMD_LVDS_ON: begin
                                    // LVDS图像下传开
                                    update_lvds_status <= 1'b1;
                                    lvds_status_val <= 8'hAA; // 开启LVDS
                                    
                                    // 指令处理完成，增加指令计数
                                    inc_cmd_cnt <= 1'b1;
                                end
                                
                                CMD_LVDS_OFF: begin
                                    // LVDS图像下传关
                                    update_lvds_status <= 1'b1;
                                    lvds_status_val <= 8'h00; // 关闭LVDS
                                    
                                    // 指令处理完成，增加指令计数
                                    inc_cmd_cnt <= 1'b1;
                                end
                                
                                CMD_CAM_CHECK: begin
                                    // 相机自检指令
                                    case (tc_w1)
                                        CAM_A_TARGET: begin
                                            // 相机A自检
                                            if (tc_w2 == 8'h01) begin
                                                update_cam_a_power <= 1'b1;
                                                cam_a_power_val <= 2'b01; // 重新加电并对接压缩核A
                                                inc_cmd_cnt <= 1'b1;
                                            end
                                            else begin
                                                inc_cmd_err_cnt <= 1'b1;
                                            end
                                        end
                                        
                                        CAM_B_TARGET: begin
                                            // 相机B自检
                                            if (tc_w2 == 8'h01) begin
                                                update_cam_b_power <= 1'b1;
                                                cam_b_power_val <= 2'b01; // 重新加电并对接压缩核A
                                                inc_cmd_cnt <= 1'b1;
                                            end
                                            else begin
                                                inc_cmd_err_cnt <= 1'b1;
                                            end
                                        end
                                        
                                        CAM_C_TARGET: begin
                                            // 相机C自检
                                            if (tc_w2 == 8'h01) begin
                                                update_cam_c_power <= 1'b1;
                                                cam_c_power_val <= 2'b01; // 重新加电并对接压缩核A
                                                inc_cmd_cnt <= 1'b1;
                                            end
                                            else begin
                                                inc_cmd_err_cnt <= 1'b1;
                                            end
                                        end
                                        
                                        default: begin
                                            // 无效相机目标
                                            inc_cmd_err_cnt <= 1'b1;
                                        end
                                    endcase
                                end
                                
                                CMD_CAM_GRAY: begin
                                    // 相机期望灰度设置
                                    case (tc_w1)
                                        CAM_A_TARGET: begin
                                            // 设置相机A期望灰度
                                            update_cam_a_expect_gray <= 1'b1;
                                            cam_a_expect_gray <= tc_w2; // W2为期望灰度值
                                            inc_cmd_cnt <= 1'b1;
                                        end
                                        
                                        CAM_B_TARGET: begin
                                            // 设置相机B期望灰度
                                            update_cam_b_expect_gray <= 1'b1;
                                            cam_b_expect_gray <= tc_w2; // W2为期望灰度值
                                            inc_cmd_cnt <= 1'b1;
                                        end
                                        
                                        CAM_C_TARGET: begin
                                            // 设置相机C期望灰度
                                            update_cam_c_expect_gray <= 1'b1;
                                            cam_c_expect_gray <= tc_w2; // W2为期望灰度值
                                            inc_cmd_cnt <= 1'b1;
                                        end
                                        
                                        default: begin
                                            // 无效相机目标
                                            inc_cmd_err_cnt <= 1'b1;
                                        end
                                    endcase
                                end
                                
                                default: begin
                                    // 未知指令类型
                                    inc_cmd_err_cnt <= 1'b1;
                                end
                            endcase
                        end
                        
                        default: begin
                            // 未知服务类型
                            inc_cmd_err_cnt <= 1'b1;
                        end
                    endcase
                    
                    // 返回空闲状态
                    rx_state <= RX_IDLE;
                end
                
                default: begin
                    rx_state <= RX_IDLE;
                end
            endcase
        end
    end
endmodule 