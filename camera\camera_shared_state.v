module camera_shared_state (
    input wire clk,                      // 系统时钟,50MHz
    input wire rst_n,                    // 系统复位,低电平有效
    
    // 通信计数器更新接口
    input wire inc_cmd_cnt,              // 增加遥控指令计数器
    input wire inc_cmd_err_cnt,          // 增加指令错误计数器
    input wire inc_tm_req_cnt,           // 增加遥测请求计数器
    input wire inc_uart_rst_cnt,         // 增加串口复位计数器
    
    // 相机状态更新接口
    input wire update_cam_a_power,       // 更新相机A加电状态
    input wire update_cam_b_power,       // 更新相机B加电状态
    input wire update_cam_c_power,       // 更新相机C加电状态
    input wire [1:0] cam_a_power_val,    // 相机A加电状态值
    input wire [1:0] cam_b_power_val,    // 相机B加电状态值
    input wire [1:0] cam_c_power_val,    // 相机C加电状态值
    
    // 压缩核对接关系更新
    input wire update_core_connection,   // 更新压缩核对接关系
    input wire [1:0] core_a_connect,     // 压缩核A对接哪个相机(00:A,01:B,10:C,11:无)
    input wire [1:0] core_b_connect,     // 压缩核B对接哪个相机(00:A,01:B,10:C,11:无)
    
    // LVDS状态更新
    input wire update_lvds_status,       // 更新LVDS状态
    input wire [7:0] lvds_status_val,    // LVDS状态值(0x00:关闭,0xAA:开启)
    
    // 相机期望灰度更新
    input wire update_cam_a_expect_gray, // 更新相机A期望灰度
    input wire update_cam_b_expect_gray, // 更新相机B期望灰度
    input wire update_cam_c_expect_gray, // 更新相机C期望灰度
    input wire [7:0] cam_a_expect_gray,  // 相机A期望灰度值
    input wire [7:0] cam_b_expect_gray,  // 相机B期望灰度值
    input wire [7:0] cam_c_expect_gray,  // 相机C期望灰度值
    
    // 时间码更新
    input wire update_time_code,         // 更新时间码
    input wire [31:0] new_time_code,     // 新时间码值
    
    // 最近指令码更新
    input wire update_last_cmd,          // 更新最近指令码
    input wire [7:0] last_cmd_val,       // 最近指令码值
    
    // 通信超时检测
    input wire tm_req_received,          // 收到遥测请求信号

    // 状态读取接口 - 通信计数器
    output reg [7:0] cmd_cnt,            // 遥控指令计数器
    output reg [7:0] cmd_err_cnt,        // 指令错误计数器
    output reg [7:0] tm_req_cnt,         // 遥测请求计数器
    output reg [7:0] uart_rst_cnt,       // 串口复位计数器
    
    // 状态读取接口 - 设备自检状态
    output reg [7:0] dev_check_status1,  // 设备自检状态1
    output reg [7:0] dev_check_status2,  // 设备自检状态2
    
    // 状态读取接口 - 相机状态
    output reg [7:0] cam_a_status1,      // 相机A状态1
    output reg [7:0] cam_a_status2,      // 相机A状态2
    output reg [7:0] cam_b_status1,      // 相机B状态1
    output reg [7:0] cam_b_status2,      // 相机B状态2
    output reg [7:0] cam_c_status1,      // 相机C状态1
    output reg [7:0] cam_c_status2,      // 相机C状态2
    
    // 状态读取接口 - 压缩与码率
    output reg [7:0] compress_mode,      // 压缩模式状态
    output reg [7:0] spi_data,           // SPI接口数据
    output reg [15:0] core_a_bitrate,    // 压缩核A码率
    output reg [15:0] core_b_bitrate,    // 压缩核B码率
    
    // 状态读取接口 - LVDS与FIFO
    output reg [7:0] lvds_status,        // LVDS输出状态
    output reg [7:0] fifo_status,        // FIFO空满状态
    
    // 状态读取接口 - 相机参数
    output reg [7:0] last_cmd,           // 最近指令码
    output reg [15:0] cam_a_exposure,    // 相机A曝光
    output reg [7:0] cam_a_expect_gray_val,  // 相机A期望灰度
    output reg [7:0] cam_a_actual_gray,      // 相机A实际灰度
    output reg [15:0] cam_b_exposure,        // 相机B曝光
    output reg [7:0] cam_b_expect_gray_val,  // 相机B期望灰度
    output reg [7:0] cam_b_actual_gray,      // 相机B实际灰度
    output reg [15:0] cam_c_exposure,        // 相机C曝光
    output reg [7:0] cam_c_expect_gray_val,  // 相机C期望灰度
    output reg [7:0] cam_c_actual_gray,      // 相机C实际灰度
    
    // 状态读取接口 - 时间信息
    output reg [31:0] time_code          // 时间码
);

    // ================ 超时检测计数器 ================
    reg [31:0] timeout_counter;          
    localparam TIMEOUT_THRESHOLD = 32'd400_000_000; // 10秒 = 40MHz * 10

    // ================ 参数初始值 ================
    // 各相机默认曝光值(范围1~1080)
    localparam DEFAULT_EXPOSURE = 16'h01F4; // 500
    // 各相机默认期望灰度(范围0~255)
    localparam DEFAULT_EXPECT_GRAY = 8'h5A; // 90
    // 压缩核默认码率4Mbps
    localparam DEFAULT_BITRATE = 16'h0FA0; // 4000(单位:256bps)
    // 1秒计数器阈值(50MHz时钟下50,000,000个周期为1秒)
    localparam ONE_SECOND = 32'd40_000_000;
    reg [31:0] second_counter;     // 秒计数器
    reg time_code_tick; //时间码自增脉冲(1秒1次)

     // ================== 秒脉冲产生逻辑 ==================
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            // 复位初始化
            second_counter <= 32'd0;
            time_code_tick <= 1'b0;
        end
        else begin
            // 默认状态
            time_code_tick <= 1'b0;
            // 秒计数器
            if (second_counter >= ONE_SECOND - 1) begin
                // 计数达到1秒
                second_counter <= 32'd0;
                time_code_tick <= 1'b1;  // 产生一个周期的脉冲
            end
            else begin
                second_counter <= second_counter + 1'b1;
            end
        end
    end

    // ================ 状态初始化和更新逻辑 ================
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            // 复位所有状态变量
            // 通信计数器
            cmd_cnt <= 8'h00;
            cmd_err_cnt <= 8'h00;
            tm_req_cnt <= 8'h00;
            uart_rst_cnt <= 8'h00;
            
            // 设备自检状态
            dev_check_status1 <= 8'hFF; // 全部完成
            dev_check_status2 <= 8'h01; // 默认压缩核A连接相机A,压缩核B连接相机B
            
            // 相机状态 - 默认为有效状态
            cam_a_status1 <= 8'h0E; // 相机断电,通信有效,SDI有效,BT1120有效
            cam_a_status2 <= 8'h01; // 纠码解码有效
            cam_b_status1 <= 8'h0E; // 相机断电,通信有效,SDI有效,BT1120有效
            cam_b_status2 <= 8'h01; // 纠码解码有效
            cam_c_status1 <= 8'h0E; // 相机断电,通信有效,SDI有效,BT1120有效
            cam_c_status2 <= 8'h01; // 纠码解码有效
            
            // 压缩与码率
            compress_mode <= 8'h66; // 压缩核A和B均为压缩4Mbps
            spi_data <= 8'h66;      // 压缩核A和B均有数据
            core_a_bitrate <= DEFAULT_BITRATE; // 4Mbps
            core_b_bitrate <= DEFAULT_BITRATE; // 4Mbps
            
            // LVDS与FIFO
            lvds_status <= 8'h00;   // LVDS默认关闭
            fifo_status <= 8'h00;   // FIFO正常
            
            // 相机参数
            last_cmd <= 8'h00;              // 最近指令码
            cam_a_exposure <= DEFAULT_EXPOSURE; // 相机A曝光
            cam_a_expect_gray_val <= DEFAULT_EXPECT_GRAY; // 相机A期望灰度
            cam_a_actual_gray <= DEFAULT_EXPECT_GRAY;     // 相机A实际灰度
            cam_b_exposure <= DEFAULT_EXPOSURE; // 相机B曝光
            cam_b_expect_gray_val <= DEFAULT_EXPECT_GRAY; // 相机B期望灰度
            cam_b_actual_gray <= DEFAULT_EXPECT_GRAY;     // 相机B实际灰度
            cam_c_exposure <= DEFAULT_EXPOSURE; // 相机C曝光
            cam_c_expect_gray_val <= DEFAULT_EXPECT_GRAY; // 相机C期望灰度
            cam_c_actual_gray <= DEFAULT_EXPECT_GRAY;     // 相机C实际灰度
            
            // 时间信息
            time_code <= 32'h00000000;
            
            // 超时检测
            timeout_counter <= 32'd0;
            
        end
        else begin
            
            // ================ 通信计数器更新 ================
            if (inc_cmd_cnt)
                cmd_cnt <= cmd_cnt + 8'h01;
                
            if (inc_cmd_err_cnt)
                cmd_err_cnt <= cmd_err_cnt + 8'h01;
                
            if (inc_tm_req_cnt)
                tm_req_cnt <= tm_req_cnt + 8'h01;
                
            if (inc_uart_rst_cnt)
                uart_rst_cnt <= uart_rst_cnt + 8'h01;
                
            // ================ 相机状态更新 ================
            if (update_cam_a_power) begin
                // 更新相机A加电状态(Bit0)和对接压缩核状态(Bit7:4)
                cam_a_status1[0] <= 1'b1; // 加电状态

                if (cam_a_power_val==2'b01) begin
                    cam_a_status1[7:4] <= 4'b0011; // 对接压缩核A
                end
                else if (cam_a_power_val==2'b10) begin
                    cam_a_status1[7:4] <= 4'b1100; // 对接压缩核B
                end
                else begin
                    cam_a_status1[7:4] <= 4'b0000; // 无对接
                end
            end
                
            if (update_cam_b_power) begin
                // 更新相机B加电状态和对接压缩核状态
                cam_b_status1[0] <= 1'b1;
                
                if (cam_b_power_val==2'b01) begin
                cam_b_status1[7:4] <= 4'b0011; // 对接压缩核A
                end
                else if (cam_b_power_val==2'b10) begin
                cam_b_status1[7:4] <= 4'b1100; // 对接压缩核B
                end
                else begin
                    cam_b_status1[7:4] <= 4'b0000; // 无对接
                end
            end
                
            if (update_cam_c_power) begin
                // 更新相机C加电状态和对接压缩核状态
                cam_c_status1[0] <= 1'b1;
                if (cam_c_power_val==2'b01) begin
                    cam_c_status1[7:4] <= 4'b0011; // 对接压缩核A
                end
                else if (cam_c_power_val==2'b10) begin
                    cam_c_status1[7:4] <= 4'b1100; // 对接压缩核B
                end
                else begin
                    cam_c_status1[7:4] <= 4'b0000; // 无对接
                end
            end
                
            // ================ 压缩核对接关系更新 ================
            if (update_core_connection) begin
                // 更新设备自检状态2的压缩核对接相机位域
                dev_check_status2[3:2] <= core_a_connect; // Bit[3:2] = 压缩核A对接相机
                dev_check_status2[1:0] <= core_b_connect; // Bit[1:0] = 压缩核B对接相机
            end
                
            // ================ LVDS状态更新 ================
            if (update_lvds_status) begin
                lvds_status <= lvds_status_val;
            end
                
            // ================ 相机期望灰度更新 ================
            if (update_cam_a_expect_gray) begin
                cam_a_expect_gray_val <= cam_a_expect_gray;
                cam_a_actual_gray <= cam_a_expect_gray;  // 模拟系统中实际灰度等于期望灰度
            end
                
            if (update_cam_b_expect_gray) begin
                cam_b_expect_gray_val <= cam_b_expect_gray;
                cam_b_actual_gray <= cam_b_expect_gray;
            end
                
            if (update_cam_c_expect_gray) begin
                cam_c_expect_gray_val <= cam_c_expect_gray;
                cam_c_actual_gray <= cam_c_expect_gray;
            end
                
            // ================ 时间码更新 ================
            if (update_time_code) begin
                time_code <= new_time_code;
            end
            else if (time_code_tick) begin
                // 每秒时间码加1
                time_code <= time_code + 32'h00000001;
            end
                
            // ================ 最近指令码更新 ================
            if (update_last_cmd) begin
                last_cmd <= last_cmd_val;
            end
                
            // ================ 超时检测逻辑 ================
            if (tm_req_received) begin
                // 收到遥测请求,重置超时计数器
                timeout_counter <= 32'd0;
            end
            else begin
                // 增加超时计数器
                timeout_counter <= timeout_counter + 32'd1;
                // 如果超过10秒无遥测请求,触发超时
                if (timeout_counter >= TIMEOUT_THRESHOLD) begin
                    uart_rst_cnt <= uart_rst_cnt + 8'h01;
                    timeout_counter <= 32'd0;
                end
            end
        end
    end

endmodule 