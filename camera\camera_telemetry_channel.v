/**
 * 卫星监视相机遥测发送模块
 * 功能：构建并发送48字节遥测数据帧，响应遥测请求
 * 协议：RS422异步串行通信(115200bps，奇校验)
 */
module camera_telemetry_channel (
    input wire clk,                        // 系统时钟,50MHz
    input wire rst_n,                      // 系统复位,低电平有效
    input wire [7:0] baud_val,             // 波特率设置
    input wire trig_telemetry,             // 触发遥测发送
    
    // 状态读取接口 - 通信计数器
    input wire [7:0] cmd_cnt,              // 遥控指令计数器
    input wire [7:0] cmd_err_cnt,          // 指令错误计数器
    input wire [7:0] tm_req_cnt,           // 遥测请求计数器
    input wire [7:0] uart_rst_cnt,         // 串口复位计数器
    
    // 状态读取接口 - 设备自检状态
    input wire [7:0] dev_check_status1,    // 设备自检状态1
    input wire [7:0] dev_check_status2,    // 设备自检状态2
    
    // 状态读取接口 - 相机状态
    input wire [7:0] cam_a_status1,        // 相机A状态1
    input wire [7:0] cam_a_status2,        // 相机A状态2
    input wire [7:0] cam_b_status1,        // 相机B状态1
    input wire [7:0] cam_b_status2,        // 相机B状态2
    input wire [7:0] cam_c_status1,        // 相机C状态1
    input wire [7:0] cam_c_status2,        // 相机C状态2
    
    // 状态读取接口 - 压缩与码率
    input wire [7:0] compress_mode,        // 压缩模式状态
    input wire [7:0] spi_data,             // SPI接口数据
    input wire [15:0] core_a_bitrate,      // 压缩核A码率
    input wire [15:0] core_b_bitrate,      // 压缩核B码率
    
    // 状态读取接口 - LVDS与FIFO
    input wire [7:0] lvds_status,          // LVDS输出状态
    input wire [7:0] fifo_status,          // FIFO空满状态
    
    // 状态读取接口 - 相机参数
    input wire [7:0] last_cmd,             // 最近指令码
    input wire [15:0] cam_a_exposure,      // 相机A曝光
    input wire [7:0] cam_a_expect_gray,    // 相机A期望灰度
    input wire [7:0] cam_a_actual_gray,    // 相机A实际灰度
    input wire [15:0] cam_b_exposure,      // 相机B曝光
    input wire [7:0] cam_b_expect_gray,    // 相机B期望灰度
    input wire [7:0] cam_b_actual_gray,    // 相机B实际灰度
    input wire [15:0] cam_c_exposure,      // 相机C曝光
    input wire [7:0] cam_c_expect_gray,    // 相机C期望灰度
    input wire [7:0] cam_c_actual_gray,    // 相机C实际灰度
    
    // 状态读取接口 - 时间信息
    input wire [31:0] time_code,           // 时间码
    
    // UART输出
    output wire tx                         // RS422发送引脚
);

    // ================== 常量定义 ==================
    // 遥测数据帧定义，固定48字节
    localparam TM_PACKET_SIZE = 48;
    
    // 帧结构参数
    localparam FRAME_HEAD       = 16'hEB90;  // 帧头
    localparam FRAME_TAIL       = 16'h09D7;  // 帧尾
    localparam TM_DATA_LEN      = 16'h0028;  // 遥测数据长度(40字节)
    localparam TM_SERVICE_TYPE  = 8'h01;     // 遥测服务类型
    localparam TM_SERVICE_STATUS = 8'h00;    // 遥测服务状态
    
    // 响应延时参数(2ms延时，50MHz时钟下)
    localparam RESPONSE_DELAY   = 20'd80000;
    
    // 状态机状态定义
    localparam IDLE       = 3'b000; // 空闲状态
    localparam BUILD_PKG  = 3'b001; // 构建数据包
    localparam SEND_PKG   = 3'b010; // 发送数据包
    localparam WAIT_DONE  = 3'b011; // 等待发送完成
    
    // ================== 内部信号和寄存器 ==================
    // UART接口信号
    reg cs_n;                      // 片选信号
    reg we_n;                      // 写使能信号
    reg [7:0] data_in;             // 待发送数据
    wire txrdy;                    // 发送就绪标志
    reg txrdy_prev;
    wire txrdy_rf;
    assign txrdy_rf = txrdy & ~txrdy_prev;
    // 发送状态机
    reg [2:0] state;               // 发送状态机状态
    reg [5:0] send_index;          // 发送索引(0-47)

    // 遥测数据包缓冲区
    reg [7:0] tm_data [0:TM_PACKET_SIZE-1];
    
    // ================== UART实例化 ==================
    COREUART uart_inst (
        .clk(clk),
        .rst_n(rst_n),
        
        .cs_n(cs_n),
        .baud_val(baud_val),
        .bit8(1'b1),        // 8位数据位
        .parity_en(1'b1),   // 使能奇偶校验
        .odd_n_even(1'b1),  // 奇校验
        
        .we_n(we_n),        // 写使能信号
        .data_in(data_in),  // 待发送数据
        .txrdy(txrdy),      // 发送就绪标志
        .tx(tx),            // 发送线
        
        .re_n(1'b1),        // 禁用接收功能
        .rx(1'b0),          // 接收线不使用
        .rxrdy(),           // 接收就绪标志不使用
        .data_out(),        // 接收数据不使用
        .parity_err(),      // 奇偶校验错误不使用
        .overflow()         // 溢出标志不使用
    );
    // 寄存器更新逻辑，保存txrdy的上一个状态
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n)
            txrdy_prev <= 1'b0;
        else
            txrdy_prev <= txrdy;
    end
    // ================== 遥测数据包发送状态机 ==================
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            // 复位初始化
            state <= IDLE;
            send_index <= 6'd0;
            cs_n <= 1'b0;
            we_n <= 1'b1;
            data_in <= 8'h00;
        end
        else begin
            // 状态机
            case (state)
                IDLE: begin
                    // 等待遥测请求触发
                    if (trig_telemetry) begin
                        state <= BUILD_PKG;
                    end
                    else begin
                        state <= IDLE;
                    end
                end
                
                BUILD_PKG: begin
                    // 构建遥测数据包
                    build_telemetry_package();
                    
                    // 准备发送
                    send_index <= 6'd0;
                    state <= SEND_PKG;
                end
                
                SEND_PKG: begin
                    // 发送遥测数据包
                    if (send_index < TM_PACKET_SIZE) begin
                        if (send_index == 6'd0) begin
                            if (txrdy) begin
                                // UART就绪，发送下一个字节
                                we_n <= 1'b0;
                                data_in <= tm_data[send_index];
                                send_index <= send_index + 1'b1;
                            end
                            else begin
                                // UART忙，等待就绪
                                we_n <= 1'b1;
                            end
                        end
                        else begin
                            if (txrdy_rf) begin
                                // UART就绪，发送下一个字节
                                we_n <= 1'b0;
                                data_in <= tm_data[send_index];
                                send_index <= send_index + 1'b1;
                            end
                            else begin
                                // UART忙，等待就绪
                                we_n <= 1'b1;
                            end
                        end
                    end
                    else begin
                        // 所有数据已发送
                        we_n <= 1'b1;
                        state <= WAIT_DONE;
                    end
                end
                
                WAIT_DONE: begin
                    state <= IDLE;
                end
                
                default: begin
                    state <= IDLE;
                end
            endcase
        end
    end
    
    // ================== 遥测数据包构建任务 ==================
    task build_telemetry_package;
        reg [15:0] checksum;
        integer i;
    begin
        // 1. 帧头 (2字节)
        tm_data[0] = FRAME_HEAD[15:8];
        tm_data[1] = FRAME_HEAD[7:0];
        
        // 2. 数据长度 (2字节)
        tm_data[2] = TM_DATA_LEN[15:8];
        tm_data[3] = TM_DATA_LEN[7:0];
        
        // 3. 服务类型 (1字节)
        tm_data[4] = TM_SERVICE_TYPE;
        
        // 4. 服务状态 (1字节)
        tm_data[5] = TM_SERVICE_STATUS;
        
        // 5. 通信状态计数器 (4字节)
        tm_data[6] = cmd_cnt;       // 遥控指令计数
        tm_data[7] = cmd_err_cnt;   // 指令错误计数
        tm_data[8] = tm_req_cnt;    // 遥测请求计数
        tm_data[9] = uart_rst_cnt;  // 串口复位计数
        
        // 6. 设备自检状态 (2字节)
        tm_data[10] = dev_check_status1;
        tm_data[11] = dev_check_status2;
        
        // 7. 相机状态信息 (6字节)
        tm_data[12] = cam_a_status1;
        tm_data[13] = cam_a_status2;
        tm_data[14] = cam_b_status1;
        tm_data[15] = cam_b_status2;
        tm_data[16] = cam_c_status1;
        tm_data[17] = cam_c_status2;
        
        // 8. 压缩与码率信息 (6字节)
        tm_data[18] = compress_mode;         // 压缩模式状态
        tm_data[19] = spi_data;              // SPI接口数据
        tm_data[20] = core_a_bitrate[15:8];  // 压缩核A码率高字节
        tm_data[21] = core_a_bitrate[7:0];   // 压缩核A码率低字节
        tm_data[22] = core_b_bitrate[15:8];  // 压缩核B码率高字节
        tm_data[23] = core_b_bitrate[7:0];   // 压缩核B码率低字节
        
        // 9. LVDS与FIFO状态 (2字节)
        tm_data[24] = lvds_status;
        tm_data[25] = fifo_status;
        
        // 10. 相机参数设置 (10字节)
        tm_data[26] = last_cmd;               // 指令码代号
        tm_data[27] = cam_a_exposure[15:8];   // 相机A曝光高字节
        tm_data[28] = cam_a_exposure[7:0];    // 相机A曝光低字节
        tm_data[29] = cam_a_expect_gray;      // 相机A期望灰度
        tm_data[30] = cam_a_actual_gray;      // 相机A实际灰度
        tm_data[31] = cam_b_exposure[15:8];   // 相机B曝光高字节
        tm_data[32] = cam_b_exposure[7:0];    // 相机B曝光低字节
        tm_data[33] = cam_b_expect_gray;      // 相机B期望灰度
        tm_data[34] = cam_b_actual_gray;      // 相机B实际灰度
        tm_data[35] = cam_c_exposure[15:8];   // 相机C曝光高字节
        tm_data[36] = cam_c_exposure[7:0];    // 相机C曝光低字节
        tm_data[37] = cam_c_expect_gray;      // 相机C期望灰度
        tm_data[38] = cam_c_actual_gray;      // 相机C实际灰度
        
        // 11. 时间信息 (5字节)
        tm_data[39] = 8'h00;                  // 预留字节
        tm_data[40] = time_code[31:24];       // 时间码字节1(最高位)
        tm_data[41] = time_code[23:16];       // 时间码字节2
        tm_data[42] = time_code[15:8];        // 时间码字节3
        tm_data[43] = time_code[7:0];         // 时间码字节4(最低位)
        
        // 12. 计算校验和 (前42字节的累加和)
        checksum = 16'h0000;
        for ( i = 2; i <= 43; i = i + 1) begin
            checksum = checksum + tm_data[i];
        end
        
        // 13. 校验和 (2字节)
        tm_data[44] = checksum[15:8];   // 校验和高字节
        tm_data[45] = checksum[7:0];    // 校验和低字节
        
        // 14. 帧尾 (2字节)
        tm_data[46] = FRAME_TAIL[15:8];
        tm_data[47] = FRAME_TAIL[7:0];
    end
    endtask

endmodule 