/**
 * 卫星监视相机顶层模块
 * 功能：综合管理监视相机的命令接收、状态维护和遥测发送
 * 协议：RS422异步串行通信(115200bps，奇校验)
 */
module camera_top (
    input wire clk,                // 系统时钟,50MHz
    input wire rst_n,              // 系统复位,低电平有效
    input wire [7:0] baud_val,     // 波特率配置
    input wire rs422_rx,           // RS422接收引脚
    output wire rs422_tx           // RS422发送引脚
);

    // ================== 模块间连接信号 ==================
    // 命令处理模块到共享状态模块的连接
    wire inc_cmd_cnt;              // 增加遥控指令计数
    wire inc_cmd_err_cnt;          // 增加指令错误计数
    wire inc_tm_req_cnt;           // 增加遥测请求计数
    wire inc_uart_rst_cnt;         // 增加串口复位计数
    
    // 相机控制指令信号
    wire update_cam_a_power;       // 更新相机A加电状态
    wire update_cam_b_power;       // 更新相机B加电状态
    wire update_cam_c_power;       // 更新相机C加电状态
    wire [1:0] cam_a_power_val;    // 相机A加电状态值
    wire [1:0] cam_b_power_val;    // 相机B加电状态值
    wire [1:0] cam_c_power_val;    // 相机C加电状态值
    
    // 压缩核对接关系更新
    wire update_core_connection;   // 更新压缩核对接关系
    wire [1:0] core_a_connect;     // 压缩核A对接哪个相机
    wire [1:0] core_b_connect;     // 压缩核B对接哪个相机
    
    // LVDS状态更新
    wire update_lvds_status;       // 更新LVDS状态
    wire [7:0] lvds_status_val;    // LVDS状态值
    
    // 相机灰度更新
    wire update_cam_a_expect_gray; // 更新相机A期望灰度
    wire update_cam_b_expect_gray; // 更新相机B期望灰度
    wire update_cam_c_expect_gray; // 更新相机C期望灰度
    wire [7:0] cam_a_expect_gray;  // 相机A期望灰度值
    wire [7:0] cam_b_expect_gray;  // 相机B期望灰度值
    wire [7:0] cam_c_expect_gray;  // 相机C期望灰度值
    
    // 时间码更新
    wire update_time_code;         // 更新时间码
    wire [31:0] new_time_code;     // 新时间码值
    
    // 最近指令码更新
    wire update_last_cmd;          // 更新最近指令码
    wire [7:0] last_cmd_val;       // 最近指令码值
    
    // 遥测请求触发
    wire tm_req_received;          // 收到遥测请求信号
    wire trig_telemetry;           // 触发遥测发送
    
    // 状态读取接口 - 通信计数器
    wire [7:0] cmd_cnt;            // 遥控指令计数器
    wire [7:0] cmd_err_cnt;        // 指令错误计数器
    wire [7:0] tm_req_cnt;         // 遥测请求计数器
    wire [7:0] uart_rst_cnt;       // 串口复位计数器
    
    // 状态读取接口 - 设备自检状态
    wire [7:0] dev_check_status1;  // 设备自检状态1
    wire [7:0] dev_check_status2;  // 设备自检状态2
    
    // 状态读取接口 - 相机状态
    wire [7:0] cam_a_status1;      // 相机A状态1
    wire [7:0] cam_a_status2;      // 相机A状态2
    wire [7:0] cam_b_status1;      // 相机B状态1
    wire [7:0] cam_b_status2;      // 相机B状态2
    wire [7:0] cam_c_status1;      // 相机C状态1
    wire [7:0] cam_c_status2;      // 相机C状态2
    
    // 状态读取接口 - 压缩与码率
    wire [7:0] compress_mode;      // 压缩模式状态
    wire [7:0] spi_data;           // SPI接口数据
    wire [15:0] core_a_bitrate;    // 压缩核A码率
    wire [15:0] core_b_bitrate;    // 压缩核B码率
    
    // 状态读取接口 - LVDS与FIFO
    wire [7:0] lvds_status;        // LVDS输出状态
    wire [7:0] fifo_status;        // FIFO空满状态
    
    // 状态读取接口 - 相机参数
    wire [7:0] last_cmd;           // 最近指令码
    wire [15:0] cam_a_exposure;    // 相机A曝光
    wire [7:0] cam_a_expect_gray_val; // 相机A期望灰度
    wire [7:0] cam_a_actual_gray;  // 相机A实际灰度
    wire [15:0] cam_b_exposure;    // 相机B曝光
    wire [7:0] cam_b_expect_gray_val; // 相机B期望灰度
    wire [7:0] cam_b_actual_gray;  // 相机B实际灰度
    wire [15:0] cam_c_exposure;    // 相机C曝光
    wire [7:0] cam_c_expect_gray_val; // 相机C期望灰度
    wire [7:0] cam_c_actual_gray;  // 相机C实际灰度
    
    // 状态读取接口 - 时间信息
    wire [31:0] time_code;         // 时间码
    
    // ================== 子模块实例化 ==================
    // 命令处理模块
    camera_cmd_channel cmd_channel_inst (
        .clk(clk),
        .rst_n(rst_n),
        .baud_val(baud_val),
        .rs422_rx(rs422_rx),
        
        // 共享状态接口 - 输出信号
        .inc_cmd_cnt(inc_cmd_cnt),
        .inc_cmd_err_cnt(inc_cmd_err_cnt),
        .inc_tm_req_cnt(inc_tm_req_cnt),
        .inc_uart_rst_cnt(inc_uart_rst_cnt),
        
        // 相机控制指令信号
        .update_cam_a_power(update_cam_a_power),
        .update_cam_b_power(update_cam_b_power),
        .update_cam_c_power(update_cam_c_power),
        .cam_a_power_val(cam_a_power_val),
        .cam_b_power_val(cam_b_power_val),
        .cam_c_power_val(cam_c_power_val),
        
        // 压缩核对接关系更新
        .update_core_connection(update_core_connection),
        .core_a_connect(core_a_connect),
        .core_b_connect(core_b_connect),
        
        // LVDS状态更新
        .update_lvds_status(update_lvds_status),
        .lvds_status_val(lvds_status_val),
        
        // 相机灰度更新
        .update_cam_a_expect_gray(update_cam_a_expect_gray),
        .update_cam_b_expect_gray(update_cam_b_expect_gray),
        .update_cam_c_expect_gray(update_cam_c_expect_gray),
        .cam_a_expect_gray(cam_a_expect_gray),
        .cam_b_expect_gray(cam_b_expect_gray),
        .cam_c_expect_gray(cam_c_expect_gray),
        
        // 时间码更新
        .update_time_code(update_time_code),
        .new_time_code(new_time_code),
        
        // 最近指令码更新
        .update_last_cmd(update_last_cmd),
        .last_cmd_val(last_cmd_val),
        
        // 遥测请求触发
        .tm_req_received(tm_req_received),
        .trig_telemetry(trig_telemetry)
    );
    
    // 共享状态模块
    camera_shared_state shared_state_inst (
        .clk(clk),
        .rst_n(rst_n),
        
        // 通信计数器更新接口
        .inc_cmd_cnt(inc_cmd_cnt),
        .inc_cmd_err_cnt(inc_cmd_err_cnt),
        .inc_tm_req_cnt(inc_tm_req_cnt),
        .inc_uart_rst_cnt(inc_uart_rst_cnt),
        
        // 相机状态更新接口
        .update_cam_a_power(update_cam_a_power),
        .update_cam_b_power(update_cam_b_power),
        .update_cam_c_power(update_cam_c_power),
        .cam_a_power_val(cam_a_power_val),
        .cam_b_power_val(cam_b_power_val),
        .cam_c_power_val(cam_c_power_val),
        
        // 压缩核对接关系更新
        .update_core_connection(update_core_connection),
        .core_a_connect(core_a_connect),
        .core_b_connect(core_b_connect),
        
        // LVDS状态更新
        .update_lvds_status(update_lvds_status),
        .lvds_status_val(lvds_status_val),
        
        // 相机期望灰度更新
        .update_cam_a_expect_gray(update_cam_a_expect_gray),
        .update_cam_b_expect_gray(update_cam_b_expect_gray),
        .update_cam_c_expect_gray(update_cam_c_expect_gray),
        .cam_a_expect_gray(cam_a_expect_gray),
        .cam_b_expect_gray(cam_b_expect_gray),
        .cam_c_expect_gray(cam_c_expect_gray),
        
        // 时间码更新
        .update_time_code(update_time_code),
        .new_time_code(new_time_code),

        // 最近指令码更新
        .update_last_cmd(update_last_cmd),
        .last_cmd_val(last_cmd_val),
        
        // 通信超时检测
        .tm_req_received(tm_req_received),

        // 状态读取接口 - 通信计数器
        .cmd_cnt(cmd_cnt),
        .cmd_err_cnt(cmd_err_cnt),
        .tm_req_cnt(tm_req_cnt),
        .uart_rst_cnt(uart_rst_cnt),
        
        // 状态读取接口 - 设备自检状态
        .dev_check_status1(dev_check_status1),
        .dev_check_status2(dev_check_status2),
        
        // 状态读取接口 - 相机状态
        .cam_a_status1(cam_a_status1),
        .cam_a_status2(cam_a_status2),
        .cam_b_status1(cam_b_status1),
        .cam_b_status2(cam_b_status2),
        .cam_c_status1(cam_c_status1),
        .cam_c_status2(cam_c_status2),
        
        // 状态读取接口 - 压缩与码率
        .compress_mode(compress_mode),
        .spi_data(spi_data),
        .core_a_bitrate(core_a_bitrate),
        .core_b_bitrate(core_b_bitrate),
        
        // 状态读取接口 - LVDS与FIFO
        .lvds_status(lvds_status),
        .fifo_status(fifo_status),
        
        // 状态读取接口 - 相机参数
        .last_cmd(last_cmd),
        .cam_a_exposure(cam_a_exposure),
        .cam_a_expect_gray_val(cam_a_expect_gray_val),
        .cam_a_actual_gray(cam_a_actual_gray),
        .cam_b_exposure(cam_b_exposure),
        .cam_b_expect_gray_val(cam_b_expect_gray_val),
        .cam_b_actual_gray(cam_b_actual_gray),
        .cam_c_exposure(cam_c_exposure),
        .cam_c_expect_gray_val(cam_c_expect_gray_val),
        .cam_c_actual_gray(cam_c_actual_gray),
        
        // 状态读取接口 - 时间信息
        .time_code(time_code)
    );
    
    // 遥测发送模块
    camera_telemetry_channel telemetry_channel_inst (
        .clk(clk),
        .rst_n(rst_n),
        .baud_val(baud_val),
        .trig_telemetry(trig_telemetry),
        
        // 状态读取接口 - 通信计数器
        .cmd_cnt(cmd_cnt),
        .cmd_err_cnt(cmd_err_cnt),
        .tm_req_cnt(tm_req_cnt),
        .uart_rst_cnt(uart_rst_cnt),
        
        // 状态读取接口 - 设备自检状态
        .dev_check_status1(dev_check_status1),
        .dev_check_status2(dev_check_status2),
        
        // 状态读取接口 - 相机状态
        .cam_a_status1(cam_a_status1),
        .cam_a_status2(cam_a_status2),
        .cam_b_status1(cam_b_status1),
        .cam_b_status2(cam_b_status2),
        .cam_c_status1(cam_c_status1),
        .cam_c_status2(cam_c_status2),
        
        // 状态读取接口 - 压缩与码率
        .compress_mode(compress_mode),
        .spi_data(spi_data),
        .core_a_bitrate(core_a_bitrate),
        .core_b_bitrate(core_b_bitrate),
        
        // 状态读取接口 - LVDS与FIFO
        .lvds_status(lvds_status),
        .fifo_status(fifo_status),
        
        // 状态读取接口 - 相机参数
        .last_cmd(last_cmd),
        .cam_a_exposure(cam_a_exposure),
        .cam_a_expect_gray(cam_a_expect_gray_val),
        .cam_a_actual_gray(cam_a_actual_gray),
        .cam_b_exposure(cam_b_exposure),
        .cam_b_expect_gray(cam_b_expect_gray_val),
        .cam_b_actual_gray(cam_b_actual_gray),
        .cam_c_exposure(cam_c_exposure),
        .cam_c_expect_gray(cam_c_expect_gray_val),
        .cam_c_actual_gray(cam_c_actual_gray),
        
        // 状态读取接口 - 时间信息
        .time_code(time_code),
        
        // UART输出
        .tx(rs422_tx)
    );

endmodule 