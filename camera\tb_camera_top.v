`timescale 1ns/1ps

module tb_camera_top();

// 系统信号
reg clk;
reg rst_n;

// 通信接口信号
wire tx;         // 相机遥测数据输出通道
reg  rx;         // 命令输入通道

// 接收数据缓存和计数
reg [7:0] tlm_data[0:47];  // 遥测数据缓存（固定48字节）
integer tlm_count = 0;      // 遥测数据计数
integer i, j;               // 循环计数

// 命令数组
reg [7:0] reset_cmd[0:15];          // 复位指令
reg [7:0] telemetry_req_cmd[0:15];  // 遥测请求指令
reg [7:0] ctrl_cmd1[0:15];          // 遥控指令1 - 相机加电通道选择
reg [7:0] ctrl_cmd2[0:15];          // 遥控指令2 - 授时指令
reg [7:0] ctrl_cmd3[0:15];          // 遥控指令3 - LVDS图像下传开
reg [7:0] ctrl_cmd4[0:15];          // 遥控指令4 - 相机自检
reg [7:0] ctrl_cmd5[0:15];          // 遥控指令5 - 期望灰度
reg [15:0] checksum;                // 校验和计算

// 波特率设置
parameter BAUD_RATE = 115200;       // 波特率
parameter BIT_TIME = 1_000_000_000/BAUD_RATE; // 位时间（纳秒）

// 数据包大小
parameter TLM_PACKET_SIZE = 48;    // 遥测数据包大小（固定48字节）

// 接收字节缓存
reg [7:0] rx_byte;

// 实例化相机顶层模块
camera_top camera_top_inst(
    .clk(clk),                  // 系统时钟 (50MHz)
    .rst_n(rst_n),              // 复位信号，低电平有效
    .baud_val(8'd21),           // 波特率配置 (115200bps)
    .rs422_rx(rx),              // 命令接收线
    .rs422_tx(tx)               // 遥测数据发送线
);

// 时钟生成
initial clk = 0;
always #12.5 clk = ~clk;  // 50MHz时钟

// 初始化命令
initial begin
    // 初始化复位指令
    // 帧头: 0xEB90
    reset_cmd[0] = 8'hEB;
    reset_cmd[1] = 8'h90;
    // 数据长度: 0x0008 (8字节有效数据)
    reset_cmd[2] = 8'h00;
    reset_cmd[3] = 8'h08;
    // 服务类型: 0x03 (设备复位)
    reset_cmd[4] = 8'h03;
    // 服务状态: 0x00
    reset_cmd[5] = 8'h00;
    // 空数据: 6字节
    reset_cmd[6] = 8'h00;
    reset_cmd[7] = 8'h00;
    reset_cmd[8] = 8'h00;
    reset_cmd[9] = 8'h00;
    reset_cmd[10] = 8'h00;
    reset_cmd[11] = 8'h00;
    
    // 计算复位指令校验和
    checksum = 0;
    for (i = 2; i <= 11; i = i + 1) begin
        checksum = checksum + reset_cmd[i];
    end
    reset_cmd[12] = checksum[15:8];
    reset_cmd[13] = checksum[7:0];
    $display("Reset command checksum: 0x%h", checksum);
    
    // 帧尾: 0x09D7
    reset_cmd[14] = 8'h09;
    reset_cmd[15] = 8'hD7;
    
    // 初始化遥测请求指令
    // 帧头: 0xEB90
    telemetry_req_cmd[0] = 8'hEB;
    telemetry_req_cmd[1] = 8'h90;
    // 数据长度: 0x0008 (8字节有效数据)
    telemetry_req_cmd[2] = 8'h00;
    telemetry_req_cmd[3] = 8'h08;
    // 服务类型: 0x01 (遥测请求)
    telemetry_req_cmd[4] = 8'h01;
    // 服务状态: 0x00
    telemetry_req_cmd[5] = 8'h00;
    // 空数据: 6字节
    telemetry_req_cmd[6] = 8'h00;
    telemetry_req_cmd[7] = 8'h00;
    telemetry_req_cmd[8] = 8'h00;
    telemetry_req_cmd[9] = 8'h00;
    telemetry_req_cmd[10] = 8'h00;
    telemetry_req_cmd[11] = 8'h00;
    
    // 计算遥测请求指令校验和
    checksum = 0;
    for (i = 2; i <= 11; i = i + 1) begin
        checksum = checksum + telemetry_req_cmd[i];
    end
    telemetry_req_cmd[12] = checksum[15:8];
    telemetry_req_cmd[13] = checksum[7:0];
    $display("Telemetry request command checksum: 0x%h", checksum);
    
    // 帧尾: 0x09D7
    telemetry_req_cmd[14] = 8'h09;
    telemetry_req_cmd[15] = 8'hD7;
    
    // 初始化遥控指令1 - 相机加电通道选择 (CMD=0x01)
    // 帧头: 0xEB90
    ctrl_cmd1[0] = 8'hEB;
    ctrl_cmd1[1] = 8'h90;
    // 数据长度: 0x0008 (8字节有效数据)
    ctrl_cmd1[2] = 8'h00;
    ctrl_cmd1[3] = 8'h08;
    // 服务类型: 0x08 (遥控指令)
    ctrl_cmd1[4] = 8'h08;
    // 服务状态: 0x00
    ctrl_cmd1[5] = 8'h00;
    // 命令字段: CMD=0x01 (相机加电通道选择)
    ctrl_cmd1[6] = 8'h01;  // CMD
    ctrl_cmd1[7] = 8'h11;  // W1: 第1路相机对接压缩核A
    ctrl_cmd1[8] = 8'h22;  // W2: 第2路相机对接压缩核B
    ctrl_cmd1[9] = 8'h00;  // W3: 空
    ctrl_cmd1[10] = 8'h00; // W4: 空
    ctrl_cmd1[11] = 8'h00; // W5: 空
    
    // 计算遥控指令1校验和
    checksum = 0;
    for (i = 2; i <= 11; i = i + 1) begin
        checksum = checksum + ctrl_cmd1[i];
    end
    ctrl_cmd1[12] = checksum[15:8];
    ctrl_cmd1[13] = checksum[7:0];
    $display("Control command 1 checksum: 0x%h", checksum);
    
    // 帧尾: 0x09D7
    ctrl_cmd1[14] = 8'h09;
    ctrl_cmd1[15] = 8'hD7;
    
    // 初始化遥控指令2 - 授时指令 (CMD=0x02)
    // 帧头: 0xEB90
    ctrl_cmd2[0] = 8'hEB;
    ctrl_cmd2[1] = 8'h90;
    // 数据长度: 0x0008 (8字节有效数据)
    ctrl_cmd2[2] = 8'h00;
    ctrl_cmd2[3] = 8'h08;
    // 服务类型: 0x08 (遥控指令)
    ctrl_cmd2[4] = 8'h08;
    // 服务状态: 0x00
    ctrl_cmd2[5] = 8'h00;
    // 命令字段: CMD=0x02 (授时指令)
    ctrl_cmd2[6] = 8'h02;  // CMD
    ctrl_cmd2[7] = 8'h12;  // W1: 秒值4最高位
    ctrl_cmd2[8] = 8'h34;  // W2: 秒值3
    ctrl_cmd2[9] = 8'h56;  // W3: 秒值2
    ctrl_cmd2[10] = 8'h78; // W4: 秒值1最低位
    ctrl_cmd2[11] = 8'h00; // W5: 空
    
    // 计算遥控指令2校验和
    checksum = 0;
    for (i = 2; i <= 11; i = i + 1) begin
        checksum = checksum + ctrl_cmd2[i];
    end
    ctrl_cmd2[12] = checksum[15:8];
    ctrl_cmd2[13] = checksum[7:0];
    $display("Control command 2 checksum: 0x%h", checksum);
    
    // 帧尾: 0x09D7
    ctrl_cmd2[14] = 8'h09;
    ctrl_cmd2[15] = 8'hD7;
    
    // 初始化遥控指令3 - LVDS图像下传开 (CMD=0x03)
    // 帧头: 0xEB90
    ctrl_cmd3[0] = 8'hEB;
    ctrl_cmd3[1] = 8'h90;
    // 数据长度: 0x0008 (8字节有效数据)
    ctrl_cmd3[2] = 8'h00;
    ctrl_cmd3[3] = 8'h08;
    // 服务类型: 0x08 (遥控指令)
    ctrl_cmd3[4] = 8'h08;
    // 服务状态: 0x00
    ctrl_cmd3[5] = 8'h00;
    // 命令字段: CMD=0x03 (LVDS图像下传开)
    ctrl_cmd3[6] = 8'h03;  // CMD
    ctrl_cmd3[7] = 8'h00;  // W1: 空
    ctrl_cmd3[8] = 8'h00;  // W2: 空
    ctrl_cmd3[9] = 8'h00;  // W3: 空
    ctrl_cmd3[10] = 8'h00; // W4: 空
    ctrl_cmd3[11] = 8'h00; // W5: 空
    
    // 计算遥控指令3校验和
    checksum = 0;
    for (i = 2; i <= 11; i = i + 1) begin
        checksum = checksum + ctrl_cmd3[i];
    end
    ctrl_cmd3[12] = checksum[15:8];
    ctrl_cmd3[13] = checksum[7:0];
    $display("Control command 3 checksum: 0x%h", checksum);
    
    // 帧尾: 0x09D7
    ctrl_cmd3[14] = 8'h09;
    ctrl_cmd3[15] = 8'hD7;
    
    // 初始化遥控指令4 - 相机自检 (CMD=0x11)
    // 帧头: 0xEB90
    ctrl_cmd4[0] = 8'hEB;
    ctrl_cmd4[1] = 8'h90;
    // 数据长度: 0x0008 (8字节有效数据)
    ctrl_cmd4[2] = 8'h00;
    ctrl_cmd4[3] = 8'h08;
    // 服务类型: 0x08 (遥控指令)
    ctrl_cmd4[4] = 8'h08;
    // 服务状态: 0x00
    ctrl_cmd4[5] = 8'h00;
    // 命令字段: CMD=0x11 (相机自检)
    ctrl_cmd4[6] = 8'h11;  // CMD
    ctrl_cmd4[7] = 8'h11;  // W1: 相机A自检
    ctrl_cmd4[8] = 8'h01;  // W2: 自检操作
    ctrl_cmd4[9] = 8'h00;  // W3: 空
    ctrl_cmd4[10] = 8'h00; // W4: 空
    ctrl_cmd4[11] = 8'h00; // W5: 空
    
    // 计算遥控指令4校验和
    checksum = 0;
    for (i = 2; i <= 11; i = i + 1) begin
        checksum = checksum + ctrl_cmd4[i];
    end
    ctrl_cmd4[12] = checksum[15:8];
    ctrl_cmd4[13] = checksum[7:0];
    $display("Control command 4 checksum: 0x%h", checksum);
    
    // 帧尾: 0x09D7
    ctrl_cmd4[14] = 8'h09;
    ctrl_cmd4[15] = 8'hD7;
    
    // 初始化遥控指令5 - 期望灰度 (CMD=0x12)
    // 帧头: 0xEB90
    ctrl_cmd5[0] = 8'hEB;
    ctrl_cmd5[1] = 8'h90;
    // 数据长度: 0x0008 (8字节有效数据)
    ctrl_cmd5[2] = 8'h00;
    ctrl_cmd5[3] = 8'h08;
    // 服务类型: 0x08 (遥控指令)
    ctrl_cmd5[4] = 8'h08;
    // 服务状态: 0x00
    ctrl_cmd5[5] = 8'h00;
    // 命令字段: CMD=0x12 (期望灰度)
    ctrl_cmd5[6] = 8'h12;  // CMD
    ctrl_cmd5[7] = 8'h22;  // W1: 相机B
    ctrl_cmd5[8] = 8'h60;  // W2: 期望灰度值
    ctrl_cmd5[9] = 8'h00;  // W3: 空
    ctrl_cmd5[10] = 8'h00; // W4: 空
    ctrl_cmd5[11] = 8'h00; // W5: 空
    
    // 计算遥控指令5校验和
    checksum = 0;
    for (i = 2; i <= 11; i = i + 1) begin
        checksum = checksum + ctrl_cmd5[i];
    end
    ctrl_cmd5[12] = checksum[15:8];
    ctrl_cmd5[13] = checksum[7:0];
    $display("Control command 5 checksum: 0x%h", checksum);
    
    // 帧尾: 0x09D7
    ctrl_cmd5[14] = 8'h09;
    ctrl_cmd5[15] = 8'hD7;
    
    // 初始化命令接收线为高电平（空闲状态）
    rx = 1;
end

// 主测试流程
initial begin
    // 系统复位
    rst_n = 0;
    #100 rst_n = 1;
    
    // 启动监测任务
    fork
        tlm_monitor;       // 遥测数据监测任务
        test_sequence;     // 测试序列任务
    join
end

// 测试序列任务
task test_sequence;
    begin
        // 等待系统初始化
        #500000;
        
        // 1. 发送复位指令
        $display("Time %0t: Sending reset command...", $time);
        // 直接发送复位指令的每个字节
        for (j = 0; j < 16; j = j + 1) begin
            send_byte(reset_cmd[j]);
        end
        $display("Time %0t: Reset command sent", $time);
        
        // 等待复位完成
        #200000;
        
        // 2. 发送遥测请求指令
        $display("Time %0t: Sending telemetry request command...", $time);
        // 直接发送遥测请求指令的每个字节
        for (j = 0; j < 16; j = j + 1) begin
            send_byte(telemetry_req_cmd[j]);
        end
        $display("Time %0t: Telemetry request command sent", $time);
        
        // 等待遥测数据响应
        $display("Time %0t: Waiting for telemetry data response...", $time);
        wait(tlm_count == TLM_PACKET_SIZE);
        tlm_count = 0;
        
        // 3. 发送遥控指令1并请求遥测
        #200000;
        $display("Time %0t: Sending control command 1 (Camera Power Selection)...", $time);
        for (j = 0; j < 16; j = j + 1) begin
            send_byte(ctrl_cmd1[j]);
        end
        $display("Time %0t: Control command 1 sent", $time);
        
        #50000;
        $display("Time %0t: Sending telemetry request after control command 1...", $time);
        for (j = 0; j < 16; j = j + 1) begin
            send_byte(telemetry_req_cmd[j]);
        end
        $display("Time %0t: Telemetry request command sent", $time);
        wait(tlm_count == TLM_PACKET_SIZE);
        tlm_count = 0;
        
        // 4. 发送遥控指令2并请求遥测
        #200000;
        $display("Time %0t: Sending control command 2 (Time Injection)...", $time);
        for (j = 0; j < 16; j = j + 1) begin
            send_byte(ctrl_cmd2[j]);
        end
        $display("Time %0t: Control command 2 sent", $time);
        
        #50000;
        $display("Time %0t: Sending telemetry request after control command 2...", $time);
        for (j = 0; j < 16; j = j + 1) begin
            send_byte(telemetry_req_cmd[j]);
        end
        $display("Time %0t: Telemetry request command sent", $time);
        wait(tlm_count == TLM_PACKET_SIZE);
        tlm_count = 0;
        
        // 5. 发送遥控指令3并请求遥测
        #200000;
        $display("Time %0t: Sending control command 3 (LVDS Image Transmission On)...", $time);
        for (j = 0; j < 16; j = j + 1) begin
            send_byte(ctrl_cmd3[j]);
        end
        $display("Time %0t: Control command 3 sent", $time);
        
        #50000;
        $display("Time %0t: Sending telemetry request after control command 3...", $time);
        for (j = 0; j < 16; j = j + 1) begin
            send_byte(telemetry_req_cmd[j]);
        end
        $display("Time %0t: Telemetry request command sent", $time);
        wait(tlm_count == TLM_PACKET_SIZE);
        tlm_count = 0;
        
        // 6. 发送遥控指令4并请求遥测
        #200000;
        $display("Time %0t: Sending control command 4 (Camera Self-Check)...", $time);
        for (j = 0; j < 16; j = j + 1) begin
            send_byte(ctrl_cmd4[j]);
        end
        $display("Time %0t: Control command 4 sent", $time);
        
        #50000;
        $display("Time %0t: Sending telemetry request after control command 4...", $time);
        for (j = 0; j < 16; j = j + 1) begin
            send_byte(telemetry_req_cmd[j]);
        end
        $display("Time %0t: Telemetry request command sent", $time);
        wait(tlm_count == TLM_PACKET_SIZE);
        tlm_count = 0;
        
        // 7. 发送遥控指令5并请求遥测
        #200000;
        $display("Time %0t: Sending control command 5 (Target Brightness)...", $time);
        for (j = 0; j < 16; j = j + 1) begin
            send_byte(ctrl_cmd5[j]);
        end
        $display("Time %0t: Control command 5 sent", $time);
        
        #50000;
        $display("Time %0t: Sending telemetry request after control command 5...", $time);
        for (j = 0; j < 16; j = j + 1) begin
            send_byte(telemetry_req_cmd[j]);
        end
        $display("Time %0t: Telemetry request command sent", $time);
        wait(tlm_count == TLM_PACKET_SIZE);
        tlm_count = 0;
        
        // 测试完成
        $display("Time %0t: Test sequence completed", $time);
        #100000 $finish;
    end
endtask

// 遥测数据监测任务
task tlm_monitor;
    forever begin
        // 等待起始位(低电平)
        @(negedge tx);
        
        // 采样点定位在起始位中间
        #(BIT_TIME/2);
        
        // 确认是起始位
        if (tx == 0) begin
            rx_byte = 0;
            
            // 读取8个数据位(LSB先传)
            for (i = 0; i < 8; i = i + 1) begin
                #BIT_TIME;
                rx_byte[i] = tx;
            end
            
            // 读取奇偶校验位
            #BIT_TIME;
            // 这里可以添加奇偶校验检查代码
            
            // 读取停止位
            #BIT_TIME;
            
            // 保存接收到的字节
            tlm_data[tlm_count] = rx_byte;
            tlm_count = tlm_count + 1;
            
            // 显示接收到的字节
            $display("Time %0t: Received telemetry byte: 0x%h", $time, rx_byte);
            
            // 检查是否接收到完整的遥测数据包
            if (tlm_count == TLM_PACKET_SIZE) begin
                display_tlm_packet();
            end
        end
    end
endtask

// 基本字节发送任务
task send_byte;
    input [7:0] byte_data;
    reg parity;  // 奇校验位
    begin
        // 计算奇校验位
        parity = ~(^byte_data);  // 异或求奇校验
        
        // 起始位
        rx = 0;
        #BIT_TIME;
        
        // 8个数据位 (LSB先传)
        for (i = 0; i < 8; i = i + 1) begin
            rx = (byte_data >> i) & 1'b1;
            #BIT_TIME;
        end
        
        // 奇校验位
        rx = parity;
        #BIT_TIME;
        
        // 停止位
        rx = 1;
        #BIT_TIME;
        
        // 字节间小延时
        #(BIT_TIME/2);
    end
endtask

// 显示遥测数据包
task display_tlm_packet;
    begin
        $display("====== Camera Telemetry Packet ======");
        $display("Frame Header: 0x%h%h", tlm_data[0], tlm_data[1]);
        $display("Data Length: 0x%h%h", tlm_data[2], tlm_data[3]);
        $display("Service Type: 0x%h", tlm_data[4]);
        $display("Service Status: 0x%h", tlm_data[5]);
        
        // 通信状态计数器
        $display("Command Counter: 0x%h", tlm_data[6]);
        $display("Command Error Counter: 0x%h", tlm_data[7]);
        $display("Telemetry Request Counter: 0x%h", tlm_data[8]);
        $display("Serial Reset Counter: 0x%h", tlm_data[9]);
        
        // 设备自检状态
        $display("Device Self-Check Status 1: 0x%h", tlm_data[10]);
        $display("Device Self-Check Status 2: 0x%h", tlm_data[11]);
        
        // 相机状态信息
        $display("Camera A Status 1: 0x%h", tlm_data[12]);
        $display("Camera A Status 2: 0x%h", tlm_data[13]);
        $display("Camera B Status 1: 0x%h", tlm_data[14]);
        $display("Camera B Status 2: 0x%h", tlm_data[15]);
        $display("Camera C Status 1: 0x%h", tlm_data[16]);
        $display("Camera C Status 2: 0x%h", tlm_data[17]);
        
        // 压缩与码率信息
        $display("Compression Mode: 0x%h", tlm_data[18]);
        $display("SPI Interface Data: 0x%h", tlm_data[19]);
        $display("Compression Core A Bitrate: 0x%h%h", tlm_data[20], tlm_data[21]);
        $display("Compression Core B Bitrate: 0x%h%h", tlm_data[22], tlm_data[23]);
        
        // LVDS与FIFO状态
        $display("LVDS Output: 0x%h", tlm_data[24]);
        $display("FIFO Status: 0x%h", tlm_data[25]);
        
        // 相机参数设置
        $display("Command Code: 0x%h", tlm_data[26]);
        
        // 相机A参数
        $display("Camera A Exposure: 0x%h%h", tlm_data[27], tlm_data[28]);
        $display("Camera A Target Brightness: 0x%h", tlm_data[29]);
        $display("Camera A Actual Brightness: 0x%h", tlm_data[30]);
        
        // 相机B参数
        $display("Camera B Exposure: 0x%h%h", tlm_data[31], tlm_data[32]);
        $display("Camera B Target Brightness: 0x%h", tlm_data[33]);
        $display("Camera B Actual Brightness: 0x%h", tlm_data[34]);
        
        // 相机C参数
        $display("Camera C Exposure: 0x%h%h", tlm_data[35], tlm_data[36]);
        $display("Camera C Target Brightness: 0x%h", tlm_data[37]);
        $display("Camera C Actual Brightness: 0x%h", tlm_data[38]);
        
        // 预留字节
        $display("Reserved Byte: 0x%h", tlm_data[39]);
        
        // 时间码
        $display("Time Code: 0x%h%h%h%h", 
                tlm_data[40], tlm_data[41], tlm_data[42], tlm_data[43]);
        
        // 校验和和帧尾
        $display("Checksum: 0x%h%h", tlm_data[44], tlm_data[45]);
        $display("Frame Footer: 0x%h%h", tlm_data[46], tlm_data[47]);
        
        $display("Response Time: %0t ns", $time);
        $display("==================================");
        
        // 解析相机状态位
        decode_camera_status();
    end
endtask

// 解析详细的相机状态位
task decode_camera_status;
    begin
        // 设备自检状态1解析
        $display("\n-- Detailed Device Self-Check Status 1 (0x%h) --", tlm_data[10]);
        $display("Internal Reset Complete: %b", (tlm_data[10] >> 7) & 1);
        $display("SDRAM Initialization: %b", (tlm_data[10] >> 6) & 1);
        $display("Compression Core A Initialization: %b", (tlm_data[10] >> 5) & 1);
        $display("Compression Core B Initialization: %b", (tlm_data[10] >> 4) & 1);
        $display("Reserved: %b", (tlm_data[10] >> 3) & 1);
        $display("Camera C SDI Initialization: %b", (tlm_data[10] >> 2) & 1);
        $display("Camera B SDI Initialization: %b", (tlm_data[10] >> 1) & 1);
        $display("Camera A SDI Initialization: %b", tlm_data[10] & 1);
        
        // 设备自检状态2解析
        $display("\n-- Detailed Device Self-Check Status 2 (0x%h) --", tlm_data[11]);
        $display("Compression Core A Data Status: %b", (tlm_data[11] >> 7) & 1);
        $display("Compression Core B Data Status: %b", (tlm_data[11] >> 6) & 1);
        $display("Reserved: %b", (tlm_data[11] >> 4) & 3);
        $display("Compression Core A Connected Camera: %b (%s)", 
                 (tlm_data[11] >> 2) & 3, 
                 get_camera_name((tlm_data[11] >> 2) & 3));
        $display("Compression Core B Connected Camera: %b (%s)", 
                 tlm_data[11] & 3,
                 get_camera_name(tlm_data[11] & 3));
    end
endtask

// 辅助函数 - 获取相机名称
function [23:0] get_camera_name;
    input [1:0] code;
    begin
        case(code)
            2'b00: get_camera_name = "A";
            2'b01: get_camera_name = "B";
            2'b10: get_camera_name = "C";
            2'b11: get_camera_name = "Invalid";
        endcase
    end
endfunction

endmodule 