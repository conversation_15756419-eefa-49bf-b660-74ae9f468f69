module envmonitor_shared_state (
    input wire clk,                
    input wire rst_n,             
    
    // 系统时间输入
    input wire [31:0] sys_time_s,  // 系统时间(秒)
    input wire [15:0] sys_time_ms, // 系统时间(毫秒)
    
    // 命令通道状态更新输入
    input wire update_utc,               // 更新UTC时间标志
    input wire [31:0] new_utc_second,    // 新的UTC秒值
    input wire [15:0] new_utc_msecond,   // 新的UTC毫秒值
    input wire inc_time_code,            // 增加时间码注入计数
    input wire inc_data_inject,          // 增加数据注入计数
    input wire inc_cmd_error,            // 增加指令错误计数
    
    // 状态输出
    output reg [31:0] utc_second,        // UTC秒计数
    output reg [15:0] utc_msecond,       // UTC毫秒值
    
    // 计数器输出
    output reg [7:0] time_code_cnt,      // 时间码注入正确计数
    output reg [7:0] data_inject_cnt,    // 数据注入指令正确计数
    output reg [7:0] cmd_error_cnt,      // 指令错误计数
    output reg [7:0] error_flag,         // 错误标识
    
    // 状态输出
    output reg [7:0] electron_temp,      // 电子温度
    output reg [7:0] proton_temp,        // 质子温度
    output reg [7:0] v12_value,          // 12V检测值
    output reg [7:0] v5_value,           // 5V检测值
    
    // 噪声数据输出
    output reg [7:0] proton_noise1,      // 质子噪声1
    output reg [7:0] proton_noise2,      // 质子噪声2
    output reg [7:0] proton_noise3,      // 质子噪声3
    output reg [7:0] highE_noise1,       // 高能电子噪声1
    output reg [7:0] highE_noise2,       // 高能电子噪声2
    output reg [7:0] highE_noise3,       // 高能电子噪声3
    output reg [7:0] midE_noise1,        // 中能电子噪声1
    output reg [7:0] midE_noise2,        // 中能电子噪声2
    output reg [7:0] midE_noise3,        // 中能电子噪声3
    output reg [7:0] midE_noise4,        // 中能电子噪声4
    output reg [7:0] midE_noise5,        // 中能电子噪声5
    output reg [7:0] midE_noise6,        // 中能电子噪声6
    output reg [63:0]proton_event
);

    // ================== 状态初始化和更新 ==================
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            utc_second <= 32'd0;
            utc_msecond <= 16'd0;
            
            time_code_cnt <= 8'd0;
            data_inject_cnt <= 8'd0;
            cmd_error_cnt <= 8'd0;
            
            error_flag <= 8'd0;      
            
            proton_event <= 64'h1122334455667788;
        end
        else begin
            
            // 更新UTC时间
            if (update_utc) begin
                utc_second <= new_utc_second;
                utc_msecond <= new_utc_msecond;
            end
            
            // 更新计数器
            if (inc_time_code) begin
                time_code_cnt <= time_code_cnt + 1'b1;
            end
            
            if (inc_data_inject) begin
                data_inject_cnt <= data_inject_cnt + 1'b1;
            end
            
            if (inc_cmd_error) begin
                cmd_error_cnt <= cmd_error_cnt + 1'b1;
                error_flag <= 8'h11; // 错误状态
            end
        end
    end
    
    // ================== 模拟噪声生成 ==================
    // 生成噪声数据的计数器
    reg [31:0] noise_counter;
    // 波形生成用参数
    reg [7:0] wave_position;
    reg up_direction;
    
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            noise_counter <= 32'd0;
            wave_position <= 8'd0;
            up_direction <= 1'b1;
			   // 噪声数据初始化
            proton_noise1 <= 8'd10;
            proton_noise2 <= 8'd12;
            proton_noise3 <= 8'd15;
            highE_noise1 <= 8'd20;
            highE_noise2 <= 8'd22;
            highE_noise3 <= 8'd25;
            midE_noise1 <= 8'd30;
            midE_noise2 <= 8'd32;
            midE_noise3 <= 8'd35;
            midE_noise4 <= 8'd37;
            midE_noise5 <= 8'd40;
            midE_noise6 <= 8'd42;
				 // 状态数据初始化
            electron_temp <= 8'd25;  // 25℃
            proton_temp <= 8'd25;    // 25℃
            v12_value <= 8'd120;     // 12.0V (表示为120)
            v5_value <= 8'd50;       // 5.0V (表示为50)
        end else begin
            noise_counter <= noise_counter + 1'b1;
            
            // 每隔一定时间更新噪声值
            if (noise_counter[19:0] == 20'd0) begin  // 约每21ms更新一次
                // 使用简单的三角波形代替随机生成
                if (up_direction) begin
                    wave_position <= wave_position + 1;
                    if (wave_position >= 8'd20) begin
                        up_direction <= 1'b0;
                    end
                end else begin
                    wave_position <= wave_position - 1;
                    if (wave_position <= 8'd1) begin
                        up_direction <= 1'b1;
                    end
                end
                
					// 使用位掩码代替取模运算
					proton_noise1 <= 8'd10 + (wave_position & 8'h03);         
					proton_noise2 <= 8'd12 + (wave_position + 2) & 8'h03;     
					proton_noise3 <= 8'd15 + (wave_position + 5) & 8'h03;     

					// 高能电子噪声 - 使用位掩码
					highE_noise1 <= 8'd20 + (wave_position + 3) & 8'h07;      
					highE_noise2 <= 8'd22 + (wave_position + 7) & 8'h03;      
					highE_noise3 <= 8'd25 + wave_position & 8'h03;           

					// 中能电子噪声 - 使用位掩码
					midE_noise1 <= 8'd30 + (wave_position * 2) & 8'h07;       
					midE_noise2 <= 8'd32 + (wave_position >> 1) + 1;          
					midE_noise3 <= 8'd35 + (wave_position * 3) & 8'h03;       
					midE_noise4 <= 8'd37 + (21 - wave_position) & 8'h07;      
					midE_noise6 <= 8'd42 + (wave_position + 10) & 8'h07;     

					// 温度和电压值也有少量波动
					electron_temp <= 8'd25 + (wave_position & 8'h01);         
					proton_temp <= 8'd25 + ((wave_position + 5) & 8'h01);     
					v12_value <= 8'd120 + (wave_position & 8'h01);            
					v5_value <= 8'd50 + ((wave_position + 7) & 8'h01);        
            end
        end
    end

endmodule 