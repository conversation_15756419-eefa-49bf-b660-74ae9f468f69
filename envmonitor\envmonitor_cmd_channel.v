module envmonitor_cmd_channel (
    input wire clk,                     
    input wire rst_n,                   
    input wire [7:0] baud_set,          
    input wire rs422_rx,                 
    
    // 系统时间输入
    input wire [31:0] sys_time_s,        // 系统时间(秒)
    input wire [15:0] sys_time_ms,       // 系统时间(毫秒)
    
    // 状态输出到共享状态模块
    output reg update_utc,               // 更新UTC时间标志
    output reg [31:0] new_utc_second,    // 新的UTC秒值
    output reg [15:0] new_utc_msecond,   // 新的UTC毫秒值
    
    // 计数器输出
    output reg inc_time_code,            // 增加时间码注入计数
    output reg inc_data_inject,          // 增加数据注入计数
    output reg inc_cmd_error,            // 增加指令错误计数
    
    // 触发工程参数遥测
    output reg trig_param_telemetry,     // 触发工程参数遥测
    
    // 触发科学数据遥测
    output reg trig_science_telemetry    // 触发科学数据遥测
);

    // ================== 常量定义 ==================
    // 帧结构参数
    localparam FRAME_HEAD       = 16'hEB90;  // 帧头
    localparam FRAME_TAIL       = 16'h09D7;  // 帧尾
    
    // 接收缓冲区大小
    localparam RX_BUFFER_SIZE   = 128;       // 接收缓冲区大小
    localparam RX_BUFFER_MASK   = 8'h7F;     // 接收缓冲区掩码
    
    // 服务类型定义
    localparam SVC_TIME_CODE    = 8'h13;     // 时间码广播
    localparam SVC_PARAM_REQ    = 8'h25;     // 工程参数请求
    localparam SVC_DATA_INJECT  = 8'h87;     // 数据注入指令
    localparam SVC_SCIENCE_REQ  = 8'h34;     // 科学数据发送指令
    
    // 服务状态定义
    localparam STS_TIME_CODE    = 8'h14;     // 时间码广播状态
    localparam STS_PARAM_REQ    = 8'h27;     // 工程参数请求状态
    localparam STS_DATA_INJECT  = 8'h89;     // 数据注入指令状态
    localparam STS_SCIENCE_REQ  = 8'h36;     // 科学数据发送指令状态
    
    // 数据块常量
    localparam PARAM_REQ_DATA   = 16'h5A5A;  // 工程参数请求数据
    localparam SCIENCE_REQ_DATA = 16'h0B0B;  // 科学数据请求数据
    
    // 状态机状态定义
    localparam RX_IDLE      = 4'h0; // 空闲状态
    localparam RX_HEAD_1    = 4'h1; // 帧头1
    localparam RX_HEAD_2    = 4'h2; // 帧头2
    localparam RX_LEN_1     = 4'h3; // 长度1
    localparam RX_LEN_2     = 4'h4; // 长度2
    localparam RX_SERVICE   = 4'h5; // 服务类型
    localparam RX_STATUS    = 4'h6; // 服务状态
    localparam RX_DATA      = 4'h7; // 数据域
    localparam RX_CHECK_1   = 4'h8; // 校验码1
    localparam RX_CHECK_2   = 4'h9; // 校验码2
    localparam RX_TAIL_1    = 4'hA; // 帧尾1
    localparam RX_TAIL_2    = 4'hB; // 帧尾2
    localparam RX_PROCESS   = 4'hC; // 处理命令
    
    // ================== 内部信号和寄存器 ==================
    // UART接口信号
    reg cs_n;                      // 片选信号
    reg re_n;                      // 读使能信号
    wire [7:0] rx_data;            // 接收数据
    wire rxrdy;                    // 接收就绪标志
    reg rxrdy_d1;                  // 寄存器捕获rxrdy的上一个状态
    wire rxrdy_posedge;            // rxrdy的上升沿脉冲信号
    assign rxrdy_posedge = rxrdy & ~rxrdy_d1;
    wire parity_err;               // 奇偶校验错误标志
    
    // 接收状态机
    reg [3:0] rx_state;            // 接收状态
    reg [7:0] rx_byte_cnt;         // 接收字节计数
    reg [7:0] rx_buffer[0:RX_BUFFER_SIZE-1]; // 接收缓冲区
    reg [7:0] rx_head;             // 接收缓冲区头指针
    reg [7:0] rx_tail;             // 接收缓冲区尾指针
    reg [7:0] data_length_h;       // 数据长度高字节
    reg [7:0] data_length_l;       // 数据长度低字节
    reg [15:0] data_length;        // 数据长度
    reg [15:0] checksum;           // 校验和
    reg [7:0] cmd_service_type;    // 服务类型
    reg [7:0] cmd_service_status;  // 服务状态
    
    // 临时数据存储
    reg [15:0] data_value;         // 存储请求数据值
        
    
    // ================== UART实例化 ==================
    COREUART uart_inst (
        .clk(clk),
        .rst_n(rst_n),
        
        .cs_n(cs_n),
        .baud_val(baud_set),
        .bit8(1'b1),        // 8位数据位
        .parity_en(1'b1),   // 使能奇偶校验
        .odd_n_even(1'b1),  // 奇校验
        
        .re_n(re_n),        // 读使能信号
        .rx(rs422_rx),      // 接收线
        .rxrdy(rxrdy),      // 接收就绪标志
        .data_out(rx_data), // 接收数据
        .parity_err(parity_err), // 奇偶校验错误
        
        .we_n(1'b1),        // 禁用发送功能
        .data_in(),    // 发送数据不使用
        .txrdy(),           // 发送就绪标志不使用
        .tx(),              // 发送线不使用
        .overflow()         // 溢出标志不使用
    );
    // 保存rxrdy的上一个状态
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            rxrdy_d1 <= 1'b0;  
        end
        else begin  
            rxrdy_d1 <= rxrdy;  // 保存当前状态
        end
    end
    // ================== 接收状态机 ==================
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            // 复位初始化
            rx_state <= RX_IDLE;
            rx_byte_cnt <= 8'h00;
            cs_n <= 1'b0;
            re_n <= 1'b1;
            rx_head <= 8'h00;
            rx_tail <= 8'h00;
            data_value <= 16'h0000;
            
            // 输出信号初始化
            update_utc <= 1'b0;
            new_utc_second <= 32'd0;
            new_utc_msecond <= 16'd0;
            inc_time_code <= 1'b0;
            inc_data_inject <= 1'b0;
            inc_cmd_error <= 1'b0;
            trig_param_telemetry <= 1'b0;
            trig_science_telemetry <= 1'b0;
        end
        else begin
            update_utc <= 1'b0;
            inc_time_code <= 1'b0;
            inc_data_inject <= 1'b0;
            inc_cmd_error <= 1'b0;
            trig_param_telemetry <= 1'b0;
            trig_science_telemetry <= 1'b0;
            
            // 处理UART接收
            if (rxrdy && !re_n) begin
                // 接收到数据，存入缓冲区
                rx_buffer[rx_head] <= rx_data;
                rx_head <= (rx_head + 1'b1) & RX_BUFFER_MASK;
                re_n <= 1'b1;
            end
            else if (rxrdy_posedge) begin
                // 准备接收新数据
                re_n <= 1'b0;
            end
            
            // 检测到奇偶校验错误
            if (parity_err) begin
                inc_cmd_error <= 1'b1;
            end
            
            // 接收状态机
            case (rx_state)
                RX_IDLE: begin
                    // 检查是否有数据可以处理
                    if (rx_head != rx_tail) begin
                        // 检查帧头第一个字节
                        if (rx_buffer[rx_tail] == FRAME_HEAD[15:8]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_HEAD_2;
                        end
                        else begin
                            // 无效数据，跳过
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        end
                    end
                end
                
                RX_HEAD_2: begin
                    // 检查帧头第二个字节
                    if (rx_head != rx_tail) begin
                        if (rx_buffer[rx_tail] == FRAME_HEAD[7:0]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_LEN_1;
                        end
                        else begin
                            // 帧头错误，返回空闲状态
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_IDLE;
                        end
                    end
                end
                
                RX_LEN_1: begin
                    // 接收数据长度高字节
                    if (rx_head != rx_tail) begin
                        data_length_h <= rx_buffer[rx_tail];
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        rx_state <= RX_LEN_2;
                    end
                end
                
                RX_LEN_2: begin
                    // 接收数据长度低字节
                    if (rx_head != rx_tail) begin
                        data_length_l <= rx_buffer[rx_tail];
                        data_length <= {data_length_h, rx_buffer[rx_tail]};
                        rx_byte_cnt <= 8'h00;
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        rx_state <= RX_SERVICE;
                        
                        // 初始化校验和计算
                        checksum <= {data_length_h, rx_buffer[rx_tail]};
                    end
                end
                
                RX_SERVICE: begin
                    // 接收服务类型
                    if (rx_head != rx_tail) begin
                        cmd_service_type <= rx_buffer[rx_tail];
                        checksum <= checksum + rx_buffer[rx_tail];
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        rx_state <= RX_STATUS;
                    end
                end
                
                RX_STATUS: begin
                    // 接收服务状态
                    if (rx_head != rx_tail) begin
                        cmd_service_status <= rx_buffer[rx_tail];
                        checksum <= checksum + rx_buffer[rx_tail];
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        rx_state <= RX_DATA;
                        data_value <= 16'h0000; // 重置数据值
                    end
                end
                
                RX_DATA: begin
                    // 接收数据字段
                    if (rx_head != rx_tail) begin
                        // 根据服务类型处理数据字段
                        case (cmd_service_type)
                            SVC_TIME_CODE: begin
                                // 时间码广播 - UTC时间 (6字节)
                                if (rx_byte_cnt == 8'h00) begin
                                    new_utc_second[31:24] <= rx_buffer[rx_tail];
                                end
                                else if (rx_byte_cnt == 8'h01) begin
                                    new_utc_second[23:16] <= rx_buffer[rx_tail];
                                end
                                else if (rx_byte_cnt == 8'h02) begin
                                    new_utc_second[15:8] <= rx_buffer[rx_tail];
                                end
                                else if (rx_byte_cnt == 8'h03) begin
                                    new_utc_second[7:0] <= rx_buffer[rx_tail];
                                end
                                else if (rx_byte_cnt == 8'h04) begin
                                    new_utc_msecond[15:8] <= rx_buffer[rx_tail];
                                end
                                else if (rx_byte_cnt == 8'h05) begin
                                    new_utc_msecond[7:0] <= rx_buffer[rx_tail];
                                end
                            end
                            
                            SVC_PARAM_REQ: begin
                                // 工程参数请求 (2字节固定值 5A5A)
                                if (rx_byte_cnt == 8'h00) begin
                                    data_value[15:8] <= rx_buffer[rx_tail];
                                end
                                else if (rx_byte_cnt == 8'h01) begin
                                    data_value[7:0] <= rx_buffer[rx_tail];
                                end
                            end
                            
                            SVC_SCIENCE_REQ: begin
                                // 科学数据请求 (2字节固定值 0B0B)
                                if (rx_byte_cnt == 8'h00) begin
                                    data_value[15:8] <= rx_buffer[rx_tail];
                                end
                                else if (rx_byte_cnt == 8'h01) begin
                                    data_value[7:0] <= rx_buffer[rx_tail];
                                end
                            end
                            
                            SVC_DATA_INJECT: begin
                                // 仅累加校验和
                            end
                            
                            default: begin
                                // 其他服务类型，仅累加校验和
                            end
                        endcase
                        
                        // 更新校验和
                        checksum <= checksum + rx_buffer[rx_tail];
                        
                        // 更新计数和指针
                        rx_byte_cnt <= rx_byte_cnt + 1'b1;
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        
                        // 检查是否接收完所有数据
                        if (rx_byte_cnt >= data_length - 16'h0003) begin
                            rx_state <= RX_CHECK_1;
                        end
                    end
                end
                
                RX_CHECK_1: begin
                    // 接收校验码高字节
                    if (rx_head != rx_tail) begin
                        if (rx_buffer[rx_tail] != checksum[15:8]) begin
                            // 校验和错误
                            inc_cmd_error <= 1'b1;
                            rx_state <= RX_IDLE;
                        end else begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_CHECK_2;
                        end
                    end
                end
                
                RX_CHECK_2: begin
                    // 接收校验码低字节
                    if (rx_head != rx_tail) begin
                        if (rx_buffer[rx_tail] != checksum[7:0]) begin
                            // 校验和错误
                            inc_cmd_error <= 1'b1;
                            rx_state <= RX_IDLE;
                        end else begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_TAIL_1;
                        end
                    end
                end
                
                RX_TAIL_1: begin
                    // 接收帧尾第一个字节
                    if (rx_head != rx_tail) begin
                        if (rx_buffer[rx_tail] == FRAME_TAIL[15:8]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_TAIL_2;
                        end
                        else begin
                            // 帧尾错误，返回空闲状态
                            inc_cmd_error <= 1'b1;
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_IDLE;
                        end
                    end
                end
                
                RX_TAIL_2: begin
                    // 接收帧尾第二个字节
                    if (rx_head != rx_tail) begin
                        if (rx_buffer[rx_tail] == FRAME_TAIL[7:0]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_PROCESS;
                        end
                        else begin
                            // 帧尾错误，返回空闲状态
                            inc_cmd_error <= 1'b1;
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_IDLE;
                        end
                    end
                end
                
                RX_PROCESS: begin
                    // 处理命令，根据服务类型和状态执行相应操作
                    case (cmd_service_type)
                        SVC_TIME_CODE: begin
                            if (cmd_service_status == STS_TIME_CODE) begin
                                // 时间码广播
                                update_utc <= 1'b1;    // 更新UTC时间
                                inc_time_code <= 1'b1; // 增加时间码注入计数
                            end else begin
                                inc_cmd_error <= 1'b1;
                            end
                        end
                        
                        SVC_PARAM_REQ: begin
                            if (cmd_service_status == STS_PARAM_REQ) begin
                                // 工程参数请求
                                if (data_value == PARAM_REQ_DATA) begin
                                    trig_param_telemetry <= 1'b1; // 触发工程参数遥测
                                end else begin
                                    inc_cmd_error <= 1'b1;
                                end
                            end else begin
                                inc_cmd_error <= 1'b1;
                            end
                        end
                        
                        SVC_DATA_INJECT: begin
                            if (cmd_service_status == STS_DATA_INJECT) begin
                                // 数据注入指令，只增加计数，不处理具体数据
                                inc_data_inject <= 1'b1;
                            end else begin
                                inc_cmd_error <= 1'b1;
                            end
                        end
                        
                        SVC_SCIENCE_REQ: begin
                            if (cmd_service_status == STS_SCIENCE_REQ) begin
                                // 科学数据请求
                                if (data_value == SCIENCE_REQ_DATA) begin
                                    trig_science_telemetry <= 1'b1; // 触发科学数据遥测
                                end else begin
                                    inc_cmd_error <= 1'b1;
                                end
                            end else begin
                                inc_cmd_error <= 1'b1;
                            end
                        end
                        
                        default: begin
                            // 未知服务类型
                            inc_cmd_error <= 1'b1;
                        end
                    endcase
                    
                    // 返回空闲状态
                    rx_state <= RX_IDLE;
                end
                
                default: begin
                    rx_state <= RX_IDLE;
                end
            endcase
        end
    end

endmodule 