module envmonitor_param_channel (
    input wire clk,                // 系统时钟
    input wire rst_n,              // 复位信号，低电平有效
    input wire [7:0] baud_val,     // 波特率配置
    
    // 触发信号
    input wire param_req_trigger,  // 工程参数请求触发
    
    // 系统状态输入
    input wire [31:0] utc_second,        // UTC秒计数
    input wire [15:0] utc_msecond,       // UTC毫秒值
    input wire [7:0] time_code_cnt,      // 时间码注入正确计数
    input wire [7:0] data_inject_cnt,    // 数据注入指令正确计数
    input wire [7:0] cmd_error_cnt,      // 指令错误计数
    input wire [7:0] error_flag,         // 错误标识
    
    // 状态数据输入
    input wire [7:0] electron_temp,      // 电子温度
    input wire [7:0] proton_temp,        // 质子温度
    input wire [7:0] v12_value,          // 12V检测值
    input wire [7:0] v5_value,           // 5V检测值
    
    // 噪声数据输入
    input wire [7:0] proton_noise1,      // 质子噪声1
    input wire [7:0] proton_noise2,      // 质子噪声2
    input wire [7:0] proton_noise3,      // 质子噪声3
    input wire [7:0] highE_noise1,       // 高能电子噪声1
    input wire [7:0] highE_noise2,       // 高能电子噪声2
    input wire [7:0] highE_noise3,       // 高能电子噪声3
    input wire [7:0] midE_noise1,        // 中能电子噪声1
    input wire [7:0] midE_noise2,        // 中能电子噪声2
    input wire [7:0] midE_noise3,        // 中能电子噪声3
    input wire [7:0] midE_noise4,        // 中能电子噪声4
    input wire [7:0] midE_noise5,        // 中能电子噪声5
    input wire [7:0] midE_noise6,        // 中能电子噪声6
    
    // 预置质子事件
    input wire [63:0] proton_event,      // 预置质子事件(8字节)
    
    // UART输出
    output wire tx                       // RS422发送线
);

    // ================== 内部参数定义 ==================
    // 工程参数数据包定义，固定为46字节
    localparam PARAM_PACKET_SIZE = 46;
    
    // 工程参数包字段位置定义
    localparam FRAME_HEAD_POS      = 0;  // 帧头位置(2字节)
    localparam DATA_LEN_POS        = 2;  // 数据长度位置(2字节)
    localparam SERVICE_TYPE_POS    = 4;  // 服务类型位置(1字节)
    localparam SERVICE_STATUS_POS  = 5;  // 服务状态位置(1字节)
    
    // 数据块起始位置 (从第6个字节开始)
    localparam TIME_CODE_POS       = 6;  // 时间码位置(6字节)
    localparam PACKET_SEQ_POS      = 12; // 包序号位置(2字节)
    localparam ELECTRON_TEMP_POS   = 14; // 电子温度位置(1字节)
    localparam PROTON_TEMP_POS     = 15; // 质子温度位置(1字节)
    localparam V12_VALUE_POS       = 16; // 12V检测位置(1字节)
    localparam V5_VALUE_POS        = 17; // 5V检测位置(1字节)
    localparam TIME_CODE_CNT_POS   = 18; // 时间码注入计数位置(1字节)
    localparam DATA_INJECT_CNT_POS = 19; // 数据注入计数位置(1字节)
    localparam CMD_ERROR_CNT_POS   = 20; // 指令错误计数位置(1字节)
    localparam ERROR_FLAG_POS      = 21; // 错误标识位置(1字节)
    
    // 噪声数据位置
    localparam PROTON_NOISE_POS    = 22; // 质子噪声位置(3字节)
    localparam HIGHE_NOISE_POS     = 25; // 高能电子噪声位置(3字节)
    localparam MIDE_NOISE_POS      = 28; // 中能电子噪声位置(6字节)
    
    // 预置质子事件位置
    localparam PROTON_EVENT_POS    = 34; // 预置质子事件位置(8字节)
    
    // 校验和和帧尾位置
    localparam CHECKSUM_POS        = 42; // 校验码位置(2字节)
    localparam FRAME_TAIL_POS      = 44; // 帧尾位置(2字节)
    
    // 固定参数值
    localparam [15:0] FRAME_HEAD   = 16'hEB90; // 帧头
    localparam [15:0] DATA_LEN     = 16'h0026; // 数据长度(38)
    localparam [7:0] SERVICE_TYPE  = 8'h25;    // 服务类型(工程参数)
    localparam [7:0] SERVICE_STATUS = 8'h27;   // 服务状态
    localparam [15:0] FRAME_TAIL   = 16'h09D7; // 帧尾
    
    // UART通信参数
    reg cs_n;                      // 片选信号
    reg we_n;                      // 写使能信号
    reg [7:0] data_in;             // 待写数据
    wire txrdy;                    // 发送就绪标志
    reg txrdy_prev;
    wire txrdy_rf;
    assign txrdy_rf = txrdy & ~txrdy_prev;
    
    // ================== 内部寄存器定义 ==================
    reg [2:0] state;               // 发送状态机状态
    reg [7:0] send_index;          // 发送索引
    reg [15:0] packet_seq_num;    // 包序号
    // 工程参数数据包缓冲区
    reg [7:0] param_data [0:PARAM_PACKET_SIZE-1];
    
    // 发送状态机状态定义
    localparam IDLE      = 3'b000; // 空闲状态
    localparam BUILD_PKG = 3'b001; // 构建数据包
    localparam SEND_PKG  = 3'b010; // 发送数据包
    localparam WAIT_DONE = 3'b011; // 等待发送完成
    
    // ================== UART实例化 ==================
    // 使用COREUART模块发送工程参数数据
    COREUART uart_inst (
        .clk(clk),
        .rst_n(rst_n),
        
        .cs_n(cs_n),
        .baud_val(baud_val),
        .bit8(1'b1),        // 8位数据位
        .parity_en(1'b1),   // 使能奇偶校验
        .odd_n_even(1'b1),  // 奇校验
        
        .we_n(we_n),        // 写使能信号
        .data_in(data_in),  // 待写数据
        .txrdy(txrdy),      // 发送就绪标志
        .tx(tx),            // 发送数据线
        
        .re_n(1'b1),        // 禁用接收功能
        .rx(),              // 接收线不使用
        .rxrdy(),
        .data_out(),
        .parity_err(),
        .overflow()
    );
    // 寄存器更新逻辑，保存txrdy的上一个状态
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n)
            txrdy_prev <= 1'b0;
        else
            txrdy_prev <= txrdy;
    end 
    // ================== 工程参数包构建和发送状态机 ==================
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            // 复位初始化
            state <= IDLE;
            send_index <= 8'd0;
            cs_n <= 1'b0;
            we_n <= 1'b1;
            data_in <= 8'h00;
            packet_seq_num <= 16'h0000;
        end
        else begin
            // 状态机
            case (state)
                IDLE: begin
                    // 等待工程参数请求触发
                    if (param_req_trigger) begin
                        state <= BUILD_PKG;
                    end
                    else begin
                        state <= IDLE;
                    end
                end
                
                BUILD_PKG: begin
                    // 构建工程参数数据包
                    build_param_package();
                    
                    // 更新包序号
                    packet_seq_num <= packet_seq_num + 1'b1;
                    
                    // 准备发送
                    send_index <= 8'd0;
                    state <= SEND_PKG;
                end
                
                SEND_PKG: begin
                    // 发送工程参数数据包
                    if (send_index < PARAM_PACKET_SIZE) begin
                            if (send_index == 8'd0) begin
                                if (txrdy) begin
                                    // UART就绪，发送下一个字节
                                    we_n <= 1'b0;
                                    data_in <= param_data[send_index];
                                    send_index <= send_index + 1'b1;
                            end
                            else begin
                                we_n <= 1'b1;
                            end
                        end
                        else begin
                            if (txrdy_rf) begin
                                we_n <= 1'b0;
                                data_in <= param_data[send_index];
                                send_index <= send_index + 1'b1;
                            end
                            else begin
                                we_n <= 1'b1;
                            end
                        end
                    end
                    else begin
                        // 所有数据已发送
                        we_n <= 1'b1;
                        state <= WAIT_DONE;
                    end
                end
                
                WAIT_DONE: begin
                    // 等待传输完成后回到空闲状态
                    state <= IDLE;
                end
                
                default: begin
                    state <= IDLE;
                end
            endcase
        end
    end
    
    // ================== 工程参数数据包构建任务 ==================
    task build_param_package;
        integer i;
        reg [15:0] checksum;
    begin
        // 1. 帧头 (2字节)
        param_data[FRAME_HEAD_POS] = FRAME_HEAD[15:8];
        param_data[FRAME_HEAD_POS+1] = FRAME_HEAD[7:0];
        
        // 2. 数据长度 (2字节)
        param_data[DATA_LEN_POS] = DATA_LEN[15:8];
        param_data[DATA_LEN_POS+1] = DATA_LEN[7:0];
        
        // 3. 服务类型 (1字节)
        param_data[SERVICE_TYPE_POS] = SERVICE_TYPE;
        
        // 4. 服务状态 (1字节)
        param_data[SERVICE_STATUS_POS] = SERVICE_STATUS;
        
        // 5. 数据块 (38字节)
        // 5.1 时间码 (6字节)
        param_data[TIME_CODE_POS] = utc_second[31:24];     // 秒计数最高字节
        param_data[TIME_CODE_POS+1] = utc_second[23:16];   // 秒计数次高字节
        param_data[TIME_CODE_POS+2] = utc_second[15:8];    // 秒计数次低字节
        param_data[TIME_CODE_POS+3] = utc_second[7:0];     // 秒计数最低字节
        param_data[TIME_CODE_POS+4] = utc_msecond[15:8];   // 毫秒高字节
        param_data[TIME_CODE_POS+5] = utc_msecond[7:0];    // 毫秒低字节
        
        // 5.2 包序号 (2字节)
        param_data[PACKET_SEQ_POS] = packet_seq_num[15:8]; // 包序号高字节
        param_data[PACKET_SEQ_POS+1] = packet_seq_num[7:0]; // 包序号低字节
        
        // 5.3 温度和电压状态 (4字节)
        param_data[ELECTRON_TEMP_POS] = electron_temp;    // 电子温度
        param_data[PROTON_TEMP_POS] = proton_temp;        // 质子温度
        param_data[V12_VALUE_POS] = v12_value;            // 12V检测
        param_data[V5_VALUE_POS] = v5_value;              // 5V检测
        
        // 5.4 计数器状态 (3字节)
        param_data[TIME_CODE_CNT_POS] = time_code_cnt;    // 时间码注入计数
        param_data[DATA_INJECT_CNT_POS] = data_inject_cnt; // 数据注入计数
        param_data[CMD_ERROR_CNT_POS] = cmd_error_cnt;    // 指令错误计数
        
        // 5.5 错误标识 (1字节)
        param_data[ERROR_FLAG_POS] = error_flag;          // 错误标识
        
        // 5.6 噪声数据 (12字节)
        // 质子噪声 (3字节)
        param_data[PROTON_NOISE_POS] = proton_noise1;     // 质子噪声1
        param_data[PROTON_NOISE_POS+1] = proton_noise2;   // 质子噪声2
        param_data[PROTON_NOISE_POS+2] = proton_noise3;   // 质子噪声3
        
        // 高能电子噪声 (3字节)
        param_data[HIGHE_NOISE_POS] = highE_noise1;       // 高能电子噪声1
        param_data[HIGHE_NOISE_POS+1] = highE_noise2;     // 高能电子噪声2
        param_data[HIGHE_NOISE_POS+2] = highE_noise3;     // 高能电子噪声3
        
        // 中能电子噪声 (6字节)
        param_data[MIDE_NOISE_POS] = midE_noise1;         // 中能电子噪声1
        param_data[MIDE_NOISE_POS+1] = midE_noise2;       // 中能电子噪声2
        param_data[MIDE_NOISE_POS+2] = midE_noise3;       // 中能电子噪声3
        param_data[MIDE_NOISE_POS+3] = midE_noise4;       // 中能电子噪声4
        param_data[MIDE_NOISE_POS+4] = midE_noise5;       // 中能电子噪声5
        param_data[MIDE_NOISE_POS+5] = midE_noise6;       // 中能电子噪声6
        
        // 5.7 预置质子事件 (8字节)
        param_data[PROTON_EVENT_POS] = proton_event[63:56];   // 字节0
        param_data[PROTON_EVENT_POS+1] = proton_event[55:48]; // 字节1
        param_data[PROTON_EVENT_POS+2] = proton_event[47:40]; // 字节2
        param_data[PROTON_EVENT_POS+3] = proton_event[39:32]; // 字节3
        param_data[PROTON_EVENT_POS+4] = proton_event[31:24]; // 字节4
        param_data[PROTON_EVENT_POS+5] = proton_event[23:16]; // 字节5
        param_data[PROTON_EVENT_POS+6] = proton_event[15:8];  // 字节6
        param_data[PROTON_EVENT_POS+7] = proton_event[7:0];   // 字节7
        
        // 6. 计算校验和 (数据长度+服务类型+服务状态+数据块的累加和)
        checksum = 16'h0000;
        
        // 数据长度
        checksum = checksum + param_data[DATA_LEN_POS];
        checksum = checksum + param_data[DATA_LEN_POS+1];
        
        // 服务类型和状态
        checksum = checksum + param_data[SERVICE_TYPE_POS];
        checksum = checksum + param_data[SERVICE_STATUS_POS];
        
        // 数据块 (38字节)
        for (i = TIME_CODE_POS; i < CHECKSUM_POS; i = i + 1) begin
            checksum = checksum + param_data[i];
        end
        
        // 7. 校验和 (2字节)
        param_data[CHECKSUM_POS] = checksum[15:8];
        param_data[CHECKSUM_POS+1] = checksum[7:0];
        
        // 8. 帧尾 (2字节)
        param_data[FRAME_TAIL_POS] = FRAME_TAIL[15:8];
        param_data[FRAME_TAIL_POS+1] = FRAME_TAIL[7:0];
    end
    endtask

endmodule 