module envmonitor_science_channel (
    input wire clk,                // 系统时钟
    input wire rst_n,              // 复位信号，低电平有效
    input wire [7:0] baud_val,     // 波特率配置
    
    // 触发信号
    input wire science_req_trigger, // 科学数据请求触发
    
    // 系统时间
    input wire [31:0] utc_second,  // UTC秒计数
    input wire [15:0] utc_msecond, // UTC毫秒值
        
    // UART输出
    output wire tx                 // RS422发送线
);

    // ================== 内部参数定义 ==================
    // 科学数据包定义，固定为18字节
    localparam SCIENCE_PACKET_SIZE = 18;
    
    // 科学数据包字段位置定义
    localparam SYNC_CODE_POS      = 0;  // 包同步码位置(2字节)
    localparam ID_POS             = 2;  // 标识位置(2字节)
    localparam PKT_CTRL_POS       = 4;  // 包序控制位置(2字节)
    localparam PKT_LEN_POS        = 6;  // 包数据域长度位置(2字节)
    localparam TIME_CODE_POS      = 8;  // 副导头-时间码位置(6字节)
    localparam SCIENCE_DATA_POS   = 14; // 科学数据位置(2字节)
    localparam CHECKSUM_POS       = 16; // 校验和位置(2字节)
    
    // 固定参数值
    localparam [15:0] SYNC_CODE   = 16'hEB90; // 包同步码
    localparam [15:0] PACKET_ID   = 16'h0932; // 包标识
    localparam [1:0] GROUP_FLAG   = 2'b11;    // 分组标志
    localparam [15:0] SCIENCE_DATA_PARAM = 16'hAAAA; // 科学数据固定值
    localparam [15:0] DATA_DOMAIN_LEN = 16'h0009; // 包数据域长度(10-1)
    
    // UART通信参数
    reg cs_n;                      // 片选信号
    reg we_n;                      // 写使能信号
    reg [7:0] data_in;             // 待写数据
    wire txrdy;                    // 发送就绪标志
    reg txrdy_prev;
    wire txrdy_rf;
    assign txrdy_rf = txrdy & ~txrdy_prev;
    // ================== 内部寄存器定义 ==================
    reg [2:0] state;               // 发送状态机状态
    reg [7:0] send_index;          // 发送索引
    reg [13:0] science_seq_num;
    // 科学数据包缓冲区
    reg [7:0] science_data [0:SCIENCE_PACKET_SIZE-1];
    
    // 发送状态机状态定义
    localparam IDLE      = 3'b000; // 空闲状态
    localparam BUILD_PKG = 3'b001; // 构建数据包
    localparam SEND_PKG  = 3'b010; // 发送数据包
    localparam WAIT_DONE = 3'b011; // 等待发送完成
    
    // ================== UART实例化 ==================
    // 使用COREUART模块发送科学数据
    COREUART uart_inst (
        .clk(clk),
        .rst_n(rst_n),
        
        .cs_n(cs_n),
        .baud_val(baud_val),
        .bit8(1'b1),        // 8位数据位
        .parity_en(1'b1),   // 使能奇偶校验
        .odd_n_even(1'b1),  // 奇校验
        
        .we_n(we_n),        // 写使能信号
        .data_in(data_in),  // 待写数据
        .txrdy(txrdy),      // 发送就绪标志
        .tx(tx),            // 发送数据线
        
        .re_n(1'b1),        // 禁用接收功能
        .rx(),              // 接收线不使用
        .rxrdy(),
        .data_out(),
        .parity_err(),
        .overflow()
    );
    //  寄存器更新逻辑，保存txrdy的上一个状态
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n)
            txrdy_prev <= 1'b0;
        else
            txrdy_prev <= txrdy;
    end
    // ================== 科学数据包构建和发送状态机 ==================
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            // 复位初始化
            state <= IDLE;
            send_index <= 8'd0;
            cs_n <= 1'b0;
            we_n <= 1'b1;
            data_in <= 8'h00;
            science_seq_num <= 14'h0000;
        end
        else begin
            // 状态机
            case (state)
                IDLE: begin
                    // 等待科学数据请求触发
                    if (science_req_trigger) begin
                        state <= BUILD_PKG;
                    end
                    else begin
                        state <= IDLE;
                    end
                end
                
                BUILD_PKG: begin
                    // 构建科学数据包
                    build_science_package();
                    
                    // 更新包序号
                    science_seq_num <= science_seq_num + 1'b1;
                    
                    // 准备发送
                    send_index <= 8'd0;
                    state <= SEND_PKG;
                end
                
                SEND_PKG: begin
                    // 发送科学数据包
                    if (send_index < SCIENCE_PACKET_SIZE) begin
                        if (send_index == 8'd0) begin
                            if (txrdy) begin
                            // UART就绪，发送下一个字节
                            we_n <= 1'b0;
                            data_in <= science_data[send_index];
                            send_index <= send_index + 1'b1;
                        end
                        else begin
                            we_n <= 1'b1;
                        end
                    end
                    else begin
                        if (txrdy_rf) begin
                            we_n <= 1'b0;
                            data_in <= science_data[send_index];
                            send_index <= send_index + 1'b1;
                        end
                        else begin
                            we_n <= 1'b1;
                        end
                    end
                    end
                    else begin
                        // 所有数据已发送
                        we_n <= 1'b1;
                        state <= WAIT_DONE;
                    end
                end
                
                WAIT_DONE: begin
                    // 等待传输完成后回到空闲状态
                    state <= IDLE;
                end
                
                default: begin
                    state <= IDLE;
                end
            endcase
        end
    end
    
    // ================== 科学数据包构建任务 ==================
    task build_science_package;
        integer i;
        reg [15:0] checksum;
    begin
        // 1. 包同步码 (2字节)
        science_data[SYNC_CODE_POS] = SYNC_CODE[15:8];
        science_data[SYNC_CODE_POS+1] = SYNC_CODE[7:0];
        
        // 2. 标识 (2字节)
        science_data[ID_POS] = PACKET_ID[15:8];
        science_data[ID_POS+1] = PACKET_ID[7:0];
        
        // 3. 包序控制 (2字节)
        // 高2位为分组标志，低14位为包序号
        science_data[PKT_CTRL_POS] = {GROUP_FLAG, science_seq_num[13:8]};
        science_data[PKT_CTRL_POS+1] = science_seq_num[7:0];
        
        // 4. 包数据域长度 (2字节)
        science_data[PKT_LEN_POS] = DATA_DOMAIN_LEN[15:8];
        science_data[PKT_LEN_POS+1] = DATA_DOMAIN_LEN[7:0];
        
        // 5. 副导头-时间码 (6字节)
        science_data[TIME_CODE_POS] = utc_second[31:24];     // 秒计数最高字节
        science_data[TIME_CODE_POS+1] = utc_second[23:16];   // 秒计数次高字节
        science_data[TIME_CODE_POS+2] = utc_second[15:8];    // 秒计数次低字节
        science_data[TIME_CODE_POS+3] = utc_second[7:0];     // 秒计数最低字节
        science_data[TIME_CODE_POS+4] = utc_msecond[15:8];   // 毫秒高字节
        science_data[TIME_CODE_POS+5] = utc_msecond[7:0];    // 毫秒低字节
        
        // 6. 科学数据 (2字节) - 固定值
        science_data[SCIENCE_DATA_POS] = SCIENCE_DATA_PARAM[15:8];
        science_data[SCIENCE_DATA_POS+1] = SCIENCE_DATA_PARAM[7:0];
        
        // 7. 计算校验和 (副导头和科学数据的累加和，取低16位)
        checksum = 16'h0000;
        
        // 累加副导头(时间码)和科学数据
        for (i = TIME_CODE_POS; i < CHECKSUM_POS; i = i + 1) begin
            checksum = checksum + science_data[i];
        end
        
        // 8. 校验和 (2字节)
        science_data[CHECKSUM_POS] = checksum[15:8];
        science_data[CHECKSUM_POS+1] = checksum[7:0];
    end
    endtask

endmodule 