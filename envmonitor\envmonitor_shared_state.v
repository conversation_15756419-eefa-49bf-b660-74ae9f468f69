module envmonitor_shared_state (
    input wire clk,                // 系统时钟 (50MHz)
    input wire rst_n,              // 复位信号，低电平有效
    
    // 系统时间输入
    input wire [31:0] sys_time_s,  // 系统时间(�?)
    input wire [15:0] sys_time_ms, // 系统时间(毫秒)
    
    // 命令通道状�?�更新输�?
    input wire update_utc,               // 更新UTC时间标志
    input wire [31:0] new_utc_second,    // 新的UTC秒�??
    input wire [15:0] new_utc_msecond,   // 新的UTC毫秒�?
    input wire inc_time_code,            // 增加时间码注入计�?
    input wire inc_data_inject,          // 增加数据注入计数
    input wire inc_cmd_error,            // 增加指令错误计数
    
    // 状�?�输�?
    output reg [31:0] utc_second,        // UTC秒计�?
    output reg [15:0] utc_msecond,       // UTC毫秒�?
    
    // 计数器输�?
    output reg [7:0] time_code_cnt,      // 时间码注入正确计�?
    output reg [7:0] data_inject_cnt,    // 数据注入指令正确计数
    output reg [7:0] cmd_error_cnt,      // 指令错误计数
    output reg [7:0] error_flag,         // 错误标识
    
    // 状�?�输�?
    output reg [7:0] electron_temp,      // 电子温度
    output reg [7:0] proton_temp,        // 质子温度
    output reg [7:0] v12_value,          // 12V�?测�??
    output reg [7:0] v5_value,           // 5V�?测�??
    
    // 噪声数据输出
    output reg [7:0] proton_noise1,      // 质子噪声1
    output reg [7:0] proton_noise2,      // 质子噪声2
    output reg [7:0] proton_noise3,      // 质子噪声3
    output reg [7:0] highE_noise1,       // 高能电子噪声1
    output reg [7:0] highE_noise2,       // 高能电子噪声2
    output reg [7:0] highE_noise3,       // 高能电子噪声3
    output reg [7:0] midE_noise1,        // 中能电子噪声1
    output reg [7:0] midE_noise2,        // 中能电子噪声2
    output reg [7:0] midE_noise3,        // 中能电子噪声3
    output reg [7:0] midE_noise4,        // 中能电子噪声4
    output reg [7:0] midE_noise5,        // 中能电子噪声5
    output reg [7:0] midE_noise6,        // 中能电子噪声6
    output reg [63:0] proton_event        // 预置质子事件
);

    // ================== 状�?�初始化和更�? ==================
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            // 重置状�?�变�?
            utc_second <= 32'd0;
            utc_msecond <= 16'd0;
            error_flag <= 8'd0;      // 无错�?
            // 重置计数�?
            time_code_cnt <= 8'd0;
            data_inject_cnt <= 8'd0;
            cmd_error_cnt <= 8'd0;
           
        end
        else begin
            // 根据命令通道的信号更新状�?
            
            // 更新UTC时间
            if (update_utc) begin
                utc_second <= new_utc_second;
                utc_msecond <= new_utc_msecond;
            end
            
            // 更新计数�?
            if (inc_time_code) begin
                time_code_cnt <= time_code_cnt + 1'b1;
            end
            
            if (inc_data_inject) begin
                data_inject_cnt <= data_inject_cnt + 1'b1;
            end
            
            if (inc_cmd_error) begin
                cmd_error_cnt <= cmd_error_cnt + 1'b1;
                error_flag <= 8'h11; // 错误状�??
            end
        end
    end
    
    // ================== 模拟噪声生成 ==================
    // 生成噪声数据的计数器
    reg [31:0] noise_counter;
    // 波形生成用参�?
    reg [7:0] wave_position;
    reg up_direction;
		 
	 always @(posedge clk or negedge rst_n) begin
			  if (!rst_n) begin
					noise_counter <= 32'd0;
					wave_position <= 8'd0;
					up_direction <= 1'b1;
					    // 状�?�数据初始化
            electron_temp <= 8'd25;  // 25�?
            proton_temp <= 8'd25;    // 25�?
            v12_value <= 8'd120;     // 12.0V (表示�?120)
            v5_value <= 8'd50;       // 5.0V (表示�?50)
            
            // 噪声数据初始�?
            proton_noise1 <= 8'd10;
            proton_noise2 <= 8'd12;
            proton_noise3 <= 8'd15;
            highE_noise1 <= 8'd20;
            highE_noise2 <= 8'd22;
            highE_noise3 <= 8'd25;
            midE_noise1 <= 8'd30;
            midE_noise2 <= 8'd32;
            midE_noise3 <= 8'd35;
            midE_noise4 <= 8'd37;
            midE_noise5 <= 8'd40;
            midE_noise6 <= 8'd42;
            proton_event <= 64'h1122334455667788;
			  end else begin
					noise_counter <= noise_counter + 1'b1;
					
					// 每隔一定时间更新噪声值
					if (noise_counter[19:0] == 20'd0) begin  // 约每21ms更新一次
						 // 使用简单的三角波形代替随机生成
						 if (up_direction) begin
							  wave_position <= wave_position + 1;
							  if (wave_position >= 8'd20) begin
									up_direction <= 1'b0;
							  end
						 end else begin
							  wave_position <= wave_position - 1;
							  if (wave_position <= 8'd1) begin
									up_direction <= 1'b1;
							  end
						 end
						 
						 // 基于波形位置生成噪声
						 // 质子噪声 - 使用基本波形
						 proton_noise1 <= 8'd10 + (wave_position & 8'h4); // 限制在0-4范围内
						 proton_noise2 <= 8'd12 + ((wave_position + 2) & 8'h6); // 限制在0-6范围内
						 proton_noise3 <= 8'd15 + ((wave_position + 5) & 8'h5); // 限制在0-5范围内
						 
						 // 高能电子噪声 - 使用相移波形
						 highE_noise1 <= 8'd20 + ((wave_position + 3) & 8'h7); // 限制在0-7范围内
						 highE_noise2 <= 8'd22 + ((wave_position + 7) & 8'h4); // 限制在0-4范围内
						 highE_noise3 <= 8'd25 + (wave_position & 8'h5); // 限制在0-5范围内
						 
						 // 中能电子噪声 - 使用更复杂的模式
						 midE_noise1 <= 8'd30 + ((wave_position << 1) & 8'h8); // 限制在0-8范围内
						 midE_noise2 <= 8'd32 + (wave_position >> 1);
						 midE_noise3 <= 8'd35 + ((wave_position + (wave_position << 1)) & 8'h6); // 限制在0-6范围内
						 midE_noise4 <= 8'd37 + ((21 - wave_position) & 8'h7); // 限制在0-7范围内
						 midE_noise5 <= 8'd40 + ((wave_position ^ 8'h5) & 8'h5); // 限制在0-5范围内
						 midE_noise6 <= 8'd42 + ((wave_position + 10) & 8'h8); // 限制在0-8范围内
						 
						 // 温度和电压值也有少量波动
						 electron_temp <= 8'd25 + (wave_position & 8'h2); // 限制在0-2范围内
						 proton_temp <= 8'd25 + ((wave_position + 5) & 8'h2); // 限制在0-2范围内
						 v12_value <= 8'd120 + (wave_position & 8'h1); // 限制在0-1范围内
						 v5_value <= 8'd50 + ((wave_position + 7) & 8'h1); // 限制在0-1范围内
					end
			  end
		 end

endmodule 