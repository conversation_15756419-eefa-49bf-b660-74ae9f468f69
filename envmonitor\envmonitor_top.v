module envmonitor_top (
    input wire clk,                
    input wire rst_n,              
    input wire [7:0] baud_val,     
    
    input wire [31:0] sys_time_s,  // 系统时间(秒)
    input wire [15:0] sys_time_ms, // 系统时间(毫秒)
    
    input wire env_rx,             // 命令接收线
    output wire env_tx             // 工程参数发送线
);

    // 内部连线
    wire [31:0] utc_second;        // UTC秒计数
    wire [15:0] utc_msecond;       // UTC毫秒值
    wire [7:0] time_code_cnt;      // 时间码注入正确计数
    wire [7:0] data_inject_cnt;    // 数据注入指令正确计数
    wire [7:0] cmd_error_cnt;      // 指令错误计数
    wire [7:0] error_flag;         // 错误标识
    
    wire update_utc;               // 更新UTC时间标志
    wire [31:0] new_utc_second;    // 新的UTC秒值
    wire [15:0] new_utc_msecond;   // 新的UTC毫秒值
    wire inc_time_code;            // 增加时间码注入计数
    wire inc_data_inject;          // 增加数据注入计数
    wire inc_cmd_error;            // 增加指令错误计数
      
    wire [15:0] midE_threshold;    // 中能电子阈值
    wire [15:0] highE_threshold;   // 高能电子阈值
    wire [15:0] proton_threshold;  // 质子阈值
    
    wire param_req_trigger;        // 工程参数请求触发
    wire science_req_trigger;      // 科学数据请求触发
        
    wire [7:0] electron_temp;      // 电子温度
    wire [7:0] proton_temp;        // 质子温度
    wire [7:0] v12_value;          // 12V检测值
    wire [7:0] v5_value;           // 5V检测值
    wire [7:0] proton_noise1;      // 质子噪声1
    wire [7:0] proton_noise2;      // 质子噪声2
    wire [7:0] proton_noise3;      // 质子噪声3
    wire [7:0] highE_noise1;       // 高能电子噪声1
    wire [7:0] highE_noise2;       // 高能电子噪声2
    wire [7:0] highE_noise3;       // 高能电子噪声3
    wire [7:0] midE_noise1;        // 中能电子噪声1
    wire [7:0] midE_noise2;        // 中能电子噪声2
    wire [7:0] midE_noise3;        // 中能电子噪声3
    wire [7:0] midE_noise4;        // 中能电子噪声4
    wire [7:0] midE_noise5;        // 中能电子噪声5
    wire [7:0] midE_noise6;        // 中能电子噪声6
    
    wire [63:0] proton_event;      // 预置质子事件(8字节)
    
    // 共享状态模块
    envmonitor_shared_state state_inst (
        .clk(clk),
        .rst_n(rst_n),
        .sys_time_s(sys_time_s),
        .sys_time_ms(sys_time_ms),
        .update_utc(update_utc),
        .new_utc_second(new_utc_second),
        .new_utc_msecond(new_utc_msecond),
        .inc_time_code(inc_time_code),
        .inc_data_inject(inc_data_inject),
        .inc_cmd_error(inc_cmd_error),
        .utc_second(utc_second),
        .utc_msecond(utc_msecond),
        .time_code_cnt(time_code_cnt),
        .data_inject_cnt(data_inject_cnt),
        .cmd_error_cnt(cmd_error_cnt),
        .electron_temp(electron_temp),
        .proton_temp(proton_temp),
        .v12_value(v12_value),
        .v5_value(v5_value),
        .error_flag(error_flag),
        .proton_noise1(proton_noise1),
        .proton_noise2(proton_noise2),
        .proton_noise3(proton_noise3),
        .highE_noise1(highE_noise1),
        .highE_noise2(highE_noise2),
        .highE_noise3(highE_noise3),
        .midE_noise1(midE_noise1),
        .midE_noise2(midE_noise2),
        .midE_noise3(midE_noise3),
        .midE_noise4(midE_noise4),
        .midE_noise5(midE_noise5),
        .midE_noise6(midE_noise6),
        .proton_event(proton_event)
    );
    
    // 命令接收模块
    envmonitor_cmd_channel cmd_inst (
        .clk(clk),
        .rst_n(rst_n),
        .baud_set(baud_val),
        .rs422_rx(env_rx),
        .sys_time_s(sys_time_s),
        .sys_time_ms(sys_time_ms),
        .update_utc(update_utc),
        .new_utc_second(new_utc_second),
        .new_utc_msecond(new_utc_msecond),
        .inc_time_code(inc_time_code),
        .inc_data_inject(inc_data_inject),
        .inc_cmd_error(inc_cmd_error),
        .trig_param_telemetry(param_req_trigger),
        .trig_science_telemetry(science_req_trigger)
    );
    
    // 科学数据和工程参数发送模块
    envmonitor_sci_param sci_param_inst (
        .clk(clk),
        .rst_n(rst_n),
        .baud_val(baud_val),
        .science_req_trigger(science_req_trigger),
        .param_req_trigger(param_req_trigger),
        .utc_second(utc_second),
        .utc_msecond(utc_msecond),
        .time_code_cnt(time_code_cnt),
        .data_inject_cnt(data_inject_cnt),
        .cmd_error_cnt(cmd_error_cnt),
        .error_flag(error_flag),
        .electron_temp(electron_temp),
        .proton_temp(proton_temp),
        .v12_value(v12_value),
        .v5_value(v5_value),
        .proton_noise1(proton_noise1),
        .proton_noise2(proton_noise2),
        .proton_noise3(proton_noise3),
        .highE_noise1(highE_noise1),
        .highE_noise2(highE_noise2),
        .highE_noise3(highE_noise3),
        .midE_noise1(midE_noise1),
        .midE_noise2(midE_noise2),
        .midE_noise3(midE_noise3),
        .midE_noise4(midE_noise4),
        .midE_noise5(midE_noise5),
        .midE_noise6(midE_noise6),
        .proton_event(proton_event),
        .tx(env_tx)
    );

endmodule 