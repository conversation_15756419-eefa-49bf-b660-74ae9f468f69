`timescale 1ns/1ps

module tb_envmonitor_top2();

// 系统信号
reg clk;
reg rst_n;
reg [31:0] sys_time_s;    // 系统时间(秒)
reg [15:0] sys_time_ms;   // 系统时间(毫秒)

wire env_tx;              // 环境监测数据输出通道
reg env_rx;               // 命令输入通道

// 接收数据缓冲区和计数器
reg [7:0] science_data[0:17];  // 科学数据缓冲区
integer science_count = 0;     // 科学数据计数器
integer i, j;                  // 循环计数器

// 命令数组
reg [7:0] science_req_cmd[0:11];  // 科学数据请求命令
reg [15:0] checksum;              // 校验和计算

// 波特率设置
parameter BAUD_RATE = 115200;     // 波特率
parameter BIT_TIME = 1000000000/BAUD_RATE; // 位时间（纳秒）

// 数据包大小
parameter SCIENCE_PACKET_SIZE = 18; // 科学数据包大小（固定18字节）

// 接收字节缓冲区
reg [7:0] rx_byte;

// 实例化环境监测顶层模块
envmonitor_top envmonitor_top_inst(
    .clk(clk),                  // 系统时钟 (40MHz)
    .rst_n(rst_n),              // 复位信号，低电平有效
    .baud_val(8'd21),           // 波特率配置 (115200bps@40MHz)
    .sys_time_s(sys_time_s),    // 系统时间(秒)
    .sys_time_ms(sys_time_ms),  // 系统时间(毫秒)
    .env_rx(env_rx),            // 命令接收线
    .env_tx(env_tx)             // 环境监测数据发送线
);

// 时钟生成
initial clk = 0;
always #12.5 clk = ~clk;  // 40MHz时钟 (周期25ns)

// 系统时间生成
initial sys_time_s = 32'h11223344;  // 初始时间
initial sys_time_ms = 16'h5566;     // 初始毫秒

// 每秒更新系统时间
always begin
    #40000000; // 40MHz时钟下的1秒
    sys_time_s = sys_time_s + 1;
    if (sys_time_ms >= 16'h03E7) begin // 999ms
        sys_time_ms = 0;
    end else begin
        sys_time_ms = sys_time_ms + 1;
    end
end

// 初始化命令
initial begin
    // 初始化科学数据请求命令
    // 帧头: 0xEB90
    science_req_cmd[0] = 8'hEB;
    science_req_cmd[1] = 8'h90;
    // 数据长度: 0x0004
    science_req_cmd[2] = 8'h00;
    science_req_cmd[3] = 8'h04;
    // 服务类型: 0x34 (科学数据请求)
    science_req_cmd[4] = 8'h34;
    // 服务状态: 0x36
    science_req_cmd[5] = 8'h36;
    // 数据块: 0x0B0B (固定值)
    science_req_cmd[6] = 8'h0B;
    science_req_cmd[7] = 8'h0B;
    
    // 计算科学数据请求命令校验和
    checksum = 0;
    for (i = 2; i <= 7; i = i + 1) begin
        checksum = checksum + science_req_cmd[i];
    end
    science_req_cmd[8] = checksum[15:8];
    science_req_cmd[9] = checksum[7:0];
    $display("科学数据请求校验和: 0x%h", checksum);
    
    // 帧尾: 0x09D7
    science_req_cmd[10] = 8'h09;
    science_req_cmd[11] = 8'hD7;
    
    // 初始化命令接收线为高电平（空闲状态）
    env_rx = 1;
end

// 主测试流程
initial begin
    // 系统复位
    rst_n = 0;
    #100 rst_n = 1;
    
    fork
        data_monitor;      
        test_sequence;     
    join
end

task test_sequence;
    begin
        #500000;
        
        // 循环发送10次科学数据请求
        repeat (10) begin
            $display("时间 %0t: 发送科学数据请求命令...", $time);
            send_science_req();
            
            // 等待科学数据响应（大约500个比特时间足够接收完整个科学数据包）
            #(BIT_TIME*500);
            
            // 延迟200us，等待系统准备下一个请求
            #200000;
        end
        
        // 测试完成
        $display("时间 %0t: 测试序列完成", $time);
        #100000 $finish;
    end
endtask

// 数据监控任务 
task data_monitor;
    reg [7:0] temp_buffer[0:5];  // 用于判断数据包类型的临时缓冲区
    integer buffer_count = 0;
    integer packet_receiving = 0;  // 0: 未接收, 1: 接收科学数据包
    
    forever begin
        // 等待起始位
        @(negedge env_tx);
        
        #(BIT_TIME/2);
        
        // 确认是起始位
        if (env_tx == 0) begin
            rx_byte = 0;
            
            // 读取8个数据位 (LSB first)
            for (i = 0; i < 8; i = i + 1) begin
                #BIT_TIME;
                rx_byte[i] = env_tx;
            end
            
            // 读取奇偶校验位
            #BIT_TIME;
            
            // 读取停止位
            #BIT_TIME;
            
            // 显示接收到的字节
            $display("时间 %0t: 接收到数据字节: 0x%h", $time, rx_byte);
            
            // 根据当前接收状态处理数据
            if (packet_receiving == 0) begin
                // 存储到临时缓冲区
                if (buffer_count < 6) begin
                    temp_buffer[buffer_count] = rx_byte;
                    buffer_count = buffer_count + 1;
                    
                    if (buffer_count == 6) begin
                        // 检查是否为科学数据包
                        if (temp_buffer[0] == 8'hEB && temp_buffer[1] == 8'h90 && 
                            temp_buffer[2] == 8'h09 && temp_buffer[3] == 8'h32) begin
                            // 确认为科学数据包
                            packet_receiving = 1;
                            
                            // 将临时缓冲区数据复制到科学数据缓冲区
                            for (i = 0; i < 6; i = i + 1) begin
                                science_data[i] = temp_buffer[i];
                            end
                            science_count = 6;
                            
                            $display("时间 %0t: 开始接收科学数据包", $time);
                        end
                        else begin
                            // 不是科学数据包，尝试恢复同步
                            $display("时间 %0t: 未识别的数据包: 0x%h%h%h%h%h%h", $time, 
                                    temp_buffer[0], temp_buffer[1], temp_buffer[2], 
                                    temp_buffer[3], temp_buffer[4], temp_buffer[5]);
                            
                            // 尝试重新同步，查找可能的帧头部分
                            buffer_count = 0;
                            for (i = 1; i < 6; i = i + 1) begin
                                if (temp_buffer[i] == 8'hEB && i < 5) begin
                                    temp_buffer[0] = 8'hEB;
                                    for (j = 1; j < 6-i; j = j + 1) begin
                                        temp_buffer[j] = temp_buffer[i+j];
                                    end
                                    buffer_count = 6-i;
                                   
                                end
                            end
                        end
                    end
                end
            end
            else if (packet_receiving == 1) begin
                // 接收科学数据包
                if (science_count < SCIENCE_PACKET_SIZE) begin
                    science_data[science_count] = rx_byte;
                    science_count = science_count + 1;
                    
                    // 检查是否接收完整的科学数据包
                    if (science_count == SCIENCE_PACKET_SIZE) begin
                        $display("时间 %0t: 接收到完整的科学数据包", $time);
                        
                        // 验证校验和
                        checksum = 0;
                        for (i = 8; i < SCIENCE_PACKET_SIZE-2; i = i + 1) begin
                            checksum = checksum + science_data[i];
                        end
                        
                        if (science_data[SCIENCE_PACKET_SIZE-2] == checksum[15:8] && 
                            science_data[SCIENCE_PACKET_SIZE-1] == checksum[7:0]) begin
                            $display("时间 %0t: 科学数据包校验正确", $time);
                            display_science_packet();
                        end else begin
                            $display("时间 %0t: 科学数据包校验错误", $time);
                            $display("期望校验和: 0x%h, 实际校验和: 0x%h%h", 
                                     checksum, science_data[SCIENCE_PACKET_SIZE-2], science_data[SCIENCE_PACKET_SIZE-1]);
                        end
                        
                        // 重置接收状态
                        packet_receiving = 0;
                        buffer_count = 0;
                    end
                end
            end
        end
    end
endtask

// 发送科学数据请求命令
task send_science_req;
    begin
        for (j = 0; j < 12; j = j + 1) begin
            send_byte(science_req_cmd[j]);
        end
        $display("时间 %0t: 科学数据请求命令已发送", $time);
    end
endtask

// 基本字节发送任务
task send_byte;
    input [7:0] byte_data;
    reg parity;  // 奇校验位
    begin
        // 计算奇校验位
        parity = ~(^byte_data);  // 奇校验的XOR操作
        
        // 起始位
        env_rx = 0;
        #BIT_TIME;
        
        // 8个数据位 (LSB first)
        for (i = 0; i < 8; i = i + 1) begin
            env_rx = (byte_data >> i) & 1'b1;
            #BIT_TIME;
        end
        
        // 奇校验位
        env_rx = parity;
        #BIT_TIME;
        
        // 停止位
        env_rx = 1;
        #BIT_TIME;
        
        // 字节间小延迟
        #(BIT_TIME/2);
    end
endtask

// 显示科学数据包
task display_science_packet;
    begin
        $display("====== 科学数据包 ======");
        $display("数据包同步码: 0x%h%h", science_data[0], science_data[1]);
        
        // 主头
        $display("-- 主头 --");
        $display("标识符: 0x%h%h", science_data[2], science_data[3]);
        
        $display("分组标志: 0x%h", (science_data[4] >> 6) & 3);
        $display("数据包序列计数: 0x%h%h", science_data[4][5:0], science_data[5]);
        
        $display("数据包数据域长度: 0x%h%h", science_data[6], science_data[7]);
        
        $display("-- 数据包数据域 --");
        
        // 副导头-时间码
        $display("时间码(秒): 0x%h%h%h%h", 
                science_data[8], science_data[9], science_data[10], science_data[11]);
        $display("时间码(毫秒): 0x%h%h", science_data[12], science_data[13]);
        
        // 科学数据
        $display("-- 有效数据域 --");
        $display("科学数据: 0x%h%h", science_data[14], science_data[15]);
        
        // 校验和
        $display("校验和: 0x%h%h", science_data[16], science_data[17]);
        
        $display("===========================");
    end
endtask

endmodule