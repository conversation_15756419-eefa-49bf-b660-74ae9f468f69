`timescale 1ns/1ps

module tb_envmonitor_top();

// 系统信号
reg clk;
reg rst_n;
reg [31:0] sys_time_s;    // 系统时间(秒)
reg [15:0] sys_time_ms;   // 系统时间(毫秒)

wire env_tx;              // 环境监测数据输出通道
reg env_rx;               // 命令输入通道

// 接收数据缓冲区和计数器
reg [7:0] param_data[0:45];    // 工程参数数据缓冲区
integer param_count = 0;       // 工程参数数据计数器
integer i, j;                  // 循环计数器

// 命令数组
reg [7:0] param_req_cmd[0:11];    // 工程参数请求命令
reg [15:0] checksum;              // 校验和计算

// 波特率设置
parameter BAUD_RATE = 115200;     // 波特率
parameter BIT_TIME = 1000000000/BAUD_RATE; // 位时间（纳秒）

// 数据包大小
parameter PARAM_PACKET_SIZE = 46;   // 工程参数数据包大小（固定46字节）

// 接收字节缓冲区
reg [7:0] rx_byte;

// 实例化环境监测顶层模块
envmonitor_top envmonitor_top_inst(
    .clk(clk),                  // 系统时钟 (40MHz)
    .rst_n(rst_n),              // 复位信号，低电平有效
    .baud_val(8'd21),           // 波特率配置 (115200bps@40MHz)
    .sys_time_s(sys_time_s),    // 系统时间(秒)
    .sys_time_ms(sys_time_ms),  // 系统时间(毫秒)
    .env_rx(env_rx),            // 命令接收线
    .env_tx(env_tx)             // 环境监测数据发送线
);

// 时钟生成
initial clk = 0;
always #12.5 clk = ~clk;  // 40MHz时钟 (周期25ns)

// 系统时间生成
initial sys_time_s = 32'h11223344;  // 初始时间
initial sys_time_ms = 16'h5566;     // 初始毫秒

// 每秒更新系统时间
always begin
    #40000000; // 40MHz时钟下的1秒
    sys_time_s = sys_time_s + 1;
    if (sys_time_ms >= 16'h03E7) begin // 999ms
        sys_time_ms = 0;
    end else begin
        sys_time_ms = sys_time_ms + 1;
    end
end

// 初始化命令
initial begin
    // 初始化工程参数请求命令
    // 帧头: 0xEB90
    param_req_cmd[0] = 8'hEB;
    param_req_cmd[1] = 8'h90;
    // 数据长度: 0x0004 
    param_req_cmd[2] = 8'h00;
    param_req_cmd[3] = 8'h04;
    // 服务类型: 0x25 (工程参数请求)
    param_req_cmd[4] = 8'h25;
    // 服务状态: 0x27
    param_req_cmd[5] = 8'h27;
    // 数据块: 0x5A5A (固定值)
    param_req_cmd[6] = 8'h5A;
    param_req_cmd[7] = 8'h5A;
    
    // 工程参数请求命令校验和
    checksum = 0;
    for (i = 2; i <= 7; i = i + 1) begin
        checksum = checksum + param_req_cmd[i];
    end
    param_req_cmd[8] = checksum[15:8];
    param_req_cmd[9] = checksum[7:0];
    $display("工程参数请求校验和: 0x%h", checksum);
    
    // 帧尾: 0x09D7
    param_req_cmd[10] = 8'h09;
    param_req_cmd[11] = 8'hD7;
    
    // 初始化命令接收线为高电平（空闲状态）
    env_rx = 1;
end

// 主测试流程
initial begin
    // 系统复位
    rst_n = 0;
    #100 rst_n = 1;
    
    fork
        data_monitor;      
        test_sequence;     
    join
end

task test_sequence;
    begin
        #500000;
        
        // 循环发送10次遥测请求
        repeat (10) begin
            $display("时间 %0t: 发送工程参数请求命令...", $time);
            send_param_req();
            
            // 等待工程参数响应（约700个比特时间够接收完整个遥测包）
            #(BIT_TIME*700);
            
            // 延迟200us，等待系统准备下一个请求
            #200000;
        end
        
        // 测试完成
        $display("时间 %0t: 测试序列完成", $time);
        #100000 $finish;
    end
endtask

// 数据监控任务 
task data_monitor;
    reg [7:0] temp_buffer[0:3];  // 用于判断数据包类型的临时缓冲区
    integer buffer_count = 0;
    integer packet_receiving = 0;  // 0: 未接收, 1: 接收遥测数据包
    
    forever begin
        // 等待起始位
        @(negedge env_tx);
        
        #(BIT_TIME/2);
        
        // 确认是起始位
        if (env_tx == 0) begin
            rx_byte = 0;
            
            // 读取8个数据位 (LSB first)
            for (i = 0; i < 8; i = i + 1) begin
                #BIT_TIME;
                rx_byte[i] = env_tx;
            end
            
            // 读取奇偶校验位
            #BIT_TIME;
            
            // 读取停止位
            #BIT_TIME;
            
            // 显示接收到的字节
            $display("时间 %0t: 接收到数据字节: 0x%h", $time, rx_byte);
            
            // 根据当前接收状态处理数据
            if (packet_receiving == 0) begin
                //存储到临时缓冲区
                if (buffer_count < 4) begin
                    temp_buffer[buffer_count] = rx_byte;
                    buffer_count = buffer_count + 1;
                    
                    if (buffer_count == 4) begin
                        // 数据包帧头和数据长度字段
                        if (temp_buffer[0] == 8'hEB && temp_buffer[1] == 8'h90) begin
                            if (temp_buffer[2] == 8'h00 && temp_buffer[3] == 8'h26) begin
                                // 遥测数据包 (0026)
                                packet_receiving = 1;
                                
                                for (i = 0; i < 4; i = i + 1) begin
                                    param_data[i] = temp_buffer[i];
                                end
                                param_count = 4;
                                
                                $display("时间 %0t: 开始接收遥测数据包", $time);
                            end
                            else begin
                                // 不是期望的数据包类型，重置缓冲区
                                $display("时间 %0t: 未知的数据包类型: 0x%h%h", $time, temp_buffer[2], temp_buffer[3]);
                                buffer_count = 0;
                            end
                        end
                        else begin
                            // 不是有效的帧头，重置缓冲区
                            $display("时间 %0t: 无效的帧头: 0x%h%h", $time, temp_buffer[0], temp_buffer[1]);
                            
                            // 恢复同步逻辑：检查是否有部分EB90帧头
                            if (temp_buffer[1] == 8'hEB) begin
                                temp_buffer[0] = 8'hEB;
                                temp_buffer[1] = temp_buffer[2];
                                temp_buffer[2] = temp_buffer[3];
                                buffer_count = 3;
                            end
                            else if (temp_buffer[2] == 8'hEB) begin
                                temp_buffer[0] = 8'hEB;
                                temp_buffer[1] = temp_buffer[3];
                                buffer_count = 2;
                            end
                            else if (temp_buffer[3] == 8'hEB) begin
                                temp_buffer[0] = 8'hEB;
                                buffer_count = 1;
                            end
                            else begin
                                buffer_count = 0;
                            end
                        end
                    end
                end
            end
            else if (packet_receiving == 1) begin
                // 接收遥测数据包
                if (param_count < PARAM_PACKET_SIZE) begin
                    param_data[param_count] = rx_byte;
                    param_count = param_count + 1;
                    
                    // 检查是否接收完整的遥测数据包
                    if (param_count == PARAM_PACKET_SIZE) begin
                        $display("时间 %0t: 接收到完整的工程参数数据包", $time);
                        
                        // 验证校验和
                        checksum = 0;
                        for (i = 2; i < PARAM_PACKET_SIZE-4; i = i + 1) begin
                            checksum = checksum + param_data[i];
                        end
                        
                        if (param_data[PARAM_PACKET_SIZE-4] == checksum[15:8] && 
                            param_data[PARAM_PACKET_SIZE-3] == checksum[7:0]) begin
                            $display("时间 %0t: 工程参数数据包校验正确", $time);
                            display_param_packet();
                        end else begin
                            $display("时间 %0t: 工程参数数据包校验错误", $time);
                            $display("期望校验和: 0x%h, 实际校验和: 0x%h%h", 
                                     checksum, param_data[PARAM_PACKET_SIZE-4], param_data[PARAM_PACKET_SIZE-3]);
                        end
                        
                        // 重置接收状态
                        packet_receiving = 0;
                        buffer_count = 0;
                    end
                end
            end
        end
    end
endtask

// 发送工程参数请求命令
task send_param_req;
    begin
        for (j = 0; j < 12; j = j + 1) begin
            send_byte(param_req_cmd[j]);
        end
        $display("时间 %0t: 工程参数请求命令已发送", $time);
    end
endtask

// 基本字节发送任务
task send_byte;
    input [7:0] byte_data;
    reg parity;  // 奇校验位
    begin
        // 计算奇校验位
        parity = ~(^byte_data);  // 奇校验的XOR操作
        
        // 起始位
        env_rx = 0;
        #BIT_TIME;
        
        // 8个数据位 (LSB first)
        for (i = 0; i < 8; i = i + 1) begin
            env_rx = (byte_data >> i) & 1'b1;
            #BIT_TIME;
        end
        
        // 奇校验位
        env_rx = parity;
        #BIT_TIME;
        
        // 停止位
        env_rx = 1;
        #BIT_TIME;
        
        // 字节间小延迟
        #(BIT_TIME/2);
    end
endtask

// 显示工程参数数据包
task display_param_packet;
    begin
        $display("====== 工程参数数据包 ======");
        $display("帧头: 0x%h%h", param_data[0], param_data[1]);
        $display("数据长度: 0x%h%h", param_data[2], param_data[3]);
        $display("服务类型: 0x%h", param_data[4]);
        $display("服务状态: 0x%h", param_data[5]);
        
        // 时间码
        $display("时间码(秒): 0x%h%h%h%h", 
                param_data[6], param_data[7], param_data[8], param_data[9]);
        $display("时间码(毫秒): 0x%h%h", param_data[10], param_data[11]);
        
        // 数据包序号
        $display("数据包序号: 0x%h%h", param_data[12], param_data[13]);
        
        // 温度和电压
        $display("电子温度: 0x%h", param_data[14]);
        $display("质子温度: 0x%h", param_data[15]);
        $display("12V检测: 0x%h", param_data[16]);
        $display("5V检测: 0x%h", param_data[17]);
        
        // 命令计数器
        $display("时间码注入正确计数: 0x%h", param_data[18]);
        $display("数据注入命令正确计数: 0x%h", param_data[19]);
        $display("命令错误计数: 0x%h", param_data[20]);
        
        // 错误标志
        $display("错误标志: 0x%h", param_data[21]);
        
        // 噪声数据
        $display("质子噪声: 0x%h 0x%h 0x%h", 
                param_data[22], param_data[23], param_data[24]);
        $display("高能电子噪声: 0x%h 0x%h 0x%h", 
                param_data[25], param_data[26], param_data[27]);
        $display("中能电子噪声: 0x%h 0x%h 0x%h 0x%h 0x%h 0x%h", 
                param_data[28], param_data[29], param_data[30], 
                param_data[31], param_data[32], param_data[33]);
        
        // 预置质子事件
        $display("预置质子事件: 0x%h 0x%h 0x%h 0x%h 0x%h 0x%h 0x%h 0x%h", 
                param_data[34], param_data[35], param_data[36], param_data[37],
                param_data[38], param_data[39], param_data[40], param_data[41]);
        
        // 校验和和帧尾
        $display("校验和: 0x%h%h", param_data[42], param_data[43]);
        $display("帧尾: 0x%h%h", param_data[44], param_data[45]);
        
        $display("响应时间: %0t ns", $time);
        $display("===========================");
    end
endtask

endmodule