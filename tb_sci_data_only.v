`timescale 1ns/1ps

module tb_sci_data_only();

reg clk;
reg rst_n;
wire sci_tx;        // Scientific data output channel
reg [7:0] rx_byte;
reg [7:0] sci_data[0:95];    // Array for science data (96 bytes)
integer sci_count = 0;
integer i;

// Baud rate settings
parameter BAUD_RATE = 115200;
parameter BIT_TIME = 1_000_000_000/BAUD_RATE; // Bit time in nanoseconds

// Packet sizes
parameter SCI_PACKET_SIZE = 96;  // Science data packet size

// 只实例化需要测试的模块，保留sci_tx相关接口
electrostatic_acc_top electrostatic_acc_top_inst(
    .clk(clk),
    .rst_n(rst_n),
    .baud_val(8'd26), // 115200bps @ 50MHz
    .sys_time_s(32'd0),
    .sys_time_ms(16'd0),
    .sci_tx(sci_tx),
    .cmd_rx(1'b1),     // 默认高电平（空闲状态）
    .telemetry_tx()    // 不连接
);

initial clk = 0;
always #12.5 clk = ~clk; // 50MHz clock

initial begin
    rst_n = 0;
    #100 rst_n = 1;
    
    // 只启动科学数据监控
    fork
        sci_monitor;     // Monitor for science data
        test_sequence;   // 简化的测试序列
    join
end

// 简化的测试序列
task test_sequence;
    begin
        // 等待一段时间让设备初始化
        #500000;
        
        // 等待并观察科学数据包（应该自动发送）
        $display("等待科学数据包...");
        wait(sci_count == SCI_PACKET_SIZE-1);
        
        // 继续等待接收几个科学数据包
        repeat(3) begin
            sci_count = 0; // 重置计数器
            wait(sci_count == SCI_PACKET_SIZE-1);
        end
        
        // 测试完成
        $display("测试序列成功完成");
        $finish;
    end
endtask

// 科学数据UART接收任务
task sci_monitor;
    forever begin
        // 等待起始位（低电平）
        @(negedge sci_tx);
        
        // 起始位中间点
        #(BIT_TIME/2);
        
        // 确认是起始位
        if (sci_tx == 0) begin
            rx_byte = 0;
            
            // 读取8位数据（LSB优先）
            for (i = 0; i < 8; i = i + 1) begin
                #BIT_TIME;
                rx_byte[i] = sci_tx;
            end
            
            // 读取校验位
            #BIT_TIME;
            // 这里可以添加校验位检查代码
            
            // 读取停止位
            #BIT_TIME;
            
            // 保存接收到的字节到科学数据数组
            sci_data[sci_count] = rx_byte;
            sci_count = sci_count + 1;
            
            // 打印接收到的字节
            $display("时间 %0t: 接收到科学数据字节: 0x%h", $time, rx_byte);
            
            // 检查是否接收到完整的科学数据包
            if (sci_count == SCI_PACKET_SIZE) begin
                display_sci_packet();
                // 不重置sci_count，由test_sequence控制
            end
        end
    end
endtask

// 显示完整的科学数据包
task display_sci_packet;
    begin
        $display("====== 完整科学数据包 ======");
        $display("帧头: 0x%h%h", sci_data[0], sci_data[1]);
        $display("数据长度: 0x%h%h", sci_data[2], sci_data[3]);
        $display("服务类型: 0x%h", sci_data[4]);
        $display("服务状态: 0x%h", sci_data[5]);
        $display("帧计数器: 0x%h%h%h", sci_data[6], sci_data[7], sci_data[8]);
        $display("工作模式状态: 0x%h", sci_data[16]);
        // 这里可以添加更多数据解析
        $display("校验和: 0x%h%h", sci_data[92], sci_data[93]);
        $display("帧尾: 0x%h%h", sci_data[94], sci_data[95]);
        $display("===============================");
    end
endtask

endmodule 