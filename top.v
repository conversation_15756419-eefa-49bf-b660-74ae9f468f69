//顶层模块，连接四个科学仪器
module top(
    input clk,                   // 系统时钟 (40MHz)
    
    // 加速度计接口
    input acc_cmd_rx,            // 加速度计遥控指令接收线
    output acc_sci_tx,           // 加速度计科学数据发送线
    output acc_telemetry_tx,     // 加速度计遥测数据发送线
    
    // 振动传感器接口
    input vib_rx,                // 振动监测子系统命令接收线
    output vib_tx,               // 振动监测子系统数据发送线
    
    // 环境检测接口
    input env_rx,                // 环境监测子系统命令接收线
    output env_tx,           // 环境监测科学数据发送线
    
    // 空间相机接口
    input  cam_rx,                // 监视相机命令接收线
    output cam_tx,                // 监视相机数据发送线  
    // 平台接口
    output platform_sci_tx,        // 平台科学数据发送线
    //GNSSA接口
    output gnssa_sci_tx,           // GNSSA科学数据发送线
    //GNSSB接口
    output gnssb_sci_tx,           // GNSSB科学数据发送线
    //AD5383接口    
    output wire FIFO_EN,
    output wire CLR_N,
    output wire AD5383_RESET_N,
    output wire PD,
    output wire PAR_N,
    output wire A_N_B,
    output wire REG0,
    output wire REG1,
    output wire CS_N,
    output wire WR_N,
    output wire [4:0] ADDRESS,
    output wire [11:0] DB,
	output pps
);

    // 内部信号定义
    reg [31:0] sys_time_s;       // 系统时间(秒)
    reg [15:0] sys_time_ms;      // 系统时间(毫秒)
    reg [31:0] ms_counter;       // 毫秒计数器
    
    wire [7:0] baud_val;
    assign baud_val = 8'd21;     // 40MHz/(115200*16) ≈ 21
    
    wire clkin_bufg;
    wire LOCKED_OUT;
    assign rst_n = LOCKED_OUT;
	 
    assign pps = (sys_time_ms == 16'd0);
	 
    // 系统时间生成
    always @(posedge clkin_bufg or negedge rst_n) begin
        if (!rst_n) begin
            ms_counter <= 32'd0;
            sys_time_ms <= 16'd0;
            sys_time_s <= 32'd0;
        end
        else begin
            if (ms_counter >= 32'd39999) begin
                ms_counter <= 32'd0;
                
                if (sys_time_ms >= 16'd999) begin
                    sys_time_ms <= 16'd0;
                    sys_time_s <= sys_time_s + 1'b1;
                end
                else begin
                    sys_time_ms <= sys_time_ms + 1'b1;
                end
            end
            else begin
                ms_counter <= ms_counter + 1'b1;
            end
        end
    end
	 
    DCM_40M DCM_inst (
       .CLKIN_IN(clk), 
       .RST_IN(1'b0), 
       .CLKIN_IBUFG_OUT(clkin_bufg), 
       .CLK0_OUT(), 
       .LOCKED_OUT(LOCKED_OUT)
    );

    // 加速度计模块实例化
    electrostatic_acc_top acc_inst (
        .clk(clkin_bufg),
        .rst_n(rst_n),
        .baud_val(baud_val),
        .sys_time_s(sys_time_s),
        .sys_time_ms(sys_time_ms),
        .sci_tx(acc_sci_tx),
        .cmd_rx(acc_cmd_rx),
        .telemetry_tx(acc_telemetry_tx)
    );
    
    // 振动监测子系统模块实例化
    vibration_top vib_inst (
        .clk(clkin_bufg),
        .rst_n(rst_n),
        .baud_val(baud_val),
        .rx(vib_rx),
        .tx(vib_tx)
    );
    
    // 环境监测子系统模块实例化
    envmonitor_top env_inst (
        .clk(clkin_bufg),
        .rst_n(rst_n),
        .baud_val(baud_val),
        .env_rx(env_rx),
        .env_tx(env_tx)
    );
    
    // 卫星监视相机模块实例化
    camera_top cam_inst (
        .clk(clkin_bufg),
        .rst_n(rst_n),
        .baud_val(baud_val),
        .rs422_rx(cam_rx),
        .rs422_tx(cam_tx)
    );

    // 平台模块实例化
    platform_top platform_inst (
        .clk(clkin_bufg),
        .rst_n(rst_n),
        .baud_val(baud_val),
        .sci_tx(platform_sci_tx)
    );
    
    // GNSSA模块实例化
    gnssa_top gnssa_inst (
        .clk(clkin_bufg),
        .rst_n(rst_n),
        .baud_val(baud_val),
        .sci_tx(gnssa_sci_tx)
    );

    // GNSSB模块实例化
    gnssb_top gnssb_inst (
        .clk(clkin_bufg),
        .rst_n(rst_n),  
        .baud_val(baud_val),
        .sci_tx(gnssb_sci_tx)
    );
    
   AD5383_CTRL AD5383_inst(
    .clk_40mhz(clkin_bufg),
    .reset_n(rst_n),
    .FIFO_EN(FIFO_EN),
    .CLR_N(CLR_N),
    .AD5383_RESET_N(AD5383_RESET_N),
    .PD(PD),
    .PAR_N(PAR_N),
    .A_N_B(A_N_B),
    .REG0(REG0),    
    .REG1(REG1),
    .CS_N(CS_N),
    .WR_N(WR_N),
    .ADDRESS(ADDRESS),
    .DB(DB)
   );

endmodule