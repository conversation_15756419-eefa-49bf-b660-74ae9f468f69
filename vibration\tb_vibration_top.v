`timescale 1ns/1ps

module tb_vibration_monitor();

reg clk;
reg rst_n;
wire telemetry_tx;  // Telemetry data output channel
reg cmd_rx;         // Command input channel
reg [7:0] rx_byte;
reg [7:0] tlm_data[0:233];   // Array for telemetry data (up to 256 bytes)
integer tlm_count = 0;
integer i;
integer j;
reg [15:0] checksum_telemetry_req;
reg [15:0] checksum_detailed_survey;
reg [15:0] checksum_routine_survey;
reg [15:0] checksum_stop_survey;
reg [15:0] checksum_start_sending;
reg [15:0] checksum_stop_sending;
reg [15:0] checksum_time_broadcast;

// Command arrays
reg [7:0] telemetry_req_cmd[0:11];      // Telemetry request command
reg [7:0] detailed_survey_cmd[0:13];    // Detailed survey start command
reg [7:0] routine_survey_cmd[0:13];     // Routine survey start command
reg [7:0] stop_survey_cmd[0:13];        // Stop survey command
reg [7:0] start_sending_cmd[0:13];      // Start sending data command
reg [7:0] stop_sending_cmd[0:13];       // Stop sending data command
reg [7:0] time_broadcast_cmd[0:15];     // Time broadcast command

// Baud rate settings
parameter BAUD_RATE = 115200;
parameter BIT_TIME = 1_000_000_000/BAUD_RATE; // Bit time in nanoseconds

parameter TLM_PACKET_SIZE = 234; // Telemetry data packet size (10 bytes header/footer + 224 bytes data)

vibration_top vibration_top_inst(
    .clk(clk),                // 系统时钟 (50MHz)
    .rst_n(rst_n),              // 复位信号，低电平有效
    .baud_val(8'd21),     // 波特率配置
    // RS422接口
    .rx(cmd_rx),                 // 指令接收线
    .tx(telemetry_tx)                // 遥测数据发送线

);

initial clk = 0;
always #12.5 clk = ~clk; // 40MHz clock (25ns period)

// Initialize commands with calculated checksums
initial begin
    // Initialize telemetry request command (Table 4)
    // Frame header: 0xEB90
    telemetry_req_cmd[0] = 8'hEB;
    telemetry_req_cmd[1] = 8'h90;
    // Data length: 0x0004
    telemetry_req_cmd[2] = 8'h00;
    telemetry_req_cmd[3] = 8'h04;
    // Service type: 0x01
    telemetry_req_cmd[4] = 8'h01;
    // Service status: 0x00
    telemetry_req_cmd[5] = 8'h00;
    // Data block: 0x2201
    telemetry_req_cmd[6] = 8'h22;
    telemetry_req_cmd[7] = 8'h01;
    
    // Initialize detailed survey command (Table 5)
    // Frame header: 0xEB90
    detailed_survey_cmd[0] = 8'hEB;
    detailed_survey_cmd[1] = 8'h90;
    // Data length: 0x0006
    detailed_survey_cmd[2] = 8'h00;
    detailed_survey_cmd[3] = 8'h06;
    // Service type: 0x08
    detailed_survey_cmd[4] = 8'h08;
    // Service status: 0x00
    detailed_survey_cmd[5] = 8'h00;
    // Data block: 0x146F5555
    detailed_survey_cmd[6] = 8'h14;
    detailed_survey_cmd[7] = 8'h6F;
    detailed_survey_cmd[8] = 8'h55;
    detailed_survey_cmd[9] = 8'h55;
    
    // Initialize routine survey command (Table 6)
    // Frame header: 0xEB90
    routine_survey_cmd[0] = 8'hEB;
    routine_survey_cmd[1] = 8'h90;
    // Data length: 0x0006
    routine_survey_cmd[2] = 8'h00;
    routine_survey_cmd[3] = 8'h06;
    // Service type: 0x08
    routine_survey_cmd[4] = 8'h08;
    // Service status: 0x00
    routine_survey_cmd[5] = 8'h00;
    // Data block: 0x146FAAAA
    routine_survey_cmd[6] = 8'h14;
    routine_survey_cmd[7] = 8'h6F;
    routine_survey_cmd[8] = 8'hAA;
    routine_survey_cmd[9] = 8'hAA;
    
    // Initialize stop survey command (Table 7)
    // Frame header: 0xEB90
    stop_survey_cmd[0] = 8'hEB;
    stop_survey_cmd[1] = 8'h90;
    // Data length: 0x0006
    stop_survey_cmd[2] = 8'h00;
    stop_survey_cmd[3] = 8'h06;
    // Service type: 0x08
    stop_survey_cmd[4] = 8'h08;
    // Service status: 0x00
    stop_survey_cmd[5] = 8'h00;
    // Data block: 0x146FBBBB
    stop_survey_cmd[6] = 8'h14;
    stop_survey_cmd[7] = 8'h6F;
    stop_survey_cmd[8] = 8'hBB;
    stop_survey_cmd[9] = 8'hBB;
    
    // Initialize start sending data command (Table 8)
    // Frame header: 0xEB90
    start_sending_cmd[0] = 8'hEB;
    start_sending_cmd[1] = 8'h90;
    // Data length: 0x0006
    start_sending_cmd[2] = 8'h00;
    start_sending_cmd[3] = 8'h06;
    // Service type: 0x08
    start_sending_cmd[4] = 8'h08;
    // Service status: 0x00
    start_sending_cmd[5] = 8'h00;
    // Data block: 0x146FCCCC
    start_sending_cmd[6] = 8'h14;
    start_sending_cmd[7] = 8'h6F;
    start_sending_cmd[8] = 8'hCC;
    start_sending_cmd[9] = 8'hCC;
    
    // Initialize stop sending data command (Table 9)
    // Frame header: 0xEB90
    stop_sending_cmd[0] = 8'hEB;
    stop_sending_cmd[1] = 8'h90;
    // Data length: 0x0006
    stop_sending_cmd[2] = 8'h00;
    stop_sending_cmd[3] = 8'h06;
    // Service type: 0x08
    stop_sending_cmd[4] = 8'h08;
    // Service status: 0x00
    stop_sending_cmd[5] = 8'h00;
    // Data block: 0x146FDDDD
    stop_sending_cmd[6] = 8'h14;
    stop_sending_cmd[7] = 8'h6F;
    stop_sending_cmd[8] = 8'hDD;
    stop_sending_cmd[9] = 8'hDD;
    
    // Initialize time broadcast command (Table 10)
    // Frame header: 0xEB90
    time_broadcast_cmd[0] = 8'hEB;
    time_broadcast_cmd[1] = 8'h90;
    // Data length: 0x0008
    time_broadcast_cmd[2] = 8'h00;
    time_broadcast_cmd[3] = 8'h08;
    // Service type: 0x0E
    time_broadcast_cmd[4] = 8'h0E;
    // Service status: 0x00
    time_broadcast_cmd[5] = 8'h00;
    // Data block: UTC seconds (4 bytes) + milliseconds (2 bytes)
    time_broadcast_cmd[6] = 8'h12; // UTC seconds (0x12345678)
    time_broadcast_cmd[7] = 8'h34;
    time_broadcast_cmd[8] = 8'h56;
    time_broadcast_cmd[9] = 8'h78;
    time_broadcast_cmd[10] = 8'h01; // milliseconds (0x0123)
    time_broadcast_cmd[11] = 8'h23;
    
    // Calculate and fill checksums for all commands
    // For telemetry request
    checksum_telemetry_req = 0;
    for (i = 2; i <= 7; i = i + 1) begin
        checksum_telemetry_req = checksum_telemetry_req + telemetry_req_cmd[i];
    end
    telemetry_req_cmd[8] = checksum_telemetry_req[15:8];
    telemetry_req_cmd[9] = checksum_telemetry_req[7:0];
    $display("Telemetry request checksum: 0x%h", checksum_telemetry_req);
    
    // For detailed survey
    checksum_detailed_survey = 0;
    for (i = 2; i <= 9; i = i + 1) begin
        checksum_detailed_survey = checksum_detailed_survey + detailed_survey_cmd[i];
    end
    detailed_survey_cmd[10] = checksum_detailed_survey[15:8];
    detailed_survey_cmd[11] = checksum_detailed_survey[7:0];
    $display("Detailed survey checksum: 0x%h", checksum_detailed_survey);
    
    // For routine survey
    checksum_routine_survey = 0;
    for (i = 2; i <= 9; i = i + 1) begin
        checksum_routine_survey = checksum_routine_survey + routine_survey_cmd[i];
    end
    routine_survey_cmd[10] = checksum_routine_survey[15:8];
    routine_survey_cmd[11] = checksum_routine_survey[7:0];
    $display("Routine survey checksum: 0x%h", checksum_routine_survey);
    
    // For stop survey
    checksum_stop_survey = 0;
    for (i = 2; i <= 9; i = i + 1) begin
        checksum_stop_survey = checksum_stop_survey + stop_survey_cmd[i];
    end
    stop_survey_cmd[10] = checksum_stop_survey[15:8];
    stop_survey_cmd[11] = checksum_stop_survey[7:0];
    $display("Stop survey checksum: 0x%h", checksum_stop_survey);
    
    // For start sending data
    checksum_start_sending = 0;
    for (i = 2; i <= 9; i = i + 1) begin
        checksum_start_sending = checksum_start_sending + start_sending_cmd[i];
    end
    start_sending_cmd[10] = checksum_start_sending[15:8];
    start_sending_cmd[11] = checksum_start_sending[7:0];
    $display("Start sending checksum: 0x%h", checksum_start_sending);
    
    // For stop sending data
    checksum_stop_sending = 0;
    for (i = 2; i <= 9; i = i + 1) begin
        checksum_stop_sending = checksum_stop_sending + stop_sending_cmd[i];
    end
    stop_sending_cmd[10] = checksum_stop_sending[15:8];
    stop_sending_cmd[11] = checksum_stop_sending[7:0];
    $display("Stop sending checksum: 0x%h", checksum_stop_sending);
    
    // For time broadcast
    checksum_time_broadcast = 0;
    for (i = 2; i <= 11; i = i + 1) begin
        checksum_time_broadcast = checksum_time_broadcast + time_broadcast_cmd[i];
    end
    time_broadcast_cmd[12] = checksum_time_broadcast[15:8];
    time_broadcast_cmd[13] = checksum_time_broadcast[7:0];
    $display("Time broadcast checksum: 0x%h", checksum_time_broadcast);
    
    // Add frame tail to all commands
    // Telemetry request
    telemetry_req_cmd[10] = 8'h09;
    telemetry_req_cmd[11] = 8'hD7;
    
    // Detailed survey
    detailed_survey_cmd[12] = 8'h09;
    detailed_survey_cmd[13] = 8'hD7;
    
    // Routine survey
    routine_survey_cmd[12] = 8'h09;
    routine_survey_cmd[13] = 8'hD7;
    
    // Stop survey
    stop_survey_cmd[12] = 8'h09;
    stop_survey_cmd[13] = 8'hD7;
    
    // Start sending
    start_sending_cmd[12] = 8'h09;
    start_sending_cmd[13] = 8'hD7;
    
    // Stop sending
    stop_sending_cmd[12] = 8'h09;
    stop_sending_cmd[13] = 8'hD7;
    
    // Time broadcast
    time_broadcast_cmd[14] = 8'h09;
    time_broadcast_cmd[15] = 8'hD7;
    
    // Default RX line state is high (idle)
    cmd_rx = 1;
end

initial begin
    rst_n = 0;
    #100 rst_n = 1;
    
    // Start UART monitoring and test sequence tasks
    fork
        tlm_monitor;     // Monitor for telemetry data
        test_sequence;   // Main test sequence
    join
end

// Test sequence task
task test_sequence;
    begin
        // Wait for some time to let the device initialize
        #50000;
        
        // Test basic telemetry request
        $display("Time %0t: Sending telemetry request command...", $time);
        send_telemetry_req();
        
        // Wait for telemetry response (should come within 2ms)
        $display("Time %0t: Waiting for telemetry packet response...", $time);
        wait(tlm_count == TLM_PACKET_SIZE);
        tlm_count = 0;
        
        // Wait longer between commands (100,000ns = 100us)
        #100000;
        
        // Send detailed survey command
        $display("Time %0t: Sending detailed survey command...", $time);
        send_detailed_survey();
        
        // Wait longer after command (10,000ns = 10us)
        #10000;
        
        // Request telemetry to verify mode change
        $display("Time %0t: Sending telemetry request to verify mode...", $time);
        send_telemetry_req();
        wait(tlm_count == TLM_PACKET_SIZE);
        tlm_count = 0;
        
        // Wait longer between commands
        #100000;
        
        // Send routine survey command
        $display("Time %0t: Sending routine survey command...", $time);
        send_routine_survey();
        
        // Wait longer after command
        #10000;
        
        // Request telemetry to verify mode change
        $display("Time %0t: Sending telemetry request to verify mode...", $time);
        send_telemetry_req();
        wait(tlm_count == TLM_PACKET_SIZE);
        tlm_count = 0;
        
        // Wait longer between commands
        #100000;
        
        // Send start sending data command
        $display("Time %0t: Sending start sending data command...", $time);
        send_start_sending();
        
        // Wait longer after command
        #10000;
        
        // Request telemetry to verify mode change
        $display("Time %0t: Sending telemetry request to verify mode...", $time);
        send_telemetry_req();
        wait(tlm_count == TLM_PACKET_SIZE);
        tlm_count = 0;
        
        // Wait longer between commands
        #100000;
        
        // Send stop sending data command
        $display("Time %0t: Sending stop sending data command...", $time);
        send_stop_sending();
        
        // Wait longer after command
        #10000;
        
        // Request telemetry to verify mode change
        $display("Time %0t: Sending telemetry request to verify mode...", $time);
        send_telemetry_req();
        wait(tlm_count == TLM_PACKET_SIZE);
        tlm_count = 0;
        
        // Wait longer between commands
        #100000;
        
        // Send stop survey command
        $display("Time %0t: Sending stop survey command...", $time);
        send_stop_survey();
        
        // Wait longer after command
        #10000;
        
        // Request telemetry to verify mode change
        $display("Time %0t: Sending telemetry request to verify mode...", $time);
        send_telemetry_req();
        wait(tlm_count == TLM_PACKET_SIZE);
        tlm_count = 0;
        
        // Wait longer between commands
        #100000;
        
        // Send time broadcast command
        $display("Time %0t: Sending time broadcast command...", $time);
        send_time_broadcast();
        
        // Wait longer after command
        #10000;
        
        // Request telemetry to verify new time
        $display("Time %0t: Sending telemetry request to verify time...", $time);
        send_telemetry_req();
        wait(tlm_count == TLM_PACKET_SIZE);
        tlm_count = 0;
        
        // Test complete
        $display("Time %0t: Test sequence completed successfully", $time);
        #10000 $finish;
    end
endtask
// 修改后的tlm_monitor任务，集成校验和验证
// 修改后的tlm_monitor任务，直接集成校验和验证
task tlm_monitor;
    reg [15:0] calculated_checksum; // 计算得到的校验和
    reg [15:0] received_checksum;   // 接收到的校验和
    integer chk_i;                  // 校验和循环专用计数器
    
    forever begin
        // 等待起始位 (低电平)
        @(negedge telemetry_tx);
        
        // 起始位中间采样点
        #(BIT_TIME/2);
        
        // 确认是起始位
        if (telemetry_tx == 0) begin
            rx_byte = 0;
            
            // 读取8个数据位 (LSB first)
            for (i = 0; i < 8; i = i + 1) begin
                #BIT_TIME;
                rx_byte[i] = telemetry_tx;
            end
            
            // 读取奇偶校验位
            #BIT_TIME;
            // 此处可以添加奇偶校验验证
            
            // 读取停止位
            #BIT_TIME;
            
            // 保存接收到的字节到遥测数据数组
            tlm_data[tlm_count] = rx_byte;
            tlm_count = tlm_count + 1;
            
            // 打印接收到的字节
            $display("Time %0t: 接收到遥测字节: 0x%h", $time, rx_byte);
            
            // 检查是否收到完整的遥测数据包
            if (tlm_count == TLM_PACKET_SIZE) begin
                // 验证帧头
                if (tlm_data[0] == 8'hEB && tlm_data[1] == 8'h90) begin
                    // 验证帧尾
                    if (tlm_data[TLM_PACKET_SIZE-2] == 8'h09 && tlm_data[TLM_PACKET_SIZE-1] == 8'hD7) begin
                        // 验证校验和 - 内联校验和计算和验证
                        calculated_checksum = 16'h0000;
                        
                        // 从数据长度字段(第2字节)开始，到校验和字段之前结束
                        for (chk_i = 2; chk_i < TLM_PACKET_SIZE-4; chk_i = chk_i + 1) begin
                            calculated_checksum = calculated_checksum + tlm_data[chk_i];
                        end
                        
                        // 获取接收到的校验和 (高字节在前，低字节在后)
                        received_checksum = {tlm_data[TLM_PACKET_SIZE-4], tlm_data[TLM_PACKET_SIZE-3]};
                        
                        // 显示校验和信息，用于调试
                        $display("Time %0t: 计算的校验和: 0x%h, 接收到的校验和: 0x%h", 
                                 $time, calculated_checksum, received_checksum);
                        
                        // 比较计算的校验和和接收到的校验和
                        if (calculated_checksum == received_checksum) begin
                            $display("Time %0t: 遥测数据包校验和验证成功", $time);
                            display_tlm_packet(); // 显示完整的遥测数据包
                        end else begin
                            $display("Time %0t: 遥测数据包校验和验证失败", $time);
                            $display("接收到的校验和: 0x%h%h", 
                                     tlm_data[TLM_PACKET_SIZE-4], tlm_data[TLM_PACKET_SIZE-3]);
                        end
                    end else begin
                        $display("Time %0t: 遥测数据包帧尾错误", $time);
                    end
                end else begin
                    $display("Time %0t: 遥测数据包帧头错误", $time);
                end
                
                // 重置计数器，准备接收下一个数据包
               // tlm_count = 0;
            end
        end
    end
endtask

// Individual command sending tasks
task send_telemetry_req;
    begin
        for (j = 0; j < 12; j = j + 1) begin
            send_byte(telemetry_req_cmd[j]);
        end
        $display("Time %0t: Telemetry request command sent", $time);
    end
endtask

task send_detailed_survey;
    begin
        for (j = 0; j < 14; j = j + 1) begin
            send_byte(detailed_survey_cmd[j]);
        end
        $display("Time %0t: Detailed survey command sent", $time);
    end
endtask

task send_routine_survey;
    begin
        for (j = 0; j < 14; j = j + 1) begin
            send_byte(routine_survey_cmd[j]);
        end
        $display("Time %0t: Routine survey command sent", $time);
    end
endtask

task send_stop_survey;
    begin
        for (j = 0; j < 14; j = j + 1) begin
            send_byte(stop_survey_cmd[j]);
        end
        $display("Time %0t: Stop survey command sent", $time);
    end
endtask

task send_start_sending;
    begin
        for (j = 0; j < 14; j = j + 1) begin
            send_byte(start_sending_cmd[j]);
        end
        $display("Time %0t: Start sending data command sent", $time);
    end
endtask

task send_stop_sending;
    begin
        for (j = 0; j < 14; j = j + 1) begin
            send_byte(stop_sending_cmd[j]);
        end
        $display("Time %0t: Stop sending data command sent", $time);
    end
endtask

task send_time_broadcast;
    begin
        for (j = 0; j < 16; j = j + 1) begin
            send_byte(time_broadcast_cmd[j]);
        end
        $display("Time %0t: Time broadcast command sent", $time);
    end
endtask

// Basic byte sending task
task send_byte;
    input [7:0] byte_data;
    begin
        // Start bit
        cmd_rx = 0;
        #BIT_TIME;
        
        // 8 data bits (LSB first)
        for (i = 0; i < 8; i = i + 1) begin
            cmd_rx = (byte_data >> i) & 1'b1;
            #BIT_TIME;
        end
        
        // Parity bit (odd parity)
        cmd_rx = ~(^byte_data); // XOR all bits and invert for odd parity
        #BIT_TIME;
        
        // Stop bit
        cmd_rx = 1;
        #BIT_TIME;
        
        // Small delay between bytes
        #(BIT_TIME/2);
    end
endtask

// Display complete telemetry data packet
task display_tlm_packet;
    begin
        $display("====== COMPLETE TELEMETRY DATA PACKET ======");
        $display("Frame Header: 0x%h%h", tlm_data[0], tlm_data[1]);
        $display("Data Length: 0x%h%h", tlm_data[2], tlm_data[3]);
        $display("Service Type: 0x%h", tlm_data[4]);
        $display("Service Status: 0x%h", tlm_data[5]);
        
        // Counters and basic status
        $display("Telemetry Request Counter: 0x%h", tlm_data[6]);
        $display("Command Receive Counter: 0x%h", tlm_data[7]);
        $display("Command Execute Counter: 0x%h", tlm_data[8]);
        $display("RS422 Reset Counter: 0x%h", tlm_data[9]);
        
        // Time information
        $display("UTC Seconds: 0x%h%h%h%h", tlm_data[10], tlm_data[11], tlm_data[12], tlm_data[13]);
        
        // Working mode and error status
        $display("Working Mode: 0x%h", tlm_data[14]);
        $display("Communication Error: 0x%h", tlm_data[15]);
        $display("Command Verification Error: 0x%h", tlm_data[16]);
        $display("Command Execution Error: 0x%h", tlm_data[17]);
        
        // Display first few bytes of vibration data
        $display("Vibration Data (first 16 bytes): ");
        $display("  0x%h 0x%h 0x%h 0x%h 0x%h 0x%h 0x%h 0x%h 0x%h 0x%h 0x%h 0x%h 0x%h 0x%h 0x%h 0x%h", 
                 tlm_data[18], tlm_data[19], tlm_data[20], tlm_data[21],
                 tlm_data[22], tlm_data[23], tlm_data[24], tlm_data[25],
                 tlm_data[26], tlm_data[27], tlm_data[28], tlm_data[29],
                 tlm_data[30], tlm_data[31], tlm_data[32], tlm_data[33]);
        
        // Checksum and frame tail
        $display("Checksum: 0x%h%h", tlm_data[TLM_PACKET_SIZE-4], tlm_data[TLM_PACKET_SIZE-3]);
        $display("Frame Tail: 0x%h%h", tlm_data[TLM_PACKET_SIZE-2], tlm_data[TLM_PACKET_SIZE-1]);
        
        // Response time
        $display("Response time: %0t ns", $time);
        $display("=============================================");
    end
endtask

endmodule