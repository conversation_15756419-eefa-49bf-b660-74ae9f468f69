module vibration_cmd_channel (
    input wire clk,                // 系统时钟
    input wire rst_n,              // 复位信号，低电平有效
    input wire [7:0] baud_val,     // 波特率配�?
    
    // UART接收
    input wire rx,                 // RS422接收�?
    
    // 状�?�更�?
    output reg [7:0] work_mode,          // 工作模式
    output reg [7:0] comm_err_flag,      // 通信异常标志
    output reg [7:0] cmd_check_err_flag, // 指令校验异常标志
    output reg [7:0] telemetry_req_cnt,  // 遥测请求计数
    output reg [7:0] cmd_recv_cnt,       // 接收指令计数
    output reg [7:0] cmd_exec_cnt,       // 成功执行指令计数
    output reg [7:0] uart_reset_cnt,     // 异步串口复位计数
    output reg [31:0] utc_second,        // UTC秒计�?
    output reg [15:0] utc_msecond,       // UTC毫秒�?
    output reg [7:0] cmd_exec_err_flag,   // 指令执行异常标志
    // 遥测请求
    output reg telemetry_req             // 遥测请求信号
 
);

    // ================== 常量定义 ==================
    // 帧结构参�?
    localparam FRAME_HEAD       = 16'hEB90;  // 帧头
    localparam FRAME_TAIL       = 16'h09D7;  // 帧尾
    
    // 接收缓冲区大�?
    localparam RX_BUFFER_SIZE   = 128;       // 接收缓冲区大�?
    localparam RX_BUFFER_MASK   = 8'h7F;     // 接收缓冲区掩�?
    
    // 工作模式常量
    localparam IDLE_MODE     = 8'h00;  // 空闲/停止模式
    localparam DETAIL_MODE   = 8'h55;  // 详查模式 
    localparam INSPECT_MODE  = 8'hAA;  // 巡查模式
    
    // 状�?�标志常�?
    localparam STATUS_NORMAL = 8'h00;  // 正常状�??
    localparam STATUS_ERROR  = 8'h11;  // 异常状�??
    
    // 指令相关常量
    localparam SERVICE_TYPE_TELEMETRY_REQ = 8'h01;  // 遥测请求服务类型
    localparam SERVICE_TYPE_CMD           = 8'h08;  // 控制指令服务类型
    localparam SERVICE_TYPE_TIME          = 8'h0E;  // 时间广播服务类型
    
    // 微振动功能标识常�?
    localparam DETAIL_COLLECT_ID = 32'h146F5555;  // 详查采集功能标识
    localparam INSPECT_COLLECT_ID = 32'h146FAAAA; // 巡查采集功能标识
    localparam STOP_COLLECT_ID = 32'h146FBBBB;    // 停止采集功能标识
    localparam START_SEND_ID = 32'h146FCCCC;      // �?始发送功能标�?
    localparam STOP_SEND_ID = 32'h146FDDDD;       // 停止发�?�功能标�?
    localparam TELEMETRY_ID = 16'h2201;           // 遥测数据标识
    
    // ================== 内部信号和寄存器 ==================
    // UART接口信号
    reg cs_n;                      // 片�?�信�?
    reg re_n;                      // 读使能信�?
    wire [7:0] rx_data;            // 接收数据
    wire rxrdy;                    // 接收就绪标志
    reg rxrdy_d1;                  // 寄存器捕获rxrdy的上�?个状�?
    wire rxrdy_posedge;            // rxrdy的上升沿脉冲信号
    assign rxrdy_posedge = rxrdy & ~rxrdy_d1;
    wire parity_err;               // 奇偶校验错误标志
    
    // 接收状�?�机
    reg [3:0] rx_state;            // 接收状�??
    reg [7:0] rx_byte_cnt;         // 接收字节计数
    reg [7:0] rx_buffer[0:RX_BUFFER_SIZE-1]; // 接收缓冲�?
    reg [7:0] rx_head;             // 接收缓冲区头指针
    reg [7:0] rx_tail;             // 接收缓冲区尾指针
    reg [7:0] data_length_h;       // 数据长度高字�?
    reg [7:0] data_length_l;       // 数据长度低字�?
    reg [15:0] data_length;        // 数据长度
    reg [15:0] checksum;           // 校验�?
    reg [7:0] cmd_service_type;    // 服务类型
    reg [7:0] cmd_service_status;  // 服务状�??
    reg [31:0] func_id;            // 功能标识 (数据块中提取)
    
    // 超时计数�?
    reg [31:0] timeout_counter;    // 10秒超时计数器
    wire timeout_flag;             // 超时标志
    
    // 状�?�机状�?�定�?
    localparam RX_IDLE      = 4'h0; // 空闲状�??
    localparam RX_HEAD_1    = 4'h1; // 帧头1
    localparam RX_HEAD_2    = 4'h2; // 帧头2
    localparam RX_LEN_1     = 4'h3; // 长度1
    localparam RX_LEN_2     = 4'h4; // 长度2
    localparam RX_SERVICE   = 4'h5; // 服务类型
    localparam RX_STATUS    = 4'h6; // 服务状�??
    localparam RX_DATA      = 4'h7; // 数据�?
    localparam RX_CHECK_1   = 4'h8; // 校验�?1
    localparam RX_CHECK_2   = 4'h9; // 校验�?2
    localparam RX_TAIL_1    = 4'hA; // 帧尾1
    localparam RX_TAIL_2    = 4'hB; // 帧尾2
    localparam RX_PROCESS   = 4'hC; // 处理命令
    
    // 超时�?�? - 计数10�? (50MHz时钟, 10s = 500,000,000个时钟周�?)
    localparam TIMEOUT_MAX = 32'd400_000_000;
    
    assign timeout_flag = (timeout_counter >= TIMEOUT_MAX) ? 1'b1 : 1'b0;
    
    // ================== UART实例�? ==================
    COREUART uart_inst (
        .clk(clk),
        .rst_n(rst_n),
        
        .cs_n(cs_n),
        .baud_val(baud_val),
        .bit8(1'b1),        // 8位数据位
        .parity_en(1'b1),   // 使能奇偶校验
        .odd_n_even(1'b1),  // 奇校�?
        
        .re_n(re_n),        // 读使能信�?
        .rx(rx),            // 接收�?
        .rxrdy(rxrdy),      // 接收就绪标志
        .data_out(rx_data), // 接收数据
        .parity_err(parity_err), // 奇偶校验错误
        
        .we_n(1'b1),        // 禁用发�?�功�?
        .data_in(8'h00),    // 发�?�数据不使用
        .txrdy(),           // 发�?�就绪标志不使用
        .tx(),              // 发�?�线不使�?
        .overflow()         // 溢出标志不使�?
    );
    
    // 寄存器更新�?�辑，保存rxrdy的上�?个状�?
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            rxrdy_d1 <= 1'b0;  
        end
        else begin  
            rxrdy_d1 <= rxrdy;  // 保存当前状�??
        end
    end
    // ================== 接收状�?�机实现 ==================
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            // 复位初始�?
            rx_state <= RX_IDLE;
            rx_byte_cnt <= 8'h00;
            cs_n <= 1'b0;
            re_n <= 1'b1;
            rx_head <= 8'h00;
            rx_tail <= 8'h00;
            telemetry_req <= 1'b0;
            timeout_counter <= 32'd0;
            
            // 状�?�初始化
            work_mode <= IDLE_MODE;
            comm_err_flag <= STATUS_NORMAL;
            cmd_check_err_flag <= STATUS_NORMAL;
            telemetry_req_cnt <= 8'h00;
            cmd_recv_cnt <= 8'h00;
            cmd_exec_cnt <= 8'h00;
            uart_reset_cnt <= 8'h00;
            utc_second <= 32'h00000000;
            utc_msecond <= 16'h0000;
            cmd_exec_err_flag <= STATUS_NORMAL;
        end
        else begin
            // 处理UART接收
            if (rxrdy && !re_n) begin
                // 接收到数据，存入缓冲�?
                rx_buffer[rx_head] <= rx_data;
                rx_head <= (rx_head + 1'b1) & RX_BUFFER_MASK;
                re_n <= 1'b1;
            end
            else if (rxrdy_posedge) begin
                // 准备接收新数�?
                re_n <= 1'b0;
            end
            
            // 清除遥测请求标志
            if (telemetry_req) begin
                telemetry_req <= 1'b0;
            end
            
            // 超时计数器更�?
            if (timeout_flag) begin
                // 超时处理
                timeout_counter <= 32'd0;
                comm_err_flag <= STATUS_ERROR;
                uart_reset_cnt <= uart_reset_cnt + 1'b1;
            end
            else begin
                timeout_counter <= timeout_counter + 1'b1;
            end
            
            // 接收状�?�机
            case (rx_state)
                RX_IDLE: begin
                    // �?查是否有数据可以处理
                    if (rx_head != rx_tail) begin
                        // �?查帧头第�?个字�?
                        if (rx_buffer[rx_tail] == FRAME_HEAD[15:8]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_HEAD_2;
                        end
                        else begin
                            // 无效数据，跳�?
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        end
                    end
                end
                
                RX_HEAD_2: begin
                    if (rx_head != rx_tail) begin
                        if (rx_buffer[rx_tail] == FRAME_HEAD[7:0]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_LEN_1;
                        end
                        else begin
                            // 无效帧头，返回空闲状�?
                            rx_state <= RX_IDLE;
                        end
                    end
                end
                
                RX_LEN_1: begin
                    if (rx_head != rx_tail) begin
                        data_length_h <= rx_buffer[rx_tail];
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        rx_state <= RX_LEN_2;
                    end
                end
                
                RX_LEN_2: begin
                    if (rx_head != rx_tail) begin
                        data_length_l <= rx_buffer[rx_tail];
                        data_length <= {data_length_h, rx_buffer[rx_tail]};
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        rx_state <= RX_SERVICE;
                        
                        // 初始化校验和计算
                        checksum <= {data_length_h, rx_buffer[rx_tail]};
                    end
                end
                
                RX_SERVICE: begin
                    if (rx_head != rx_tail) begin
                        cmd_service_type <= rx_buffer[rx_tail];
                        checksum <= checksum + rx_buffer[rx_tail];
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        rx_state <= RX_STATUS;
                    end
                end
                
                RX_STATUS: begin
                    if (rx_head != rx_tail) begin
                        cmd_service_status <= rx_buffer[rx_tail];
                        checksum <= checksum + rx_buffer[rx_tail];
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        rx_byte_cnt <= 8'h00;
                        
                        // 增加接收指令计数
                        cmd_recv_cnt <= cmd_recv_cnt + 1'b1;
                        
                        // 根据服务类型初始化func_id
                        func_id <= 32'h00000000;
                        rx_state <= RX_DATA;
                    end
                end
                
                RX_DATA: begin
                    if (rx_head != rx_tail) begin
                        // 存储和处理数据块
                        case (cmd_service_type)
                            SERVICE_TYPE_TELEMETRY_REQ: begin
                                // 遥测请求服务 - 存储遥测标识 (2字节)
                                if (rx_byte_cnt == 8'h00) begin
                                    func_id[15:8] <= rx_buffer[rx_tail]; // 高字�?
                                end
                                else if (rx_byte_cnt == 8'h01) begin
                                    func_id[7:0] <= rx_buffer[rx_tail];  // 低字�?
                                end
                            end
                            
                            SERVICE_TYPE_CMD: begin
                                // 微振动命�? - 功能标识 (4字节)
                                if (rx_byte_cnt == 8'h00) begin
                                    func_id[31:24] <= rx_buffer[rx_tail]; // �?高字�?
                                end
                                else if (rx_byte_cnt == 8'h01) begin
                                    func_id[23:16] <= rx_buffer[rx_tail]; // 次高字节
                                end
                                else if (rx_byte_cnt == 8'h02) begin
                                    func_id[15:8] <= rx_buffer[rx_tail];  // 次低字节
                                end
                                else if (rx_byte_cnt == 8'h03) begin
                                    func_id[7:0] <= rx_buffer[rx_tail];   // �?低字�?
                                end
                            end
                            
                            SERVICE_TYPE_TIME: begin
                                // 时间广播 - UTC时间 (6字节)
                                if (rx_byte_cnt == 8'h00) begin
                                    utc_second[31:24] <= rx_buffer[rx_tail]; // 秒计数最高字�?
                                end
                                else if (rx_byte_cnt == 8'h01) begin
                                    utc_second[23:16] <= rx_buffer[rx_tail]; // 秒计数次高字�?
                                end
                                else if (rx_byte_cnt == 8'h02) begin
                                    utc_second[15:8] <= rx_buffer[rx_tail];  // 秒计数次低字�?
                                end
                                else if (rx_byte_cnt == 8'h03) begin
                                    utc_second[7:0] <= rx_buffer[rx_tail];   // 秒计数最低字�?
                                end
                                else if (rx_byte_cnt == 8'h04) begin
                                    utc_msecond[15:8] <= rx_buffer[rx_tail]; // 毫秒计数高字�?
                                end
                                else if (rx_byte_cnt == 8'h05) begin
                                    utc_msecond[7:0] <= rx_buffer[rx_tail];  // 毫秒计数低字�?
                                end
                            end
                            
                            default: begin
                                // 其他服务类型，仅累加校验�?
                            end
                        endcase
                        
                        // 更新校验�?
                        checksum <= checksum + rx_buffer[rx_tail];
                        
                        // 更新计数和指�?
                        rx_byte_cnt <= rx_byte_cnt + 1'b1;
                        rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                        
                        // �?查是否接收完�?有数据字�?
                        if (rx_byte_cnt == data_length - 16'h0003) begin
                            rx_state <= RX_CHECK_1;
                        end
                    end
                end
                
                RX_CHECK_1: begin
                    if (rx_head != rx_tail) begin
                        // 收到校验和高字节
                        if (rx_buffer[rx_tail] == checksum[15:8]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_CHECK_2;
                        end
                        else begin
                            cmd_check_err_flag <= STATUS_ERROR;
                            rx_state <= RX_IDLE;
                        end
                    end
                end
                
                RX_CHECK_2: begin
                    if (rx_head != rx_tail) begin
                        // 收到校验和低字节
                        if (rx_buffer[rx_tail] == checksum[7:0]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_TAIL_1;
                        end
                        else begin
                            // 校验和错�?
                            cmd_check_err_flag <= STATUS_ERROR;
                            rx_state <= RX_IDLE;
                        end
                    end
                end
                
                RX_TAIL_1: begin
                    if (rx_head != rx_tail) begin
                        if (rx_buffer[rx_tail] == FRAME_TAIL[15:8]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_TAIL_2;
                        end
                        else begin
                            // 帧尾错误
                            rx_state <= RX_IDLE;
                        end
                    end
                end
                
                RX_TAIL_2: begin
                    if (rx_head != rx_tail) begin
                        if (rx_buffer[rx_tail] == FRAME_TAIL[7:0]) begin
                            rx_tail <= (rx_tail + 1'b1) & RX_BUFFER_MASK;
                            rx_state <= RX_PROCESS;
                        end
                        else begin
                            // 帧尾错误
                            rx_state <= RX_IDLE;
                        end
                    end
                end
                
                RX_PROCESS: begin
                    // 处理完整接收到的命令
                    process_command();
                    rx_state <= RX_IDLE;
                end
                
                default: begin
                    rx_state <= RX_IDLE;
                end
            endcase
        end
    end
    
    // ================== 命令处理任务 ==================
    task process_command;
    begin
        // �?查服务类型和功能标识
        case (cmd_service_type)
            SERVICE_TYPE_TELEMETRY_REQ: begin
                // 遥测请求指令 (0x01)
                if (func_id[15:0] == TELEMETRY_ID) begin
                    // 遥测请求标识 (0x2201)
                    telemetry_req <= 1'b1;
                    telemetry_req_cnt <= telemetry_req_cnt + 1'b1;
                    cmd_exec_cnt <= cmd_exec_cnt + 1'b1;
                    
                    // 重置通信异常标志和超时计数器
                    comm_err_flag <= STATUS_NORMAL;
                    timeout_counter <= 32'd0;
                end
            end
            
            SERVICE_TYPE_CMD: begin
                // 微振动控制命�? (0x08)
                case (func_id)
                    DETAIL_COLLECT_ID: begin
                        // 微振动详查开始采�? (0x146F5555)
                        work_mode <= DETAIL_MODE;
                        cmd_exec_cnt <= cmd_exec_cnt + 1'b1;
                    end
                    
                    INSPECT_COLLECT_ID: begin
                        // 微振动巡查开始采�? (0x146FAAAA)
                        work_mode <= INSPECT_MODE;
                        cmd_exec_cnt <= cmd_exec_cnt + 1'b1;
                    end
                    
                    STOP_COLLECT_ID: begin
                        // 微振动停止采�? (0x146FBBBB)
                        work_mode <= IDLE_MODE;
                        cmd_exec_cnt <= cmd_exec_cnt + 1'b1;
                    end
                    
                    START_SEND_ID: begin
                        // �?始发送微振动数据 (0x146FCCCC)
                        work_mode <= IDLE_MODE;
                        cmd_exec_cnt <= cmd_exec_cnt + 1'b1;
                    end
                    
                    STOP_SEND_ID: begin
                        // 停止发�?�微振动数据 (0x146FDDDD)
                        work_mode <= IDLE_MODE;
                        cmd_exec_cnt <= cmd_exec_cnt + 1'b1;
                    end
                    
                    default: begin
                        // 未知命令ID
                        cmd_exec_err_flag <= STATUS_ERROR;
                    end
                endcase
            end
            
            SERVICE_TYPE_TIME: begin
                // 时间广播 (0x0E) - UTC时间已在RX_DATA状�?�更�?
                cmd_exec_cnt <= cmd_exec_cnt + 1'b1;
            end
            
            default: begin
                // 未知服务类型
                cmd_exec_err_flag <= STATUS_ERROR;
            end
        endcase
    end
    endtask

endmodule 