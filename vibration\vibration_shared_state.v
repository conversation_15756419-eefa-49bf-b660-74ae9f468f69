module vibration_shared_state (
    input wire clk,                // 系统时钟
    input wire rst_n,              // 复位信号，低电平有效

    // 状态输出
    output reg [7:0] work_mode,          // 工作模式
    output reg [7:0] comm_err_flag,      // 通信异常标志
    output reg [7:0] cmd_check_err_flag, // 指令校验异常标志
    output reg [7:0] telemetry_req_cnt,  // 遥测请求计数
    output reg [7:0] cmd_recv_cnt,       // 接收指令计数
    output reg [7:0] cmd_exec_cnt,       // 成功执行指令计数
    output reg [7:0] uart_reset_cnt,     // 异步串口复位计数
    output reg [31:0] utc_second,        // UTC秒计数
    output reg [15:0] utc_msecond,       // UTC毫秒值
    output reg [7:0] cmd_exec_err_flag   // 指令执行异常标志
);

    // ================== 常量定义 ==================
    // 工作模式常量
    localparam IDLE_MODE     = 8'h00;  // 空闲/停止模式
    localparam DETAIL_MODE   = 8'h55;  // 详查模式 
    localparam INSPECT_MODE  = 8'hAA;  // 巡查模式
    
    // 状态标志常量
    localparam STATUS_NORMAL = 8'h00;  // 正常状态
    localparam STATUS_ERROR  = 8'h11;  // 异常状态
    
    // ================== 状态初始化 ==================
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            // 复位时初始化所有状态
            work_mode <= IDLE_MODE;
            comm_err_flag <= STATUS_NORMAL;
            cmd_check_err_flag <= STATUS_NORMAL;
            cmd_exec_err_flag <= STATUS_NORMAL; // 初始化指令执行异常标志
            telemetry_req_cnt <= 8'h00;
            cmd_recv_cnt <= 8'h00;
            cmd_exec_cnt <= 8'h00;
            uart_reset_cnt <= 8'h00;
            utc_second <= 32'h00000000;
            utc_msecond <= 16'h0000;
        end
    end



endmodule 