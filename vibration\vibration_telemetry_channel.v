module vibration_telemetry_channel (
    input wire clk,                // 系统时钟
    input wire rst_n,              // 复位信号，低电平有效
    input wire [7:0] baud_val,     // 波特率配置
    
    // 遥测请求触发
    input wire telemetry_req,      // 遥测请求触发信号
    
    // 系统状态
    input wire [7:0] work_mode,          // 工作模式
    input wire [7:0] comm_err_flag,      // 通信异常标志
    input wire [7:0] cmd_check_err_flag, // 指令校验异常标志
    input wire [7:0] cmd_exec_err_flag,  // 指令执行异常标志
    input wire [7:0] telemetry_req_cnt,  // 遥测请求计数
    input wire [7:0] cmd_recv_cnt,       // 接收指令计数
    input wire [7:0] cmd_exec_cnt,       // 成功执行指令计数
    input wire [7:0] uart_reset_cnt,     // 异步串口复位计数
    input wire [31:0] utc_second,        // UTC秒计数
    input wire [15:0] utc_msecond,       // UTC毫秒值
    // UART输出
    output wire tx                 // RS422发送线
);

    // ================== 内部参数定义 ==================
    // 遥测数据包定义，固定为234字节 (帧头2 + 长度2 + 服务类型1 + 服务状态1 + 数据块224 + 校验和2 + 帧尾2)
    localparam TELEMETRY_DATA_SIZE = 234;
    localparam VIBRATION_DATA_SIZE = 212; // 微振动数据块大小
    
    // 遥测数据包字段位置定义
    localparam FRAME_HEAD_POS      = 0;  // 帧头位置(2字节)
    localparam DATA_LEN_POS        = 2;  // 数据长度位置(2字节)
    localparam SERVICE_TYPE_POS    = 4;  // 服务类型位置(1字节)
    localparam SERVICE_STATUS_POS  = 5;  // 服务状态位置(1字节)
    
    // 数据块起始位置 (从第6个字节开始)
    localparam DATA_BLOCK_POS      = 6;  // 数据块起始位置
    
    // 数据块内部结构
    localparam TM_REQ_CNT_POS      = 6;  // 遥测请求计数位置(1字节)
    localparam CMD_RECV_CNT_POS    = 7;  // 接收指令计数位置(1字节)
    localparam CMD_EXEC_CNT_POS    = 8;  // 成功执行指令计数位置(1字节)
    localparam UART_RESET_CNT_POS  = 9;  // 422接口复位计数位置(1字节)
    localparam UTC_SECOND_POS      = 10; // UTC秒计数位置(4字节)
    localparam WORK_MODE_POS       = 14; // 工作模式位置(1字节)
    localparam COMM_ERR_POS        = 15; // 通信异常位置(1字节)
    localparam CMD_ERR_POS         = 16; // 指令校验异常位置(1字节)
    localparam CMD_EXEC_ERR_POS    = 17; // 指令执行异常位置(1字节)
    localparam VIBRATION_DATA_POS  = 18; // 微振动数据块起始位置(211字节)
    
    // 校验和和帧尾位置
    localparam CHECKSUM_POS        = 230; // 校验码位置(2字节)
    localparam FRAME_TAIL_POS      = 232; // 帧尾位置(2字节)
    
    // 固定参数值
    localparam [15:0] FRAME_HEAD   = 16'hEB90; // 帧头
    localparam [15:0] DATA_LEN     = 16'h00E2; // 数据长度(224+2)
    localparam [7:0] SERVICE_TYPE  = 8'h01;    // 服务类型(遥测信息)
    localparam [7:0] SERVICE_STATUS = 8'h01;   // 成功状态
    localparam [15:0] FRAME_TAIL   = 16'h09D7; // 帧尾
    
    // 工作模式常量
    localparam IDLE_MODE     = 8'h00;  // 空闲/停止模式
    localparam DETAIL_MODE   = 8'h55;  // 详查模式 
    localparam INSPECT_MODE  = 8'hAA;  // 巡查模式
    
    // UART通信参数
    reg cs_n;                      // 片选信号
    reg we_n;                      // 写使能信号
    reg [7:0] data_in;             // 待写数据
    wire txrdy;                    // 发送就绪标志
    reg txrdy_prev;
    wire txrdy_rf;
    assign txrdy_rf = txrdy & ~txrdy_prev;
    
    // ================== 内部寄存器定义 ==================
    reg [2:0] state;               // 发送状态机状态
    reg [15:0] frame_counter;      // 帧计数器
    reg [7:0] send_index;          // 发送索引
    
    // 遥测数据包缓冲区
    reg [7:0] telemetry_data [0:TELEMETRY_DATA_SIZE-1];
    
    // 微振动数据缓冲区
    reg [7:0] vibration_data [0:VIBRATION_DATA_SIZE-1];
    
    // 发送状态机状态定义
    localparam IDLE      = 3'b000; // 空闲状态
    localparam BUILD_PKG = 3'b001; // 构建数据包
    localparam SEND_PKG  = 3'b010; // 发送数据包
    localparam WAIT_DONE = 3'b011; // 等待发送完成
    
    // ================== UART实例化 ==================
    // 使用COREUART模块发送遥测数据
    COREUART uart_inst (
        .clk(clk),
        .rst_n(rst_n),
        
        .cs_n(cs_n),
        .baud_val(baud_val),
        .bit8(1'b1),        // 8位数据位
        .parity_en(1'b1),   // 使能奇偶校验
        .odd_n_even(1'b1),  // 奇校验
        
        .we_n(we_n),        // 写使能信号
        .data_in(data_in),  // 待写数据
        .txrdy(txrdy),      // 发送就绪标志
        .tx(tx),            // 发送数据线
        
        .re_n(1'b1),        // 禁用接收功能
        .rx(),              // 接收线不使用
        .rxrdy(),
        .data_out(),
        .parity_err(),
        .overflow()
    );
    //  寄存器更新逻辑，保存txrdy的上一个状态
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n)
            txrdy_prev <= 1'b0;
        else
            txrdy_prev <= txrdy;
    end
    // ================== 遥测数据包构建和发送状态机 ==================
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            // 复位初始化
            state <= IDLE;
            frame_counter <= 16'd0;
            send_index <= 8'd0;
            cs_n <= 1'b0;
            we_n <= 1'b1;
            data_in <= 8'h00;
        end
        else begin
            // 状态机
            case (state)
                IDLE: begin
                    // 等待遥测请求触发
                    if (telemetry_req) begin
                        state <= BUILD_PKG;
                    end
                    else begin
                        state <= IDLE;
                    end
                end
                
                BUILD_PKG: begin
                    // 构建遥测数据包
                    build_telemetry_package();
                    
                    // 更新帧计数
                    frame_counter <= frame_counter + 1'b1;
                    
                    // 准备发送
                    send_index <= 8'd0;
                    state <= SEND_PKG;
                end
                
                SEND_PKG: begin
                    // 发送遥测数据包
                    if (send_index < TELEMETRY_DATA_SIZE) begin
                        if (send_index == 8'd0) begin
                            if (txrdy) begin
                                // UART就绪，发送下一个字节
                                we_n <= 1'b0;
                                data_in <= telemetry_data[send_index];
                                send_index <= send_index + 1'b1;
                            end
                            else begin
                            // UART忙，等待就绪
                            we_n <= 1'b1;
                            end
                        end
                        else begin
                            if (txrdy_rf) begin
                                we_n <= 1'b0;
                                data_in <= telemetry_data[send_index];
                                send_index <= send_index + 1'b1;
                            end
                            else begin
                                we_n <= 1'b1;
                            end
                        end
                    end
                    else begin
                        // 所有数据已发送
                        we_n <= 1'b1;
                        state <= WAIT_DONE;
                    end
                end
                
                WAIT_DONE: begin
                    // 等待传输完成后回到空闲状态
                    state <= IDLE;
                end
                
                default: begin
                    state <= IDLE;
                end
            endcase
        end
    end
    
    // ================== 遥测数据包构建任务 ==================
    task build_telemetry_package;
        integer i;
        reg [15:0] checksum;
    begin
        // 首先生成模拟的微振动数据
        for (i = 0; i < VIBRATION_DATA_SIZE; i = i + 1) begin
         vibration_data[i] = 8'h00;
        end
        // 1. 帧头 (2字节)
        telemetry_data[FRAME_HEAD_POS] = FRAME_HEAD[15:8];
        telemetry_data[FRAME_HEAD_POS+1] = FRAME_HEAD[7:0];
        
        // 2. 数据长度 (2字节)
        telemetry_data[DATA_LEN_POS] = DATA_LEN[15:8];
        telemetry_data[DATA_LEN_POS+1] = DATA_LEN[7:0];
        
        // 3. 服务类型 (1字节)
        telemetry_data[SERVICE_TYPE_POS] = SERVICE_TYPE;
        
        // 4. 服务状态 (1字节)
        telemetry_data[SERVICE_STATUS_POS] = SERVICE_STATUS;
        
        // 5. 数据块 (224字节)
        // 5.1 计数器状态
        telemetry_data[TM_REQ_CNT_POS] = telemetry_req_cnt;   // 遥测请求计数
        telemetry_data[CMD_RECV_CNT_POS] = cmd_recv_cnt;      // 接收指令计数
        telemetry_data[CMD_EXEC_CNT_POS] = cmd_exec_cnt;      // 成功执行指令计数
        telemetry_data[UART_RESET_CNT_POS] = uart_reset_cnt;  // 422接口复位计数
        
        // 5.2 UTC时间
        telemetry_data[UTC_SECOND_POS] = utc_second[31:24];   // 秒计数最高字节
        telemetry_data[UTC_SECOND_POS+1] = utc_second[23:16]; // 秒计数次高字节
        telemetry_data[UTC_SECOND_POS+2] = utc_second[15:8];  // 秒计数次低字节
        telemetry_data[UTC_SECOND_POS+3] = utc_second[7:0];   // 秒计数最低字节
        
        // 5.3 状态标志
        telemetry_data[WORK_MODE_POS] = work_mode;            // 工作模式
        telemetry_data[COMM_ERR_POS] = comm_err_flag;         // 通信异常标志
        telemetry_data[CMD_ERR_POS] = cmd_check_err_flag;     // 指令校验异常标志
        telemetry_data[CMD_EXEC_ERR_POS] = cmd_exec_err_flag; // 指令执行异常标志
        
        // 5.4 微振动数据 (212字节)
        for (i = 0; i < VIBRATION_DATA_SIZE; i = i + 1) begin
            telemetry_data[VIBRATION_DATA_POS+i] = vibration_data[i];
        end
        
        // 6. 计算校验和 (数据长度+服务类型+服务状态+数据块的累加和)
        checksum = 16'h0000;
        
        // 数据长度
        checksum = checksum + telemetry_data[DATA_LEN_POS];
        checksum = checksum + telemetry_data[DATA_LEN_POS+1];
        
        // 服务类型和状态
        checksum = checksum + telemetry_data[SERVICE_TYPE_POS];
        checksum = checksum + telemetry_data[SERVICE_STATUS_POS];
        
        // 数据块 (224字节)
        for (i = DATA_BLOCK_POS; i < CHECKSUM_POS; i = i + 1) begin
            checksum = checksum + telemetry_data[i];
        end
        
        // 7. 校验和 (2字节)
        telemetry_data[CHECKSUM_POS] = checksum[15:8];
        telemetry_data[CHECKSUM_POS+1] = checksum[7:0];
        
        // 8. 帧尾 (2字节)
        telemetry_data[FRAME_TAIL_POS] = FRAME_TAIL[15:8];
        telemetry_data[FRAME_TAIL_POS+1] = FRAME_TAIL[7:0];
    end
    endtask
    
    // ================== 微振动数据生成任务 ==================
    task generate_vibration_data;
        integer i;
        reg [31:0] data_value;
    begin
        // 根据工作模式生成不同特性的数据
        case (work_mode)
            DETAIL_MODE: begin
                // 详查模式 - 生成高振幅数据 (0x55)
                for (i = 0; i < 51; i = i + 1) begin
                    // 每个通道4字节，总共51个通道
                    data_value = 32'h55000000 + (i << 16) + frame_counter;
                    vibration_data[i*4]   = data_value[31:24];
                    vibration_data[i*4+1] = data_value[23:16];
                    vibration_data[i*4+2] = data_value[15:8];
                    vibration_data[i*4+3] = data_value[7:0];
                end
            end
            
            INSPECT_MODE: begin
                // 巡查模式 - 生成中振幅数据 (0xAA)
                for (i = 0; i < 51; i = i + 1) begin
                    data_value = 32'hAA000000 + (i << 16) + frame_counter;
                    vibration_data[i*4]   = data_value[31:24];
                    vibration_data[i*4+1] = data_value[23:16];
                    vibration_data[i*4+2] = data_value[15:8];
                    vibration_data[i*4+3] = data_value[7:0];
                end
            end
            
            IDLE_MODE: begin
                // 空闲模式 - 生成低振幅数据 (接近零)
                for (i = 0; i < 51; i = i + 1) begin
                    data_value = 32'h00000000 + i;
                    vibration_data[i*4]   = data_value[31:24];
                    vibration_data[i*4+1] = data_value[23:16];
                    vibration_data[i*4+2] = data_value[15:8];
                    vibration_data[i*4+3] = data_value[7:0];
                end
            end
            
            default: begin
                // 未知模式，全零数据
                for (i = 0; i < VIBRATION_DATA_SIZE; i = i + 1) begin
                    vibration_data[i] = 8'h00;
                end
            end
        endcase
        
        // 填充预留的8字节
        for (i = 204; i < 212; i = i + 1) begin
            vibration_data[i] = 8'h55;
        end
    end
    endtask

endmodule 