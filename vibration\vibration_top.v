module vibration_top (
    input wire clk,                // 系统时钟 (40MHz)
    input wire rst_n,              // 复位信号，低电平有效
    input wire [7:0] baud_val,     // 波特率配?
    
    // RS422接口
    input wire rx,                  // 指令接收?
    output wire tx                  // 遥测数据发?线
);

    // ================== 内部连线声明 ==================
    // 状?变?
    wire [7:0] work_mode;          // 工作模式 (0x55:详查, 0xAA:巡查, 0x00:停止)    
    
    // 时间信息
    wire [31:0] utc_second;        // UTC秒计?
    wire [15:0] utc_msecond;       // UTC毫秒?
    wire [7:0] comm_err_flag;
	 wire [7:0] cmd_check_err_flag;
	 wire [7:0] cmd_exec_err_flag;
	 wire [7:0] telemetry_req_cnt;
	 wire [7:0] cmd_recv_cnt;
	 wire [7:0] cmd_exec_cnt;
	 wire [7:0] uart_reset_cnt;
    // 指令处理和遥测请?
    wire telemetry_req;            // 遥测请求触发信号
    
    // ================== 模块实例? ================== 
        
    // 命令接收和处理模?
    vibration_cmd_channel cmd_inst (
        .clk(clk),
        .rst_n(rst_n),
        .baud_val(baud_val),
        // UART接收
        .rx(rx),
        // 状?更?
        .work_mode(work_mode),
        .comm_err_flag(comm_err_flag),
        .cmd_check_err_flag(cmd_check_err_flag),
        .cmd_exec_err_flag(cmd_exec_err_flag),
        .telemetry_req_cnt(telemetry_req_cnt),
        .cmd_recv_cnt(cmd_recv_cnt),
        .cmd_exec_cnt(cmd_exec_cnt),
        .uart_reset_cnt(uart_reset_cnt),
        .utc_second(utc_second),
        .utc_msecond(utc_msecond),
        
        // 遥测请求
        .telemetry_req(telemetry_req)
    );
    
    // 遥测数据发?模?
    vibration_telemetry_channel telemetry_inst (
        .clk(clk),
        .rst_n(rst_n),
        .baud_val(baud_val),
        
        // 遥测请求触发
        .telemetry_req(telemetry_req),
        
        // 系统状??
        .work_mode(work_mode),
        .comm_err_flag(comm_err_flag),
        .cmd_check_err_flag(cmd_check_err_flag),
        .cmd_exec_err_flag(cmd_exec_err_flag),
        .telemetry_req_cnt(telemetry_req_cnt),
        .cmd_recv_cnt(cmd_recv_cnt),
        .cmd_exec_cnt(cmd_exec_cnt),
        .uart_reset_cnt(uart_reset_cnt),
        .utc_second(utc_second),
        .utc_msecond(utc_msecond),

        // UART输出
        .tx(tx)
    );

endmodule 